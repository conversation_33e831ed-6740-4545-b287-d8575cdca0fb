version: 2.1

orbs:
  aws-cli: circleci/aws-cli@3.1.5

parameters:
  run-all-builds:
    type: boolean
    default: false
  run-ims-build:
    type: boolean
    default: false
  run-data-build:
    type: boolean
    default: false
  run-wms-build:
    type: boolean
    default: false
  run-user-build:
    type: boolean
    default: false
  run-partner-insight-build:
    type: boolean
    default: false

executors:
  default-executor:
    docker:
      - image: cimg/base:stable
    resource_class: small
  gradle-executor:
    docker:
      - image: cimg/openjdk:21.0
    working_directory: ~/repo
    resource_class: small
  infra-executor:
    docker:
      - image: $DOCKER_REPO_URL/mercaso/circleci-infrastructure:latest
    working_directory: ~/repo
    resource_class: small
  playwright-executor:
    docker:
      - image: ${DOCKER_REPO_URL}/mercaso/auto-test:latest
    resource_class: small

commands:
  check_preconditions:
    parameters:
      application:
        type: string
    steps:
      - run:
          name: Check preconditions
          command: |
            echo "Checking if need to proceed..."
            echo "Application: << parameters.application >>"
            echo "run-all-builds: << pipeline.parameters.run-all-builds >>"
            echo "run-ims-build: << pipeline.parameters.run-ims-build >>"
            echo "run-wms-build: << pipeline.parameters.run-wms-build >>"
            echo "run-data-build: << pipeline.parameters.run-data-build >>"
            echo "run-user-build: << pipeline.parameters.run-user-build >>"
            echo "run-partner-insight-build: << pipeline.parameters.run-partner-insight-build >>"
            
            if [[ "<< pipeline.parameters.run-all-builds >>" = "true" ]]; then
              echo "Need to proceed all apps..."
              need_build=true
            elif [[ "<< pipeline.parameters.run-ims-build >>" = "true" && "<< parameters.application >>" = "item-management-service" ]]; then
              echo "Need to proceed item-management-service..."
              need_build=true
            elif [[ "<< pipeline.parameters.run-wms-build >>" = "true" && "<< parameters.application >>" = "warehouse-management-service" ]]; then
              echo "Need to proceed warehouse-management-service..."
              need_build=true
            elif [[ "<< pipeline.parameters.run-data-build >>" = "true" && "<< parameters.application >>" = "data-service" ]]; then
              echo "Need to proceed data-service..."
              need_build=true           
            elif [[ "<< pipeline.parameters.run-user-build >>" = "true" && "<< parameters.application >>" = "user-service" ]]; then
              echo "Need to proceed user-service..."
              need_build=true
            elif [[ "<< pipeline.parameters.run-partner-insight-build >>" = "true" && "<< parameters.application >>" = "partner-insight" ]]; then
              echo "Need to proceed partner-insight..."
              need_build=true
            else
              echo "Skip since no app needs to be proceed..."
              need_build=false
            fi
            
            if $need_build; then
              echo "Proceeding..."
            else
              echo "Skip since this is not the modified app..."
              circleci-agent step halt
            fi

jobs:
  trigger_cursor_review:
    executor: default-executor
    steps:
      - run:
          name: Trigger Cursor review by posting bugbot run comment
          command: |
            if [ -n "$CIRCLE_PULL_REQUEST" ]; then
              # Extract PR number from URL
              PR_NUMBER=$(echo $CIRCLE_PULL_REQUEST | sed 's/.*\/pull\/\([0-9]*\)/\1/')
              
              # Post comment using GitHub API to trigger Cursor review
              curl -X POST \
                -H "Authorization: token $CURSOR_REVIEW_GITHUB_TOKEN" \
                -H "Accept: application/vnd.github.v3+json" \
                -H "Content-Type: application/json" \
                -d '{"body": "bugbot run"}' \
                "https://api.github.com/repos/$CIRCLE_PROJECT_USERNAME/$CIRCLE_PROJECT_REPONAME/issues/$PR_NUMBER/comments"
              
              echo "Triggered Cursor review with 'bugbot run' comment on PR #$PR_NUMBER"
            else
              echo "No PR detected, skipping Cursor review trigger"
            fi

  pre_build:
    executor: default-executor
    steps:
      - run:
          name: Display detected changes
          command: |
            echo "Detected changes:"
            echo "------------------"
            echo "run-all-builds: << pipeline.parameters.run-all-builds >>"
            echo "run-ims-build: << pipeline.parameters.run-ims-build >>"
            echo "run-wms-build: << pipeline.parameters.run-wms-build >>"
            echo "run-data-build: << pipeline.parameters.run-data-build >>"
            echo "run-user-build: << pipeline.parameters.run-user-build >>"
            echo "run-partner-insight-build: << pipeline.parameters.run-partner-insight-build >>"
  build:
    environment:
      AWS_REGION: "us-west-2"
    parameters:
      application:
        type: string
        default: ""
      environment:
        type: string
        default: ""
      role-arn:
        type: string
    executor: gradle-executor
    steps:
      - check_preconditions:
          application: << parameters.application >>
      - checkout
      - setup_remote_docker
      - restore_cache:
          keys:
            - gradle-cache-{{ checksum "gradle/wrapper/gradle-wrapper.properties" }}
            - gradle-cache-
      - run:
          name: Build and test project
          command: |
            app=<< parameters.application >>
            if [[ ${CIRCLE_BRANCH} = hotfix* ]] || [[ ${CIRCLE_BRANCH} = debug* ]]; then
              echo "Hotfix branch detected, skip building..."
              gradle -p /home/<USER>/repo/services/${app} -c /home/<USER>/repo/settings.gradle assemble
              exit 0
            fi
            
            docker-compose up -d --quiet-pull base-services
            docker-compose up --quiet-pull wait_for_dependencies
            
            echo "Building and testing ${app}..."
            
            docker create -v /home/<USER>/bin/true
            docker cp ~/.gradle mercasodata:/home/<USER>/.gradle
            docker cp ~/repo mercasodata:/home/<USER>/repo
            mkdir -p /home/<USER>/.sonar/cache
            docker cp ~/.sonar mercasodata:/home/<USER>/.sonar
            
            docker run --network repo_default --volumes-from mercasodata \
              --workdir /home/<USER>/repo \
              -e SWAGGER_OUTPUT_FILE=/home/<USER>/repo/services/${app}/swagger.json \
              -e NEXUS_USERNAME=$NEXUS_USERNAME \
              -e NEXUS_PASSWORD=$NEXUS_PASSWORD \
              -e NEXUS_ORG_HOSTED_URL=$NEXUS_ORG_HOSTED_URL \
              -e NEXUS_URL=$NEXUS_URL \
              -e SONAR_TOKEN=$SONAR_TOKEN \
              cimg/openjdk:21.0 \
              gradle -p /home/<USER>/repo/services/${app} -c /home/<USER>/repo/settings.gradle -PSPRING_PROFILES_ACTIVE=integration build sonar || exit_code=$?
            
            echo "Copying test results..."
            docker cp mercasodata:/home/<USER>/.gradle/. ~/.gradle
            docker cp mercasodata:/home/<USER>/repo/. ~/repo
            
            # If the build or tests fail, capture the exit code
            if [[ "$exit_code" -ne 0 ]]; then
              echo "Tests failed, exit code: $exit_code"
              exit 1  # Exit CI process with failure status
            fi

            docker-compose down -v
      - save_cache:
          paths:
            - ~/.gradle/caches
            - ~/.gradle/wrapper
          key: gradle-cache-{{ checksum "gradle/wrapper/gradle-wrapper.properties" }}
      - run:
          name: Save test results
          command: |
            app=<< parameters.application >>
            mkdir -p ~/test-results/${app}/junit/
            find . -type f -name "*.xml" -path "*/${app}/build/test-results/*" -exec cp {} ~/test-results/${app}/junit/ \;
          when: always
      - store_test_results:
          path: ~/test-results/<< parameters.application >>/junit/
      - run:
          name: Publish Java client to repo
          command: |
            app=<< parameters.application >>
            if [[ ${CIRCLE_BRANCH} = master ]] && [[ -f "/home/<USER>/repo/services/${app}/swagger.json" ]]; then
              echo "Generate client source for ${app}..."
              docker run --volumes-from mercasodata --workdir /home/<USER>/repo \
              openapitools/openapi-generator-cli:latest \
              generate -c /home/<USER>/repo/services/${app}/swagger-codegen-config.json -g java \
              -i /home/<USER>/repo/services/${app}/swagger.json -o /home/<USER>/repo/services/${app}/swaggerClient \
              --additional-properties=springVersion=6.1.8,build.gradle=true --skip-validate-spec
            
              docker cp mercasodata:/home/<USER>/repo/services/${app}/swaggerClient ~/repo/services/${app}/swaggerClient
            
              cd ~/repo/services/${app}/swaggerClient
              printf "\nversion = '1.0.%s'\n" "${CIRCLE_BUILD_NUM}" >> build.gradle            
            
              # Insert the `publishing` task setting from our env var secret
              gradle_publishing_task=$(echo $GRADLE_PUBLISHING_BASE64 | base64 --decode)
              printf "\n%s\n" "$gradle_publishing_task" >> build.gradle
            
              sed -i 's/spring_web_version = "[^"]*"/spring_web_version = "6.1.8"/g' build.gradle
              sed -i 's/JavaVersion.VERSION_1_8/JavaVersion.VERSION_21/g' build.gradle
            
              gradle publish
            else
              echo "Skip since the branch is not master branch..."
            fi
      - run:
          name: Build Docker image
          command: |
            if [[ ${CIRCLE_BRANCH} = master ]] || [[ ${CIRCLE_BRANCH} = hotfix* ]] || [[ ${CIRCLE_BRANCH} = debug* ]]; then
              app=<< parameters.application >>
              docker build --build-arg APPLICATION_NAME=${app} --build-arg APP_PORT=8080 -t ${app}:$CIRCLE_SHA1 ~/repo/services/${app}
              docker tag ${app}:$CIRCLE_SHA1 $DOCKER_REPO_URL/mercaso/${app}:$CIRCLE_SHA1
            fi
      - aws-cli/setup:
          role-arn: << parameters.role-arn >>
          aws-region: AWS_REGION
      - run:
          name: Push container
          command: |
            if [[ ${CIRCLE_BRANCH} = master ]] || [[ ${CIRCLE_BRANCH} = hotfix* ]] || [[ ${CIRCLE_BRANCH} = debug* ]]; then
              app=<< parameters.application >>
              echo "Logging in to AWS ECR..."
              aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin $DOCKER_REPO_URL
              docker push $DOCKER_REPO_URL/mercaso/${app}:$CIRCLE_SHA1
            fi

  deploy:
    environment:
      AWS_REGION: us-west-2
    parameters:
      application:
        type: string
        default: ""
      environment:
        type: string
      role-arn:
        type: string
    executor: infra-executor
    steps:
      - check_preconditions:
          application: << parameters.application >>
      - checkout
      - aws-cli/setup:
          role-arn: << parameters.role-arn >>
          aws-region: AWS_REGION
      - run:
          name: Apply k8s deployment
          command: |
            app=<< parameters.application >>
            env=<< parameters.environment >>
            
            echo ${app}-${env}
            
            if [[ "$app" == "partner-insight" && ("$env" == "dev" || "$env" == "sat") ]]; then
              echo "Skip deployment: ${app} does not deploy in ${env} environment."
              exit 0
            fi
            
            limit_memory_values='{
              "dev": {
                "data-service": "1280Mi",
                "item-management-service": "1280Mi",
                "warehouse-management-service": "1280Mi",
                "user-service": "512Mi"
              },
              "prod": {
                "data-service": "1280Mi",
                "item-management-service": "2Gi",
                "warehouse-management-service": "3Gi",
                "user-service": "512Mi",
                "partner-insight": "512Mi"
              },
              "sat": {
                  "data-service": "1280Mi",
                  "item-management-service": "1280Mi",
                  "warehouse-management-service": "1280Mi",
                  "user-service": "512Mi"
              }
            }'
            
            limit_memory_value=$(echo $limit_memory_values | jq -r ".[\"${env}\"][\"${app}\"]")
            echo "Limit Memory Value: $limit_memory_value"
            
            mkdir -p ~/.kube/
            echo $KUBE_CONFIG | base64 -d > ~/.kube/config
            
            export REPLICAS=2
            export SPRING_PROFILES_ACTIVE=${env}
            export IMAGE_VERSION=$CIRCLE_SHA1
            export REQUESTS_MEMORY="256Mi"
            export REQUESTS_CPU="100m"
            export LIMITS_MEMORY=${limit_memory_value}
            export LIMITS_CPU="1000m"
            export SKYWALKING_OAP_HOST=${SKYWALKING_OAP_HOST}
            export SKYWALKING_EXTRA_CONFIG=${SKYWALKING_EXTRA_CONFIG}
            export APPLICATION_NAME=${app}
            
            envsubst < ~/repo/services/${app}/k8s/app.yaml | kubectl apply --context=eks.us-west-2.${env} -f -
            
            kubectl rollout status deployment ${app}
      - run:
          name: Create release tag
          command: |
            env=<< parameters.environment >>
            if [[ "${env}" == "prod" ]]; then
              app=<< parameters.application >>
              chmod 777 ~/repo/.circleci/create_release_tag.sh
              ~/repo/.circleci/create_release_tag.sh ${env} ${app}
            fi
      - run:
          name: Configure deploy markers
          command: |
            app=<< parameters.application >>
            env=<< parameters.environment >>
            timestamp=$(date +%Y%m%d%H%M)
            version_tag="${env}-${app}-${timestamp}"
            echo "Configuring deploy markers for ${app} in ${env} environment..."
            circleci run release log --environment-name=${env} --component-name=${app} --target-version=${version_tag}
  ui_test:
    parameters:
      application:
        type: string
      environment:
        type: string
    executor: playwright-executor
    steps:
      - check_preconditions:
          application: << parameters.application >>
      - run:
          name: Run Testing
          environment:
            ENV: << parameters.environment >>
          command: |
            cd apps/mercaso-testing
            case "<< parameters.application >>" in
              "item-management-service")
                pnpm run e2e:headless --grep @IMS
                ;;
              "warehouse-management-service")
                pnpm run e2e:headless --grep @WMS
                ;;
              *)
                echo "No matching application found."
                exit 1
                ;;
            esac
      - store_artifacts:
          path: ./apps/mercaso-testing/playwright-report
          destination: playwright-report

  check_build_status:
    executor: default-executor
    steps:
      - run:
          name: Summarize build statuses
          command: |
            echo "Build Status Summary..."

workflows:
  version: 2
  mercaso-platform-pipeline:
    jobs:
      - trigger_cursor_review:
          name: trigger_cursor_review
          context: mercaso-platform-build
          filters:
            branches:
              ignore:
                - master
      - pre_build:
          name: pre_build
      - build:
          name: build_<< matrix.application >>
          context: mercaso-platform-build
          matrix:
            parameters:
              application: [ 'item-management-service', 'warehouse-management-service', 'data-service', 'user-service', 'partner-insight' ]
              role-arn: [ "arn:aws:iam::767397963667:role/circleci_business" ]
          requires:
            - pre_build
      - deploy:
          name: deploy_<< matrix.application >>_dev
          context:
            - mercaso-platform-build
            - mercaso-platform-dev
          matrix:
            parameters:
              application: [ 'item-management-service', 'warehouse-management-service', 'data-service', 'user-service', 'partner-insight' ]
              environment: [ "dev" ]
              role-arn: [ "arn:aws:iam::767397963667:role/circleci_business" ]
          requires:
            - build_<< matrix.application >>
          filters:
            branches:
              only:
                - master
                - /^hotfix.*/
                - /^debug.*/
      - hold_sat:
          type: approval
          name: hold_<< matrix.application >>_sat
          matrix:
            parameters:
              application: [ 'item-management-service', 'warehouse-management-service', 'data-service', 'user-service', 'partner-insight' ]
          requires:
            - deploy_<< matrix.application >>_dev
          filters:
            branches:
              ignore: /^debug.*/
      - deploy:
          name: deploy_<< matrix.application >>_sat
          context:
            - mercaso-platform-build
            - mercaso-platform-sat
          matrix:
            parameters:
              application: [ 'item-management-service', 'data-service', 'user-service', 'partner-insight' ]
              environment: [ "sat" ]
              role-arn: [ "arn:aws:iam::235494803530:role/circleci_business" ]
          requires:
            - hold_<< matrix.application >>_sat
          filters:
            branches:
              only:
                - master
                - /^hotfix.*/
      - deploy:
          name: deploy_warehouse-management-service_sat
          context:
            - mercaso-platform-build
            - mercaso-platform-sat
          matrix:
            parameters:
              application: [ 'warehouse-management-service' ]
              environment: [ "sat" ]
              role-arn: [ "arn:aws:iam::235494803530:role/circleci_business" ]
          requires:
            - deploy_warehouse-management-service_dev
          filters:
            branches:
              only:
                - master
                - /^hotfix.*/
      - hold_prod:
          type: approval
          name: hold_<< matrix.application >>_prod
          matrix:
            parameters:
              application: [ 'item-management-service', 'warehouse-management-service', 'data-service', 'user-service', 'partner-insight' ]
          requires:
            - deploy_<< matrix.application >>_sat
          filters:
            branches:
              ignore: /^debug.*/
      - deploy:
          name: deploy_<< matrix.application >>_prod
          context:
            - mercaso-platform-build
            - mercaso-platform-prod
          matrix:
            parameters:
              application: [ 'item-management-service', 'warehouse-management-service', 'data-service', 'user-service', 'partner-insight' ]
              environment: [ "prod" ]
              role-arn: [ "arn:aws:iam::471112831519:role/circleci_business" ]
          requires:
            - hold_<< matrix.application >>_prod
      - hold_ui_test:
          type: approval
          name: hold_test_<< matrix.application >>_dev
          matrix:
            parameters:
              application: [ 'item-management-service', 'warehouse-management-service' ]
              environment: [ "dev" ]
          requires:
            - deploy_<< matrix.application >>_dev
      - ui_test:
          name: test_<< matrix.application >>_dev
          context:
            - mercaso-platform-dev
            - mercaso-platform-build
          matrix:
            parameters:
              application: [ 'item-management-service', 'warehouse-management-service' ]
              environment: [ "dev" ]
          requires:
            - hold_test_<< matrix.application >>_dev
      - ui_test:
          name: test_<< matrix.application >>_prod
          context:
            - mercaso-platform-prod
            - mercaso-platform-build
          matrix:
            parameters:
              application: [ 'item-management-service', 'warehouse-management-service' ]
              environment: [ "prod" ]
          requires:
            - deploy_<< matrix.application >>_prod
      - check_build_status:
          name: check_build_status
          requires:
            - build_item-management-service
            - build_warehouse-management-service
            - build_data-service
            - build_user-service
            - build_partner-insight
