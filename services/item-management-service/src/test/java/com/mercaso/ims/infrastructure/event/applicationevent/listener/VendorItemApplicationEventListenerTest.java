package com.mercaso.ims.infrastructure.event.applicationevent.listener;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.application.command.CreateExceptionRecordCommand;
import com.mercaso.ims.application.dto.ExceptionRecordDto;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.VendorItemDto;
import com.mercaso.ims.application.dto.event.VendorItemAmendApplicationEvent;
import com.mercaso.ims.application.dto.payload.VendorItemAmendPayloadDto;
import com.mercaso.ims.application.queryservice.ItemQueryApplicationService;
import com.mercaso.ims.application.service.ExceptionRecordApplicationService;
import com.mercaso.ims.application.service.ItemApplicationService;
import com.mercaso.ims.application.service.ItemPriceApplicationService;
import com.mercaso.ims.application.service.ItemVersionApplicationService;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.service.ItemService;
import com.mercaso.ims.domain.vendor.VendorConstant;
import com.mercaso.ims.domain.vendoritem.VendorItemSpecification;
import com.mercaso.ims.infrastructure.external.finale.FinaleExternalApiAdaptor;
import com.mercaso.ims.utils.exceptionrecord.ExceptionRecordDtoUtil;
import com.mercaso.ims.utils.item.ItemDTOUtil;
import com.mercaso.ims.utils.item.ItemUtil;
import com.mercaso.ims.utils.vendor.VendorItemDtoUtil;
import java.math.BigDecimal;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class VendorItemApplicationEventListenerTest {

    @Mock
    ItemApplicationService itemApplicationService;
    @Mock
    ItemService itemService;
    @Mock
    FeatureFlagsManager featureFlagsManager;
    @Mock
    VendorItemSpecification vendorItemSpecification;
    @Mock
    ExceptionRecordApplicationService exceptionRecordApplicationService;
    @Mock
    FinaleExternalApiAdaptor finaleExternalApiAdaptor;
    @InjectMocks
    VendorItemApplicationEventListener vendorItemApplicationEventListener;
    @Mock
    ItemQueryApplicationService itemQueryApplicationService;
    @Mock
    ItemVersionApplicationService itemVersionApplicationService;
    @Mock
    ItemPriceApplicationService itemPriceApplicationServicel;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testHandleVendorItemAmendApplicationEvent() {

        ItemDto itemDto = ItemDTOUtil.buildItemDTO();
        itemDto.getVendorItemDtos().get(0).setVendorName(VendorConstant.EXOTIC_BLVD);
        ExceptionRecordDto exceptionRecordDto = ExceptionRecordDtoUtil.buildExceptionRecordDto();
        VendorItemDto previous = VendorItemDtoUtil.buildVendorItemDto();
        VendorItemDto current = VendorItemDtoUtil.buildVendorItemDto();
        current.setCost(new BigDecimal(232));
        VendorItemAmendPayloadDto vendorItemAmendPayloadDto = VendorItemAmendPayloadDto.builder()
            .vendorItemId(UUID.randomUUID())
            .previous(previous)
            .current(current)
            .build();
        Item item = ItemUtil.buildItem();
        item.setBackupVendorId(current.getVendorItemId());
        when(itemApplicationService.refreshPrimaryBackupVendor(any(UUID.class))).thenReturn(itemDto);
        when(itemService.findById(any(UUID.class))).thenReturn(item);
        when(itemQueryApplicationService.findById(any())).thenReturn(itemDto);
        when(featureFlagsManager.isFeatureOn(anyString())).thenReturn(true);
        when(exceptionRecordApplicationService.create(any(CreateExceptionRecordCommand.class))).thenReturn(exceptionRecordDto);

        when(itemApplicationService.refreshPrimaryBackupVendor(any(UUID.class))).thenReturn(itemDto);
        when(featureFlagsManager.isFeatureOn(anyString())).thenReturn(true);
        when(vendorItemSpecification.getExceptionMessage(any(),
            any(),
            any())).thenReturn("getExceptionMessageResponse");
        when(exceptionRecordApplicationService.create(any(CreateExceptionRecordCommand.class))).thenReturn(exceptionRecordDto);

        vendorItemApplicationEventListener.handleVendorItemAmendApplicationEvent(new VendorItemAmendApplicationEvent("source",
            vendorItemAmendPayloadDto));

        verify(exceptionRecordApplicationService, times(2)).create(any());

    }
}