package com.mercaso.ims.domain.replenishment.model.prediction;

import com.mercaso.ims.domain.replenishment.model.feature.FeatureVector;
import com.mercaso.ims.domain.replenishment.model.prediction.algorithm.*;
import com.mercaso.ims.domain.replenishment.model.prediction.ensemble.EnsemblePredictor;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 需求预测模型测试
 */
@ExtendWith(MockitoExtension.class)
class DemandForecastModelTest {

    @Mock
    private TimeSeriesPredictor timeSeriesPredictor;
    
    @Mock
    private MachineLearningPredictor mlPredictor;
    
    @Mock
    private DeepLearningPredictor deepLearningPredictor;
    
    @Mock
    private EnsemblePredictor ensemblePredictor;
    
    @Mock
    private ModelSelector modelSelector;
    
    @Mock
    private AnomalyDetector anomalyDetector;

    private DemandForecastModel demandForecastModel;

    @BeforeEach
    void setUp() {
        demandForecastModel = new DemandForecastModel(
            timeSeriesPredictor,
            mlPredictor,
            deepLearningPredictor,
            ensemblePredictor,
            modelSelector,
            anomalyDetector
        );
    }

    @Test
    void testNormalPrediction() {
        // Given
        FeatureVector features = createTestFeatureVector();
        
        // Mock model selection
        ModelSelectionResult selection = ModelSelectionResult.builder()
            .shouldUseTimeSeries(true)
            .shouldUseMachineLearning(true)
            .shouldUseDeepLearning(false)
            .weights(Map.of("timeseries", 0.6, "ml", 0.4))
            .build();
        
        when(anomalyDetector.isAnomalous(features)).thenReturn(false);
        when(modelSelector.selectOptimalModels(features)).thenReturn(selection);
        
        // Mock individual predictions
        DemandForecast tsForecast = createMockForecast(100.0, 0.8, "timeseries");
        DemandForecast mlForecast = createMockForecast(95.0, 0.75, "ml");
        DemandForecast ensembleForecast = createMockForecast(98.0, 0.85, "ensemble");
        
        when(timeSeriesPredictor.predict(features)).thenReturn(tsForecast);
        when(mlPredictor.predict(features)).thenReturn(mlForecast);
        when(ensemblePredictor.combine(any(), any())).thenReturn(ensembleForecast);

        // When
        DemandForecast result = demandForecastModel.predict(features);

        // Then
        assertNotNull(result);
        assertTrue(result.getPredictedDemand() > 0);
        assertTrue(result.getConfidence() > 0);
        assertNotNull(result.getForecastHorizon());
        
        verify(timeSeriesPredictor).predict(features);
        verify(mlPredictor).predict(features);
        verify(deepLearningPredictor, never()).predict(features);
        verify(ensemblePredictor).combine(any(), any());
    }

    @Test
    void testAnomalyDetection() {
        // Given
        FeatureVector features = createTestFeatureVector();
        when(anomalyDetector.isAnomalous(features)).thenReturn(true);

        // When
        DemandForecast result = demandForecastModel.predict(features);

        // Then
        assertNotNull(result);
        assertTrue(result.getConfidence() < 0.7); // 异常情况下置信度较低
        assertEquals("robust", result.getMetadata().get("method"));
        
        // 验证没有调用其他预测器
        verify(timeSeriesPredictor, never()).predict(any());
        verify(mlPredictor, never()).predict(any());
        verify(deepLearningPredictor, never()).predict(any());
    }

    @Test
    void testPredictionWithAllModels() {
        // Given
        FeatureVector features = createTestFeatureVector();
        
        ModelSelectionResult selection = ModelSelectionResult.builder()
            .shouldUseTimeSeries(true)
            .shouldUseMachineLearning(true)
            .shouldUseDeepLearning(true)
            .weights(Map.of("timeseries", 0.4, "ml", 0.3, "deeplearning", 0.3))
            .build();
        
        when(anomalyDetector.isAnomalous(features)).thenReturn(false);
        when(modelSelector.selectOptimalModels(features)).thenReturn(selection);
        
        // Mock all predictions
        DemandForecast tsForecast = createMockForecast(100.0, 0.8, "timeseries");
        DemandForecast mlForecast = createMockForecast(95.0, 0.75, "ml");
        DemandForecast dlForecast = createMockForecast(102.0, 0.7, "deeplearning");
        DemandForecast ensembleForecast = createMockForecast(99.0, 0.82, "ensemble");
        
        when(timeSeriesPredictor.predict(features)).thenReturn(tsForecast);
        when(mlPredictor.predict(features)).thenReturn(mlForecast);
        when(deepLearningPredictor.predict(features)).thenReturn(dlForecast);
        when(ensemblePredictor.combine(any(), any())).thenReturn(ensembleForecast);

        // When
        DemandForecast result = demandForecastModel.predict(features);

        // Then
        assertNotNull(result);
        assertTrue(result.getConfidence() > 0.8); // 多模型集成置信度较高
        
        verify(timeSeriesPredictor).predict(features);
        verify(mlPredictor).predict(features);
        verify(deepLearningPredictor).predict(features);
        verify(ensemblePredictor).combine(any(), any());
    }

    @Test
    void testSeasonalAdjustment() {
        // Given
        FeatureVector features = createSeasonalFeatureVector();
        
        ModelSelectionResult selection = ModelSelectionResult.builder()
            .shouldUseTimeSeries(true)
            .shouldUseMachineLearning(false)
            .shouldUseDeepLearning(false)
            .weights(Map.of("timeseries", 1.0))
            .build();
        
        when(anomalyDetector.isAnomalous(features)).thenReturn(false);
        when(modelSelector.selectOptimalModels(features)).thenReturn(selection);
        
        DemandForecast tsForecast = createMockForecast(100.0, 0.8, "timeseries");
        when(timeSeriesPredictor.predict(features)).thenReturn(tsForecast);
        when(ensemblePredictor.combine(any(), any())).thenReturn(tsForecast);

        // When
        DemandForecast result = demandForecastModel.predict(features);

        // Then
        assertNotNull(result);
        // 验证季节性调整被应用（预测值应该被季节性因子调整）
        double expectedAdjustedDemand = 100.0 * 1.2; // 季节性因子为1.2
        assertEquals(expectedAdjustedDemand, result.getPredictedDemand(), 0.1);
    }

    @Test
    void testPromotionalAdjustment() {
        // Given
        FeatureVector features = createPromotionalFeatureVector();
        
        ModelSelectionResult selection = ModelSelectionResult.builder()
            .shouldUseTimeSeries(true)
            .shouldUseMachineLearning(false)
            .shouldUseDeepLearning(false)
            .weights(Map.of("timeseries", 1.0))
            .build();
        
        when(anomalyDetector.isAnomalous(features)).thenReturn(false);
        when(modelSelector.selectOptimalModels(features)).thenReturn(selection);
        
        DemandForecast tsForecast = createMockForecast(100.0, 0.8, "timeseries");
        when(timeSeriesPredictor.predict(features)).thenReturn(tsForecast);
        when(ensemblePredictor.combine(any(), any())).thenReturn(tsForecast);

        // When
        DemandForecast result = demandForecastModel.predict(features);

        // Then
        assertNotNull(result);
        // 验证促销调整被应用
        double expectedAdjustedDemand = 100.0 * 1.5; // 促销提升因子为1.5
        assertEquals(expectedAdjustedDemand, result.getPredictedDemand(), 0.1);
    }

    @Test
    void testLifecycleAdjustment() {
        // Given
        FeatureVector features = createNewProductFeatureVector();
        
        ModelSelectionResult selection = ModelSelectionResult.builder()
            .shouldUseTimeSeries(true)
            .shouldUseMachineLearning(false)
            .shouldUseDeepLearning(false)
            .weights(Map.of("timeseries", 1.0))
            .build();
        
        when(anomalyDetector.isAnomalous(features)).thenReturn(false);
        when(modelSelector.selectOptimalModels(features)).thenReturn(selection);
        
        DemandForecast tsForecast = createMockForecast(100.0, 0.8, "timeseries");
        when(timeSeriesPredictor.predict(features)).thenReturn(tsForecast);
        when(ensemblePredictor.combine(any(), any())).thenReturn(tsForecast);

        // When
        DemandForecast result = demandForecastModel.predict(features);

        // Then
        assertNotNull(result);
        // 验证新品生命周期调整被应用
        double expectedAdjustedDemand = 100.0 * 1.2; // 新品调整因子为1.2
        assertEquals(expectedAdjustedDemand, result.getPredictedDemand(), 0.1);
    }

    @Test
    void testErrorHandling() {
        // Given
        FeatureVector features = createTestFeatureVector();
        when(anomalyDetector.isAnomalous(features)).thenReturn(false);
        when(modelSelector.selectOptimalModels(features)).thenThrow(new RuntimeException("Model selection failed"));

        // When
        DemandForecast result = demandForecastModel.predict(features);

        // Then
        assertNotNull(result);
        assertEquals("fallback", result.getMetadata().get("method"));
        assertTrue(result.getConfidence() < 0.5); // 降级方案置信度较低
    }

    @Test
    void testModelUpdate() {
        // Given
        TrainingData trainingData = mock(TrainingData.class);
        when(trainingData.size()).thenReturn(1000);

        // When
        demandForecastModel.updateModel(trainingData);

        // Then
        verify(timeSeriesPredictor).updateModel(trainingData);
        verify(mlPredictor).updateModel(trainingData);
        verify(deepLearningPredictor).updateModel(trainingData);
        verify(ensemblePredictor).updateWeights(trainingData);
        
        // 验证元数据更新
        ModelMetadata metadata = demandForecastModel.getMetadata();
        assertNotNull(metadata.getLastUpdated());
        assertEquals(1000, metadata.getTrainingDataSize());
    }

    // 辅助方法

    private FeatureVector createTestFeatureVector() {
        return FeatureVector.builder()
            .itemId(UUID.randomUUID())
            .addFeature("avg_daily_sales_30d", 10.0)
            .addFeature("sales_trend", 0.1)
            .addFeature("seasonality_index", 1.0)
            .addFeature("volatility_coefficient", 0.2)
            .addFeature("is_seasonal", 0.0)
            .addFeature("is_promotional_period", 0.0)
            .addFeature("lifecycle_stage", "成熟期")
            .build();
    }

    private FeatureVector createSeasonalFeatureVector() {
        return FeatureVector.builder()
            .itemId(UUID.randomUUID())
            .addFeature("avg_daily_sales_30d", 10.0)
            .addFeature("is_seasonal", 1.0)
            .addFeature("seasonal_factor", 1.2)
            .addFeature("is_promotional_period", 0.0)
            .addFeature("lifecycle_stage", "成熟期")
            .build();
    }

    private FeatureVector createPromotionalFeatureVector() {
        return FeatureVector.builder()
            .itemId(UUID.randomUUID())
            .addFeature("avg_daily_sales_30d", 10.0)
            .addFeature("is_seasonal", 0.0)
            .addFeature("is_promotional_period", 1.0)
            .addFeature("promotional_lift_factor", 1.5)
            .addFeature("lifecycle_stage", "成熟期")
            .build();
    }

    private FeatureVector createNewProductFeatureVector() {
        return FeatureVector.builder()
            .itemId(UUID.randomUUID())
            .addFeature("avg_daily_sales_30d", 10.0)
            .addFeature("is_seasonal", 0.0)
            .addFeature("is_promotional_period", 0.0)
            .addFeature("lifecycle_stage", "新品")
            .build();
    }

    private DemandForecast createMockForecast(double demand, double confidence, String method) {
        return DemandForecast.builder()
            .predictedDemand(demand)
            .confidence(confidence)
            .lowerBound(demand * 0.8)
            .upperBound(demand * 1.2)
            .forecastHorizon(Arrays.asList(demand, demand, demand, demand, demand, demand, demand))
            .method(method)
            .metadata(Map.of("test", true))
            .build();
    }
}
