package com.mercaso.ims.utils.vendor;

import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.enums.VendorStatus;
import com.mercaso.ims.infrastructure.repository.vendor.jpa.dataobject.VendorDo;
import java.time.Instant;
import java.util.UUID;

public class VendorUtil {

    public static VendorDo buildVendorDo(String vendorName) {
        return VendorDo.builder()
            .vendorName(vendorName)
            .externalPicking(true)
            .vendorStatus(VendorStatus.ACTIVE)
            .createdAt(Instant.now())
            .updatedAt(Instant.now())
            .createdBy("SYSTEM")
            .build();
    }

    public static Vendor buildVendor(String vendorName) {
        return Vendor.builder()
            .vendorName(vendorName)
            .vendorStatus(VendorStatus.ACTIVE)
            .externalPicking(true)
            .createdAt(Instant.now())
            .updatedAt(Instant.now())
            .createdBy("SYSTEM")
            .id(UUID.randomUUID())
            .build();
    }

    public static Vendor buildVendor(UUID id) {
        return Vendor.builder()
            .id(id)
            .vendorName("vendorName")
            .vendorStatus(VendorStatus.ACTIVE)
            .build();
    }

    public static Vendor buildVendor(UUID id, String name) {
        return Vendor.builder()
            .id(id)
            .vendorName(name)
            .vendorStatus(VendorStatus.ACTIVE)
            .build();
    }

}
