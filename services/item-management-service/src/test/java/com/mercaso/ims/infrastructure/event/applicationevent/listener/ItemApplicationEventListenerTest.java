package com.mercaso.ims.infrastructure.event.applicationevent.listener;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.doThrow;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.application.dto.ItemAdjustmentRequestDetailDto;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.ItemPromoPriceDto;
import com.mercaso.ims.application.dto.ItemRegPriceDto;
import com.mercaso.ims.application.dto.VendorItemDto;
import com.mercaso.ims.application.dto.event.ItemAmendApplicationEvent;
import com.mercaso.ims.application.dto.event.ItemCreatedApplicationEvent;
import com.mercaso.ims.application.dto.event.ItemDeletedApplicationEvent;
import com.mercaso.ims.application.dto.payload.ItemAmendPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemCreatedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemDeletedPayloadDto;
import com.mercaso.ims.application.mapper.itemadjustmentrequestdetail.ItemAdjustmentRequestDetailDtoApplicationMapper;
import com.mercaso.ims.application.queryservice.ItemQueryApplicationService;
import com.mercaso.ims.application.service.AsyncItemVersionService;
import com.mercaso.ims.application.service.ExceptionRecordApplicationService;
import com.mercaso.ims.application.service.FinaleApplicationService;
import com.mercaso.ims.application.service.ItemPriceApplicationService;
import com.mercaso.ims.application.service.ShopifyApplicationService;
import com.mercaso.ims.application.service.VendorItemApplicationService;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.ItemAdjustmentRequestDetail;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.ItemAdjustmentRequestDetailRepository;
import com.mercaso.ims.domain.itempromoprice.service.ItemPromoPriceService;
import com.mercaso.ims.domain.itemregprice.ItemRegPriceSpecification;
import com.mercaso.ims.utils.applicationevent.ItemAmendApplicationEventUtil;
import com.mercaso.ims.utils.applicationevent.ItemCreatedApplicationEventUtil;
import com.mercaso.ims.utils.item.ItemAmendPayloadDtoUtil;
import com.mercaso.ims.utils.item.ItemCreatedPayloadDtoUtil;
import com.mercaso.ims.utils.item.ItemDTOUtil;
import com.mercaso.ims.utils.itemadjustmentrequestdetail.ItemAdjustmentRequestDetailDtoUtil;
import com.mercaso.ims.utils.itemadjustmentrequestdetail.ItemAdjustmentRequestDetailUtil;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class ItemApplicationEventListenerTest {

    @Mock
    ItemQueryApplicationService itemQueryApplicationService;
    @Mock
    ItemAdjustmentRequestDetailRepository itemAdjustmentRequestDetailRepository;
    @Mock
    ItemAdjustmentRequestDetailDtoApplicationMapper adjustmentRequestDetailDtoApplicationMapper;
    @Mock
    ShopifyApplicationService shopifyApplicationService;
    @Mock
    ExceptionRecordApplicationService exceptionRecordApplicationService;
    @Mock
    ItemRegPriceSpecification itemRegPriceSpecification;
    @Mock
    FeatureFlagsManager featureFlagsManager;
    @Mock
    FinaleApplicationService finaleApplicationService;
    @Mock
    private ItemPriceApplicationService itemPriceApplicationService;
    @Mock
    private VendorItemApplicationService vendorItemApplicationService;
    @Mock
    private ItemPromoPriceService itemPromoPriceService;
    @InjectMocks
    ItemApplicationEventListener itemApplicationEventListener;
    @Mock
    AsyncItemVersionService asyncItemVersionService;


    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testHandleItemCreatedApplicationEvent() {
        ItemDto itemDto = ItemDTOUtil.buildItemDTO();
        UUID id = UUID.randomUUID();
        UUID itemAdjustmentRequestId = UUID.randomUUID();
        ItemCreatedPayloadDto payloadDto = ItemCreatedPayloadDtoUtil.buildItemCreatedPayloadDto(UUID.randomUUID(),
            itemAdjustmentRequestId);
        ItemCreatedApplicationEvent event = ItemCreatedApplicationEventUtil.buildItemCreatedApplicationEvent(payloadDto);

        ItemAdjustmentRequestDetailDto detailDto = ItemAdjustmentRequestDetailDtoUtil.buildItemAdjustmentRequestDetailDto(id);
        ItemAdjustmentRequestDetail detail = ItemAdjustmentRequestDetailUtil.buildItemAdjustmentRequestDetail(id);
        when(adjustmentRequestDetailDtoApplicationMapper.domainToDto(any(ItemAdjustmentRequestDetail.class))).thenReturn(
            detailDto);
        when(itemAdjustmentRequestDetailRepository.save(any(ItemAdjustmentRequestDetail.class))).thenReturn(detail);
        when(itemAdjustmentRequestDetailRepository.findById(any())).thenReturn(detail);
        when(itemQueryApplicationService.findById(any(UUID.class))).thenReturn(itemDto);
        when(featureFlagsManager.isFeatureOn(any())).thenReturn(true);

        itemApplicationEventListener.handleItemCreatedApplicationEvent(event);
        verify(shopifyApplicationService).syncItemCreatedEvent(any(ItemCreatedApplicationEvent.class));
    }

    @Test
    void testHandleItemAmendApplicationEvent() {
        ItemDto itemDto = ItemDTOUtil.buildItemDTO();
        UUID id = UUID.randomUUID();
        ItemAmendPayloadDto payloadDto = ItemAmendPayloadDtoUtil.buildItemAmendPayloadDto();
        ItemAmendApplicationEvent event = ItemAmendApplicationEventUtil.buildItemAmendApplicationEvent(payloadDto);

        ItemAdjustmentRequestDetailDto detailDto = ItemAdjustmentRequestDetailDtoUtil.buildItemAdjustmentRequestDetailDto(id);
        ItemAdjustmentRequestDetail detail = ItemAdjustmentRequestDetailUtil.buildItemAdjustmentRequestDetail(id);
        when(adjustmentRequestDetailDtoApplicationMapper.domainToDto(any(ItemAdjustmentRequestDetail.class))).thenReturn(
            detailDto);
        when(itemAdjustmentRequestDetailRepository.save(any(ItemAdjustmentRequestDetail.class))).thenReturn(detail);
        when(itemAdjustmentRequestDetailRepository.findById(any())).thenReturn(detail);
        when(itemQueryApplicationService.findById(any(UUID.class))).thenReturn(itemDto);

        itemApplicationEventListener.handleItemAmendApplicationEvent(event);
        verify(shopifyApplicationService).syncItemAmendEvent(any(ItemAmendApplicationEvent.class));
    }

    @Nested
    @DisplayName("Tests for handleItemDeletedApplicationEvent")
    class HandleItemDeletedApplicationEventTests {

        private ItemDto itemDto;
        private ItemDeletedApplicationEvent event;
        private UUID itemId;

        @BeforeEach
        void setUp() {
            itemId = UUID.randomUUID();
            UUID priceGroupId = UUID.randomUUID();
            UUID vendorItemId = UUID.randomUUID();
            UUID promoPriceId = UUID.randomUUID();

            itemDto = new ItemDto();
            itemDto.setId(itemId);

            ItemRegPriceDto regPriceDto = new ItemRegPriceDto();
            regPriceDto.setItemId(itemId);
            regPriceDto.setItemPriceGroupId(priceGroupId);
            itemDto.setItemRegPrice(regPriceDto);

            VendorItemDto vendorItemDto = new VendorItemDto();
            vendorItemDto.setVendorItemId(vendorItemId);
            itemDto.setVendorItemDtos(List.of(vendorItemDto));

            ItemPromoPriceDto promoPriceDto = new ItemPromoPriceDto();
            promoPriceDto.setItemPromoPriceId(promoPriceId);
            itemDto.setItemPromoPrices(List.of(promoPriceDto));

            ItemDeletedPayloadDto payloadDto = new ItemDeletedPayloadDto();
            payloadDto.setData(itemDto);

            event = new ItemDeletedApplicationEvent(this, payloadDto);
        }

        @Test
        @DisplayName("should call all cleanup services and sync for a valid item")
        void shouldCallAllCleanupServicesAndSync() {
            // Act
            itemApplicationEventListener.handleItemDeletedApplicationEvent(event);

            // Assert
            verify(itemPriceApplicationService).unbindingItemPriceGroup(itemId, itemDto.getItemRegPrice().getItemPriceGroupId());
            verify(itemPriceApplicationService).deleteByItemId(itemId);
            verify(vendorItemApplicationService).delete(itemDto.getVendorItemDtos().get(0).getVendorItemId());
            verify(itemPromoPriceService).delete(itemDto.getItemPromoPrices().get(0).getItemPromoPriceId());
            verify(shopifyApplicationService).syncItemDeleteEvent(event);
        }

        @Test
        @DisplayName("should do nothing if item DTO is null")
        void shouldDoNothingIfItemDtoIsNull() {
            // Arrange
            event.getPayload().setData(null);

            // Act
            itemApplicationEventListener.handleItemDeletedApplicationEvent(event);

            // Assert
            verify(itemPriceApplicationService, never()).unbindingItemPriceGroup(any(), any());
            verify(itemPriceApplicationService, never()).deleteByItemId(any());
            verify(vendorItemApplicationService, never()).delete(any());
            verify(itemPromoPriceService, never()).delete(any());
            verify(shopifyApplicationService, never()).syncItemDeleteEvent(any());
        }

        @Test
        @DisplayName("should continue cleanup even if unbinding price group fails")
        void shouldContinueWhenUnbindingFails() {
            // Arrange
            doThrow(new RuntimeException("Unbinding failed")).when(itemPriceApplicationService)
                .unbindingItemPriceGroup(any(), any());

            // Act
            itemApplicationEventListener.handleItemDeletedApplicationEvent(event);

            // Assert
            verify(itemPriceApplicationService).deleteByItemId(itemId);
            verify(vendorItemApplicationService).delete(any());
            verify(itemPromoPriceService).delete(any());
            verify(shopifyApplicationService).syncItemDeleteEvent(event);
        }

        @Test
        @DisplayName("should continue cleanup even if deleting vendor item fails")
        void shouldContinueWhenVendorItemDeleteFails() {
            // Arrange
            doThrow(new RuntimeException("Vendor delete failed")).when(vendorItemApplicationService).delete(any());

            // Act
            itemApplicationEventListener.handleItemDeletedApplicationEvent(event);

            // Assert
            verify(itemPriceApplicationService).unbindingItemPriceGroup(any(), any());
            verify(itemPriceApplicationService).deleteByItemId(itemId);
            verify(itemPromoPriceService).delete(any());
            verify(shopifyApplicationService).syncItemDeleteEvent(event);
        }
    }
}