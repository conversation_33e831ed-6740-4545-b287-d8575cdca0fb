package com.mercaso.ims.infrastructure.event.applicationevent.listener;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.application.command.UpdateItemCostChangeRequestCommand;
import com.mercaso.ims.application.command.UpdateVendorItemCommand;
import com.mercaso.ims.application.dto.ItemCostChangeRequestDto;
import com.mercaso.ims.application.dto.VendorItemDto;
import com.mercaso.ims.application.dto.event.ItemCostChangeRequestCreatedEvent;
import com.mercaso.ims.application.dto.event.ItemCostChangeRequestUpdatedEvent;
import com.mercaso.ims.application.dto.payload.ItemCostChangeRequestCreatedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemCostChangeRequestUpdatedPayloadDto;
import com.mercaso.ims.application.service.ItemCostChangeRequestApplicationService;
import com.mercaso.ims.application.service.VendorItemApplicationService;
import com.mercaso.ims.domain.itemcostchangerequest.enums.ItemCostChangeRequestStatus;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.VendorItemSpecification;
import com.mercaso.ims.domain.vendoritem.enums.VendorItemStatus;
import com.mercaso.ims.domain.vendoritem.service.VendorItemService;
import com.mercaso.ims.utils.itemcostchangerequest.ItemCostChangeRequestDtoUtil;
import com.mercaso.ims.utils.vendor.VendorItemUtil;
import java.math.BigDecimal;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class ItemCostChangeRequestApplicationEventListenerTest {

    @Mock
    ItemCostChangeRequestApplicationService itemCostChangeRequestApplicationService;
    @Mock
    VendorItemApplicationService vendorItemApplicationService;
    @Mock
    VendorItemService vendorItemService;
    @Mock
    VendorItemSpecification vendorItemSpecification;

    @InjectMocks
    ItemCostChangeRequestApplicationEventListener itemCostChangeRequestApplicationEventListener;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testHandleItemCostChangeRequestUpdatedEvent() {
        when(vendorItemApplicationService.update(any(UpdateVendorItemCommand.class))).thenReturn(new VendorItemDto(new UUID(0L,
            0L),
            new UUID(0L, 0L),
            null,
            null,
            "vendorName", null,
            "vendorSkuNumber",
            "vendorItemName",
            "note",
            "statusChangeReason",
            "aisle",
            new BigDecimal(0),
            new BigDecimal(0),
            new BigDecimal(0),
            new BigDecimal(0),
            new BigDecimal(0),
            VendorItemStatus.ACTIVE, null, null, null, "DIRECT"));
        when(vendorItemService.findByVendorIDAndItemId(any(UUID.class), any(UUID.class))).thenReturn(null);
        ItemCostChangeRequestDto itemCostChangeRequestDto = ItemCostChangeRequestDtoUtil.buildItemCostChangeRequestDto();
        itemCostChangeRequestDto.setStatus(ItemCostChangeRequestStatus.APPROVED);
        itemCostChangeRequestApplicationEventListener.handleItemCostChangeRequestUpdatedEvent(new ItemCostChangeRequestUpdatedEvent(
            "source",
            new ItemCostChangeRequestUpdatedPayloadDto(itemCostChangeRequestDto, null)));
        verify(vendorItemService).findByVendorIDAndItemId(any(), any());

    }

    @Test
    void testHandleItemCostChangeRequestCreatedEvent() {
        ItemCostChangeRequestDto itemCostChangeRequestDto = ItemCostChangeRequestDtoUtil.buildItemCostChangeRequestDto();
        itemCostChangeRequestDto.setStatus(ItemCostChangeRequestStatus.APPROVED);

        VendorItem vendorItem = VendorItemUtil.buildVendorItem();

        when(itemCostChangeRequestApplicationService.updateItemCostChangeRequest(any(UpdateItemCostChangeRequestCommand.class))).thenReturn(
            itemCostChangeRequestDto);
        when(vendorItemService.findByVendorIDAndItemId(any(), any())).thenReturn(vendorItem);
        when(vendorItemSpecification.isReasonableMargin(any(), any())).thenReturn(true);

        itemCostChangeRequestApplicationEventListener.handleItemCostChangeRequestCreatedEvent(new ItemCostChangeRequestCreatedEvent(
            "source",
            new ItemCostChangeRequestCreatedPayloadDto(itemCostChangeRequestDto, null)));

        verify(itemCostChangeRequestApplicationService).updateItemCostChangeRequest(any());

    }
}