package com.mercaso.ims.interfaces;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.command.CreateVendorItemCommand;
import com.mercaso.ims.application.command.UpdateVendorItemCommand;
import com.mercaso.ims.application.dto.VendorItemAuditHistoryInfoDto;
import com.mercaso.ims.application.dto.VendorItemDto;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.infrastructure.repository.item.jpa.dataobject.ItemDo;
import com.mercaso.ims.infrastructure.repository.vendor.jpa.dataobject.VendorDo;
import com.mercaso.ims.utils.vendor.VendorItemCommandUtil;
import java.util.List;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;

class VendorItemRestApiIT extends AbstractIT {

    @Test
    void shouldSuccessWhenDeleteVendorItem() {

        String sku = RandomStringUtils.randomAlphabetic(5);
        String vendorItemNumber = RandomStringUtils.randomAlphabetic(5);
        String vendorName = RandomStringUtils.randomAlphabetic(5);

        ItemDo itemDo = buildItemData(sku);
        VendorDo vendorDo = buildVendorDoData(vendorName);
        VendorItem savedVendorItem = buildVendorItemData(vendorItemNumber, vendorDo.getId(), itemDo.getId());

        VendorItem vendorItem = vendorItemService.findById(savedVendorItem.getId());

        assertNotNull(vendorItem);

        vendorItemRestApiUtil.deleteVendorItemRequest(savedVendorItem.getId());

        vendorItem = vendorItemService.findById(savedVendorItem.getId());

        assertNull(vendorItem);
    }


    @Test
    void shouldSuccessWhenCreateItem() throws Exception {

        String sku = RandomStringUtils.randomAlphabetic(5);
        String vendorItemNumber = RandomStringUtils.randomAlphabetic(5);
        String vendorName = RandomStringUtils.randomAlphabetic(5);

        ItemDo itemDo = buildItemData(sku);
        VendorDo vendorDo = buildVendorDoData(vendorName);

        CreateVendorItemCommand command = VendorItemCommandUtil.buildCreateVendorItemCommand(itemDo.getId(),
            vendorDo.getId(),
            vendorItemNumber);

        VendorItemDto result = vendorItemRestApiUtil.createVendorItemRequest(command);
        assertNotNull(result.getVendorItemId());
        assertEquals(vendorItemNumber, result.getVendorSkuNumber());

    }

    @Test
    void shouldSuccessWhenUpdateItem() throws Exception {

        String sku = RandomStringUtils.randomAlphabetic(5);
        String vendorItemNumber = RandomStringUtils.randomAlphabetic(5);

        String vendorName = RandomStringUtils.randomAlphabetic(5);

        ItemDo itemDo = buildItemData(sku);
        VendorDo vendorDo = buildVendorDoData(vendorName);
        VendorItem savedVendorItem = buildVendorItemData(vendorItemNumber, vendorDo.getId(), itemDo.getId());

        String newVendorItemName = RandomStringUtils.randomAlphabetic(5);
        UpdateVendorItemCommand command = VendorItemCommandUtil.buildUpdateVendorItemCommand(savedVendorItem.getId(),
            newVendorItemName,
            vendorItemNumber);

        VendorItemDto result = vendorItemRestApiUtil.updateVendorItemRequest(savedVendorItem.getId(), command);

        VendorItem vendorItem = vendorItemService.findById(savedVendorItem.getId());

        assertNotNull(result);
        assertEquals(newVendorItemName, result.getVendorItemName());
        assertEquals(newVendorItemName, vendorItem.getVendorItemName());
    }

    @Test
    void shouldSuccessWhenGetVendorItemAuditHistories() throws Exception {

        String sku = RandomStringUtils.randomAlphabetic(5);
        String vendorItemNumber = RandomStringUtils.randomAlphabetic(5);

        String vendorName = RandomStringUtils.randomAlphabetic(5);

        ItemDo itemDo = buildItemData(sku);
        VendorDo vendorDo = buildVendorDoData(vendorName);
        VendorItem savedVendorItem = buildVendorItemData(vendorItemNumber, vendorDo.getId(), itemDo.getId());

        String newVendorItemName = RandomStringUtils.randomAlphabetic(5);
        UpdateVendorItemCommand command = VendorItemCommandUtil.buildUpdateVendorItemCommand(savedVendorItem.getId(),
            newVendorItemName,
            vendorItemNumber);

        vendorItemRestApiUtil.updateVendorItemRequest(savedVendorItem.getId(), command);
        List<VendorItemAuditHistoryInfoDto> result = vendorItemRestApiUtil.getVendorItemAuditHistories(savedVendorItem.getId());

        assertNotNull(result);
        assertEquals(1, result.size());
    }
}
