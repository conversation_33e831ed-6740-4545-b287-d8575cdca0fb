spring:
  config:
    import: vault://
    activate:
      on-profile: dev
security:
  public-paths:
    - /v1/document/public-paths/**
    - /v1/document/coming_soon.jpg
shopify:
  host: https://dev-2-mercaso.myshopify.com

springdoc:
  swagger-ui:
    enabled: true
    config-url: /item-management/v3/api-docs/swagger-config
    url: /item-management/v3/api-docs
resilience4j.ratelimiter:
  instances:
    syncShopify:
      limitForPeriod: 1
      limitRefreshPeriod: 5s
      timeoutDuration: 60s
    invokeShopifyApi:
      limitForPeriod: 1
      limitRefreshPeriod: 1s
      timeoutDuration: 60s
ims:
  alert:
    alert_slack_hook: https://hooks.slack.com/triggers/T02AVL4UJG4/*************/371803a2b90038a208bb743fbbbc59a6

external:
  vernon:
    from_email: <EMAIL>
  exotic_blvd:
    from_email: <EMAIL>
  seven_star:
    from_email: huqi<PERSON><EMAIL>

finale:
  accountPathComponent: mercasosandbox