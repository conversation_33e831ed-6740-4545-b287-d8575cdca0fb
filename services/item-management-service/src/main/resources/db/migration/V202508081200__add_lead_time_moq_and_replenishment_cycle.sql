-- Add lead_time_days and moq to vendor_item; add replenishment_cycle to item
-- This migration aligns DB schema with replenishment calculation M1 requirements

-- vendor_item additions
ALTER TABLE vendor_item
    ADD COLUMN IF NOT EXISTS lead_time_days INTEGER;

COMMENT ON COLUMN vendor_item.lead_time_days IS 'Lead time in days for this vendor-item (used in replenishment calculation)';

ALTER TABLE vendor_item
    ADD COLUMN IF NOT EXISTS moq INTEGER;

COMMENT ON COLUMN vendor_item.moq IS 'Minimum order quantity for this vendor-item (used in replenishment calculation)';

-- item additions
ALTER TABLE item
    ADD COLUMN IF NOT EXISTS replenishment_cycle INTEGER;

COMMENT ON COLUMN item.replenishment_cycle IS 'Replenishment cycle in days for the item (used in forecasting window)';

