-- Add reason_code and urgency_level to replenishment_recommendation for structured outputs
ALTER TABLE replenishment_recommendation
    ADD COLUMN IF NOT EXISTS reason_code VARCHAR(50);

COMMENT ON COLUMN replenishment_recommendation.reason_code IS 'Structured reason code for reorder decision';

ALTER TABLE replenishment_recommendation
    ADD COLUMN IF NOT EXISTS urgency_level INTEGER;

COMMENT ON COLUMN replenishment_recommendation.urgency_level IS 'Urgency score from 1 (low) to 5 (critical)';

