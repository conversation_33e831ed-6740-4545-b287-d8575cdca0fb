CREATE TABLE replenishment_recommendation
(
    id                      UUID         NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    item_id                 UUID         NOT NULL,
    sku_number              VARCHAR(100) NOT NULL,
    reorder_flag            BOOLEAN      NOT NULL DEFAULT FALSE,
    reorder_qty             INTEGER      DEFAULT 0,
    forecast_sales          NUMERIC(10, 2),
    safety_stock            INTEGER      DEFAULT 0,
    reason                  TEXT,
    avg_daily_sales         NUMERIC(10, 2),
    available_days          INTEGER,
    lead_time_days          INTEGER,
    safety_stock_days       INTEGER,
    current_stock           INTEGER,
    available_stock         INTEGER,
    in_transit_stock        INTEGER,
    sales_variation         NUMERIC(5, 4),
    lifecycle_stage         VARCHAR(50),
    is_perishable           BOOLEAN      DEFAULT FALSE,
    minimum_order_qty       INTEGER,
    max_stock_capacity      INTEGER,
    calculated_at           TIMESTAMP    NOT NULL DEFAULT NOW(),
    created_at              TIMESTAMP    NOT NULL DEFAULT NOW(),
    created_by              <PERSON><PERSON><PERSON><PERSON>(255),
    updated_at              TIMESTAMP    NOT NULL DEFAULT NOW(),
    updated_by              <PERSON><PERSON>HAR(255),
    deleted_at              TIMESTAMP,
    deleted_by              VARCHAR(255)
);

CREATE INDEX idx_replenishment_recommendation_item_id ON replenishment_recommendation (item_id);
CREATE INDEX idx_replenishment_recommendation_sku_number ON replenishment_recommendation (sku_number);
CREATE INDEX idx_replenishment_recommendation_reorder_flag ON replenishment_recommendation (reorder_flag);
CREATE INDEX idx_replenishment_recommendation_calculated_at ON replenishment_recommendation (calculated_at);
CREATE INDEX idx_replenishment_recommendation_lifecycle_stage ON replenishment_recommendation (lifecycle_stage);

COMMENT ON TABLE replenishment_recommendation IS '商品补货建议表';

COMMENT ON COLUMN replenishment_recommendation.id IS '主键';
COMMENT ON COLUMN replenishment_recommendation.item_id IS '商品ID';
COMMENT ON COLUMN replenishment_recommendation.sku_number IS '商品SKU';
COMMENT ON COLUMN replenishment_recommendation.reorder_flag IS '是否需要补货';
COMMENT ON COLUMN replenishment_recommendation.reorder_qty IS '建议补货数量';
COMMENT ON COLUMN replenishment_recommendation.forecast_sales IS '预测销量';
COMMENT ON COLUMN replenishment_recommendation.safety_stock IS '安全库存';
COMMENT ON COLUMN replenishment_recommendation.reason IS '补货原因';
COMMENT ON COLUMN replenishment_recommendation.avg_daily_sales IS '平均日销量';
COMMENT ON COLUMN replenishment_recommendation.available_days IS '可用库存天数';
COMMENT ON COLUMN replenishment_recommendation.lead_time_days IS '补货提前期天数';
COMMENT ON COLUMN replenishment_recommendation.safety_stock_days IS '安全库存天数';
COMMENT ON COLUMN replenishment_recommendation.current_stock IS '当前库存';
COMMENT ON COLUMN replenishment_recommendation.available_stock IS '可用库存';
COMMENT ON COLUMN replenishment_recommendation.in_transit_stock IS '在途库存';
COMMENT ON COLUMN replenishment_recommendation.sales_variation IS '销售波动率';
COMMENT ON COLUMN replenishment_recommendation.lifecycle_stage IS '生命周期阶段(新品、主销、尾货)';
COMMENT ON COLUMN replenishment_recommendation.is_perishable IS '是否易腐商品';
COMMENT ON COLUMN replenishment_recommendation.minimum_order_qty IS '最小订货量';
COMMENT ON COLUMN replenishment_recommendation.max_stock_capacity IS '最大库存容量';
COMMENT ON COLUMN replenishment_recommendation.calculated_at IS '计算时间'; 