package com.mercaso.ims.domain.replenishment.model.prediction;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 需求预测结果
 */
@Data
@Builder(toBuilder = true)
public class DemandForecast {
    
    /**
     * 预测的需求量
     */
    private double predictedDemand;
    
    /**
     * 预测置信度 [0.0, 1.0]
     */
    private double confidence;
    
    /**
     * 预测区间下界
     */
    private double lowerBound;
    
    /**
     * 预测区间上界
     */
    private double upperBound;
    
    /**
     * 预测时间范围（未来N天的预测值）
     */
    private List<Double> forecastHorizon;
    
    /**
     * 预测时间戳
     */
    @Builder.Default
    private LocalDateTime forecastTimestamp = LocalDateTime.now();
    
    /**
     * 商品ID
     */
    private UUID itemId;
    
    /**
     * 预测方法/算法标识
     */
    private String method;
    
    /**
     * 季节性因子
     */
    private Double seasonalityFactor;
    
    /**
     * 趋势因子
     */
    private Double trendFactor;
    
    /**
     * 预测的标准误差
     */
    private Double standardError;
    
    /**
     * 模型特定的元数据
     */
    private Map<String, Object> metadata;
    
    /**
     * 获取预测区间宽度
     */
    public double getPredictionInterval() {
        return upperBound - lowerBound;
    }
    
    /**
     * 获取相对预测区间宽度（相对于预测值的比例）
     */
    public double getRelativePredictionInterval() {
        if (predictedDemand == 0) return Double.MAX_VALUE;
        return getPredictionInterval() / predictedDemand;
    }
    
    /**
     * 判断预测是否可靠
     */
    public boolean isReliable() {
        return confidence >= 0.7 && getRelativePredictionInterval() <= 0.5;
    }
    
    /**
     * 获取指定天数后的预测值
     */
    public double getForecastForDay(int dayOffset) {
        if (forecastHorizon == null || dayOffset >= forecastHorizon.size() || dayOffset < 0) {
            return predictedDemand; // 返回基础预测值
        }
        return forecastHorizon.get(dayOffset);
    }
    
    /**
     * 获取预测质量等级
     */
    public PredictionQuality getQualityLevel() {
        if (confidence >= 0.8 && getRelativePredictionInterval() <= 0.3) {
            return PredictionQuality.HIGH;
        } else if (confidence >= 0.6 && getRelativePredictionInterval() <= 0.5) {
            return PredictionQuality.MEDIUM;
        } else {
            return PredictionQuality.LOW;
        }
    }
    
    /**
     * 预测质量等级
     */
    public enum PredictionQuality {
        HIGH("高质量预测"),
        MEDIUM("中等质量预测"),
        LOW("低质量预测");
        
        private final String description;
        
        PredictionQuality(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
