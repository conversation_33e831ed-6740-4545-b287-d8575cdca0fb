package com.mercaso.ims.domain.replenishment.model.feature;

import lombok.Builder;
import lombok.Data;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 特征向量 - 存储和管理机器学习特征
 */
@Data
@Builder(toBuilder = true)
public class FeatureVector {
    
    /**
     * 数值特征
     */
    @Builder.Default
    private Map<String, Double> numericFeatures = new HashMap<>();
    
    /**
     * 分类特征
     */
    @Builder.Default
    private Map<String, String> categoricalFeatures = new HashMap<>();
    
    /**
     * 布尔特征
     */
    @Builder.Default
    private Map<String, Boolean> booleanFeatures = new HashMap<>();
    
    /**
     * 时间戳
     */
    @Builder.Default
    private Instant timestamp = Instant.now();
    
    /**
     * 特征版本
     */
    private String version;
    
    /**
     * 商品ID
     */
    private UUID itemId;
    
    /**
     * 特征元数据
     */
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();
    
    /**
     * 添加数值特征
     */
    public FeatureVector addFeature(String name, double value) {
        numericFeatures.put(name, value);
        return this;
    }
    
    /**
     * 添加分类特征
     */
    public FeatureVector addFeature(String name, String value) {
        categoricalFeatures.put(name, value);
        return this;
    }
    
    /**
     * 添加布尔特征
     */
    public FeatureVector addFeature(String name, boolean value) {
        booleanFeatures.put(name, value);
        return this;
    }
    
    /**
     * 获取数值特征
     */
    public double getFeature(String name) {
        return numericFeatures.getOrDefault(name, 0.0);
    }
    
    /**
     * 获取分类特征
     */
    public String getStringFeature(String name) {
        return categoricalFeatures.getOrDefault(name, "");
    }
    
    /**
     * 获取布尔特征
     */
    public boolean getBooleanFeature(String name) {
        return booleanFeatures.getOrDefault(name, false);
    }
    
    /**
     * 检查是否包含特征
     */
    public boolean hasFeature(String name) {
        return numericFeatures.containsKey(name) || 
               categoricalFeatures.containsKey(name) || 
               booleanFeatures.containsKey(name);
    }
    
    /**
     * 获取所有特征名称
     */
    public Set<String> getFeatureNames() {
        Set<String> names = new HashSet<>();
        names.addAll(numericFeatures.keySet());
        names.addAll(categoricalFeatures.keySet());
        names.addAll(booleanFeatures.keySet());
        return names;
    }
    
    /**
     * 合并另一个特征向量
     */
    public FeatureVector merge(FeatureVector other) {
        FeatureVector merged = this.toBuilder().build();
        merged.numericFeatures.putAll(other.numericFeatures);
        merged.categoricalFeatures.putAll(other.categoricalFeatures);
        merged.booleanFeatures.putAll(other.booleanFeatures);
        merged.metadata.putAll(other.metadata);
        return merged;
    }
    
    /**
     * 特征标准化
     */
    public FeatureVector normalize() {
        return FeatureNormalizer.normalize(this);
    }
    
    /**
     * 特征选择
     */
    public FeatureVector selectFeatures(Set<String> selectedFeatures) {
        FeatureVector selected = FeatureVector.builder()
            .itemId(this.itemId)
            .version(this.version)
            .timestamp(this.timestamp)
            .build();
        
        for (String feature : selectedFeatures) {
            if (numericFeatures.containsKey(feature)) {
                selected.addFeature(feature, numericFeatures.get(feature));
            } else if (categoricalFeatures.containsKey(feature)) {
                selected.addFeature(feature, categoricalFeatures.get(feature));
            } else if (booleanFeatures.containsKey(feature)) {
                selected.addFeature(feature, booleanFeatures.get(feature));
            }
        }
        
        return selected;
    }
    
    /**
     * 转换为数值向量（用于机器学习算法）
     */
    public double[] toNumericArray() {
        return toNumericArray(null);
    }
    
    /**
     * 转换为数值向量（指定特征顺序）
     */
    public double[] toNumericArray(List<String> featureOrder) {
        if (featureOrder == null) {
            featureOrder = new ArrayList<>(numericFeatures.keySet());
            Collections.sort(featureOrder);
        }
        
        return featureOrder.stream()
            .mapToDouble(this::getFeature)
            .toArray();
    }
    
    /**
     * 获取特征统计信息
     */
    public FeatureStatistics getStatistics() {
        return FeatureStatistics.builder()
            .numericFeatureCount(numericFeatures.size())
            .categoricalFeatureCount(categoricalFeatures.size())
            .booleanFeatureCount(booleanFeatures.size())
            .totalFeatureCount(getFeatureNames().size())
            .numericFeatureStats(calculateNumericStats())
            .build();
    }
    
    /**
     * 计算数值特征统计
     */
    private Map<String, Double> calculateNumericStats() {
        Map<String, Double> stats = new HashMap<>();
        
        if (!numericFeatures.isEmpty()) {
            DoubleSummaryStatistics summary = numericFeatures.values().stream()
                .mapToDouble(Double::doubleValue)
                .summaryStatistics();
            
            stats.put("mean", summary.getAverage());
            stats.put("min", summary.getMin());
            stats.put("max", summary.getMax());
            stats.put("sum", summary.getSum());
            stats.put("count", (double) summary.getCount());
        }
        
        return stats;
    }
    
    /**
     * 验证特征向量
     */
    public ValidationResult validate() {
        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();
        
        // 检查必要特征
        if (itemId == null) {
            errors.add("Item ID is required");
        }
        
        // 检查特征值范围
        for (Map.Entry<String, Double> entry : numericFeatures.entrySet()) {
            Double value = entry.getValue();
            if (value == null || Double.isNaN(value) || Double.isInfinite(value)) {
                errors.add("Invalid numeric value for feature: " + entry.getKey());
            }
        }
        
        // 检查特征数量
        if (getFeatureNames().size() == 0) {
            warnings.add("No features found in vector");
        } else if (getFeatureNames().size() > 1000) {
            warnings.add("Large number of features (" + getFeatureNames().size() + ") may impact performance");
        }
        
        return ValidationResult.builder()
            .valid(errors.isEmpty())
            .errors(errors)
            .warnings(warnings)
            .build();
    }
    
    /**
     * 创建特征向量构建器
     */
    public static FeatureVectorBuilder builder() {
        return new FeatureVectorBuilder();
    }
    
    /**
     * 特征向量构建器
     */
    public static class FeatureVectorBuilder {
        private final FeatureVector vector;
        
        public FeatureVectorBuilder() {
            this.vector = new FeatureVector();
            this.vector.numericFeatures = new HashMap<>();
            this.vector.categoricalFeatures = new HashMap<>();
            this.vector.booleanFeatures = new HashMap<>();
            this.vector.metadata = new HashMap<>();
            this.vector.timestamp = Instant.now();
        }
        
        public FeatureVectorBuilder itemId(UUID itemId) {
            vector.itemId = itemId;
            return this;
        }
        
        public FeatureVectorBuilder version(String version) {
            vector.version = version;
            return this;
        }
        
        public FeatureVectorBuilder addFeature(String name, double value) {
            vector.addFeature(name, value);
            return this;
        }
        
        public FeatureVectorBuilder addFeature(String name, String value) {
            vector.addFeature(name, value);
            return this;
        }
        
        public FeatureVectorBuilder addFeature(String name, boolean value) {
            vector.addFeature(name, value);
            return this;
        }
        
        public FeatureVectorBuilder merge(FeatureVector other) {
            vector.numericFeatures.putAll(other.numericFeatures);
            vector.categoricalFeatures.putAll(other.categoricalFeatures);
            vector.booleanFeatures.putAll(other.booleanFeatures);
            vector.metadata.putAll(other.metadata);
            return this;
        }
        
        public FeatureVectorBuilder metadata(String key, Object value) {
            vector.metadata.put(key, value);
            return this;
        }
        
        public FeatureVector build() {
            return vector;
        }
    }
    
    /**
     * 特征统计信息
     */
    @Data
    @Builder
    public static class FeatureStatistics {
        private int numericFeatureCount;
        private int categoricalFeatureCount;
        private int booleanFeatureCount;
        private int totalFeatureCount;
        private Map<String, Double> numericFeatureStats;
    }
    
    /**
     * 验证结果
     */
    @Data
    @Builder
    public static class ValidationResult {
        private boolean valid;
        private List<String> errors;
        private List<String> warnings;
    }
}
