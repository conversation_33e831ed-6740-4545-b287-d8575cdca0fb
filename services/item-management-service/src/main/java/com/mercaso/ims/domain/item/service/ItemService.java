package com.mercaso.ims.domain.item.service;

import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.enums.ItemUpcType;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

public interface ItemService {

    Item findById(UUID id);

    Item findBySku(String sku);

    Item save(Item item);

    Item update(Item item);

    Item delete(UUID id);

    Page<Item> findByCreatedAtBetween(Instant begin, Instant end, String photo, PageRequest pageRequest);

    List<Item> findAllByUpcAndUpcType(String upc, ItemUpcType upcType);

    List<Item> findByBrandId(UUID brandId);

    List<Item> findActiveItems();
}
