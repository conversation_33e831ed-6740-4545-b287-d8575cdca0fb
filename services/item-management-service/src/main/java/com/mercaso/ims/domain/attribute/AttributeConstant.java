package com.mercaso.ims.domain.attribute;

import java.util.UUID;

public class AttributeConstant {

    public static final UUID BOTTLE_SIZE_ATTRIBUTE_ID = UUID.fromString("d6b888b8-8e53-4dcd-b5e3-63be5036e0c0");
    public static final UUID ITEM_SIZE_ATTRIBUTE_ID = UUID.fromString("a8e3f151-2151-44f8-a2d8-3ce0b90b8cdc");
    public static final UUID VARIANT_WEIGHT_ATTRIBUTE_ID = UUID.fromString("c2d42be6-7819-4154-94f6-9aa1935ba2fc");
    public static final UUID VOLUME_VALUE_ATTRIBUTE_ID = UUID.fromString("adeeb5fb-452c-428f-a666-b2842f7dc11a");
    public static final UUID CASE_WEIGHT_ATTRIBUTE_ID = UUID.fromString("9f6fb540-6e4d-4b52-9fd8-5cf9901118e2");
    public static final UUID EACH_WEIGHT_ATTRIBUTE_ID = UUID.fromString("13aba5c7-64a4-47d4-a179-8c223553dfec");
    public static final UUID COOLER_ATTRIBUTE_ID = UUID.fromString("0e973c70-f376-4d86-b6dd-90edcbedc37e");


    private AttributeConstant() {
        throw new IllegalStateException("Constant class");
    }
}
