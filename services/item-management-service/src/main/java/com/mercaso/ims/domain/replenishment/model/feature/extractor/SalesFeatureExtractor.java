package com.mercaso.ims.domain.replenishment.model.feature.extractor;

import com.mercaso.ims.domain.replenishment.model.feature.FeatureVector;
import com.mercaso.ims.domain.replenishment.SalesData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.IntStream;

/**
 * 销售特征提取器
 * 
 * 提取销售相关的特征，包括：
 * 1. 基础统计特征（均值、方差、分位数等）
 * 2. 趋势特征（线性趋势、增长率等）
 * 3. 季节性特征（周季节性、月季节性等）
 * 4. 波动性特征（变异系数、波动率等）
 * 5. 时间特征（最后销售日期、连续零销售天数等）
 */
@Slf4j
@Component
public class SalesFeatureExtractor implements FeatureExtractor<SalesData> {
    
    @Override
    public FeatureVector extract(SalesData salesData) {
        log.debug("Extracting sales features for item");
        
        FeatureVector.FeatureVectorBuilder builder = FeatureVector.builder();
        
        try {
            // 1. 基础统计特征
            extractBasicStatistics(salesData, builder);
            
            // 2. 趋势特征
            extractTrendFeatures(salesData, builder);
            
            // 3. 季节性特征
            extractSeasonalityFeatures(salesData, builder);
            
            // 4. 波动性特征
            extractVolatilityFeatures(salesData, builder);
            
            // 5. 时间特征
            extractTemporalFeatures(salesData, builder);
            
            // 6. 高级统计特征
            extractAdvancedStatistics(salesData, builder);
            
            // 7. 业务特征
            extractBusinessFeatures(salesData, builder);
            
        } catch (Exception e) {
            log.error("Error extracting sales features", e);
            // 添加错误标记特征
            builder.addFeature("extraction_error", 1.0);
        }
        
        return builder.build();
    }
    
    /**
     * 提取基础统计特征
     */
    private void extractBasicStatistics(SalesData salesData, FeatureVector.FeatureVectorBuilder builder) {
        List<Integer> sales7d = salesData.getDailySalesLast7Days();
        List<Integer> sales30d = salesData.getDailySalesLast30Days();
        
        if (sales7d != null && !sales7d.isEmpty()) {
            DoubleSummaryStatistics stats7d = sales7d.stream().mapToDouble(Integer::doubleValue).summaryStatistics();
            builder.addFeature("avg_daily_sales_7d", stats7d.getAverage());
            builder.addFeature("max_daily_sales_7d", stats7d.getMax());
            builder.addFeature("min_daily_sales_7d", stats7d.getMin());
            builder.addFeature("sum_sales_7d", stats7d.getSum());
            builder.addFeature("sales_range_7d", stats7d.getMax() - stats7d.getMin());
        }
        
        if (sales30d != null && !sales30d.isEmpty()) {
            DoubleSummaryStatistics stats30d = sales30d.stream().mapToDouble(Integer::doubleValue).summaryStatistics();
            builder.addFeature("avg_daily_sales_30d", stats30d.getAverage());
            builder.addFeature("max_daily_sales_30d", stats30d.getMax());
            builder.addFeature("min_daily_sales_30d", stats30d.getMin());
            builder.addFeature("sum_sales_30d", stats30d.getSum());
            builder.addFeature("sales_range_30d", stats30d.getMax() - stats30d.getMin());
            
            // 分位数特征
            List<Double> sortedSales = sales30d.stream().mapToDouble(Integer::doubleValue).sorted().boxed().toList();
            builder.addFeature("sales_q25_30d", calculatePercentile(sortedSales, 0.25));
            builder.addFeature("sales_median_30d", calculatePercentile(sortedSales, 0.5));
            builder.addFeature("sales_q75_30d", calculatePercentile(sortedSales, 0.75));
            builder.addFeature("sales_q90_30d", calculatePercentile(sortedSales, 0.9));
        }
    }
    
    /**
     * 提取趋势特征
     */
    private void extractTrendFeatures(SalesData salesData, FeatureVector.FeatureVectorBuilder builder) {
        List<Integer> sales30d = salesData.getDailySalesLast30Days();
        
        if (sales30d != null && sales30d.size() >= 7) {
            // 线性趋势（回归斜率）
            double linearTrend = calculateLinearTrend(sales30d);
            builder.addFeature("linear_trend_30d", linearTrend);
            builder.addFeature("trend_strength", Math.abs(linearTrend));
            builder.addFeature("is_increasing_trend", linearTrend > 0 ? 1.0 : 0.0);
            builder.addFeature("is_decreasing_trend", linearTrend < 0 ? 1.0 : 0.0);
            
            // 增长率特征
            double growthRate = calculateGrowthRate(sales30d);
            builder.addFeature("growth_rate_30d", growthRate);
            
            // 加速度特征（二阶导数）
            double acceleration = calculateAcceleration(sales30d);
            builder.addFeature("sales_acceleration", acceleration);
            
            // 趋势一致性
            double trendConsistency = calculateTrendConsistency(sales30d);
            builder.addFeature("trend_consistency", trendConsistency);
        }
        
        // 短期vs长期趋势对比
        if (salesData.getDailySalesLast7Days() != null && sales30d != null) {
            double shortTermTrend = calculateLinearTrend(salesData.getDailySalesLast7Days());
            double longTermTrend = calculateLinearTrend(sales30d);
            builder.addFeature("trend_divergence", Math.abs(shortTermTrend - longTermTrend));
        }
    }
    
    /**
     * 提取季节性特征
     */
    private void extractSeasonalityFeatures(SalesData salesData, FeatureVector.FeatureVectorBuilder builder) {
        List<Integer> sales30d = salesData.getDailySalesLast30Days();
        
        if (sales30d != null && sales30d.size() >= 14) {
            // 周季节性检测
            double weeklySeasonality = detectWeeklySeasonality(sales30d);
            builder.addFeature("weekly_seasonality_strength", weeklySeasonality);
            builder.addFeature("has_weekly_seasonality", weeklySeasonality > 0.3 ? 1.0 : 0.0);
            
            // 工作日vs周末模式
            Map<String, Double> weekdayPattern = analyzeWeekdayPattern(sales30d);
            builder.addFeature("weekday_avg_sales", weekdayPattern.get("weekday_avg"));
            builder.addFeature("weekend_avg_sales", weekdayPattern.get("weekend_avg"));
            builder.addFeature("weekend_lift_factor", weekdayPattern.get("weekend_lift"));
        }
        
        // 季节性标记
        if (salesData.getIsSeasonalPattern() != null) {
            builder.addFeature("is_seasonal", salesData.getIsSeasonalPattern() ? 1.0 : 0.0);
        }
        
        // 当前季节因子
        double currentSeasonFactor = getCurrentSeasonFactor();
        builder.addFeature("current_season_factor", currentSeasonFactor);
    }
    
    /**
     * 提取波动性特征
     */
    private void extractVolatilityFeatures(SalesData salesData, FeatureVector.FeatureVectorBuilder builder) {
        List<Integer> sales30d = salesData.getDailySalesLast30Days();
        
        if (sales30d != null && !sales30d.isEmpty()) {
            // 变异系数
            double cv = calculateCoefficientOfVariation(sales30d);
            builder.addFeature("coefficient_of_variation", cv);
            
            // 标准差
            double std = calculateStandardDeviation(sales30d);
            builder.addFeature("sales_std_30d", std);
            
            // 相对标准差
            double mean = sales30d.stream().mapToDouble(Integer::doubleValue).average().orElse(0.0);
            builder.addFeature("relative_std_30d", mean > 0 ? std / mean : 0.0);
            
            // 波动率等级
            builder.addFeature("volatility_level", classifyVolatility(cv));
            
            // 连续变化检测
            int consecutiveIncreases = countConsecutiveIncreases(sales30d);
            int consecutiveDecreases = countConsecutiveDecreases(sales30d);
            builder.addFeature("max_consecutive_increases", consecutiveIncreases);
            builder.addFeature("max_consecutive_decreases", consecutiveDecreases);
        }
        
        // 销售波动率（来自原始数据）
        if (salesData.getSalesVariation() != null) {
            builder.addFeature("sales_variation", salesData.getSalesVariation().doubleValue());
        }
    }
    
    /**
     * 提取时间特征
     */
    private void extractTemporalFeatures(SalesData salesData, FeatureVector.FeatureVectorBuilder builder) {
        // 最后销售日期相关特征
        if (salesData.getLastSaleDate() != null) {
            long daysSinceLastSale = java.time.temporal.ChronoUnit.DAYS.between(
                salesData.getLastSaleDate(), LocalDate.now());
            builder.addFeature("days_since_last_sale", daysSinceLastSale);
            builder.addFeature("has_recent_sales", daysSinceLastSale <= 7 ? 1.0 : 0.0);
        }
        
        // 连续零销售天数
        if (salesData.getConsecutiveZeroSalesDays() != null) {
            builder.addFeature("consecutive_zero_sales_days", salesData.getConsecutiveZeroSalesDays());
            builder.addFeature("is_dormant", salesData.getConsecutiveZeroSalesDays() > 14 ? 1.0 : 0.0);
        }
        
        // 销售活跃度
        List<Integer> sales30d = salesData.getDailySalesLast30Days();
        if (sales30d != null) {
            long activeDays = sales30d.stream().mapToLong(s -> s > 0 ? 1 : 0).sum();
            builder.addFeature("active_days_ratio_30d", activeDays / 30.0);
            builder.addFeature("sales_frequency", activeDays);
        }
        
        // 当前时间特征
        LocalDate now = LocalDate.now();
        builder.addFeature("day_of_week", now.getDayOfWeek().getValue());
        builder.addFeature("day_of_month", now.getDayOfMonth());
        builder.addFeature("month_of_year", now.getMonthValue());
        builder.addFeature("is_weekend", now.getDayOfWeek().getValue() >= 6 ? 1.0 : 0.0);
        builder.addFeature("is_month_end", now.getDayOfMonth() >= 28 ? 1.0 : 0.0);
    }
    
    /**
     * 提取高级统计特征
     */
    private void extractAdvancedStatistics(SalesData salesData, FeatureVector.FeatureVectorBuilder builder) {
        List<Integer> sales30d = salesData.getDailySalesLast30Days();
        
        if (sales30d != null && sales30d.size() >= 10) {
            // 偏度（分布的对称性）
            double skewness = calculateSkewness(sales30d);
            builder.addFeature("sales_skewness", skewness);
            
            // 峰度（分布的尖锐程度）
            double kurtosis = calculateKurtosis(sales30d);
            builder.addFeature("sales_kurtosis", kurtosis);
            
            // 自相关系数
            double autocorrelation = calculateAutocorrelation(sales30d, 1);
            builder.addFeature("sales_autocorr_lag1", autocorrelation);
            
            // 零销售比例
            long zeroSalesDays = sales30d.stream().mapToLong(s -> s == 0 ? 1 : 0).sum();
            builder.addFeature("zero_sales_ratio", zeroSalesDays / (double) sales30d.size());
            
            // 销售集中度（基尼系数）
            double giniCoefficient = calculateGiniCoefficient(sales30d);
            builder.addFeature("sales_concentration", giniCoefficient);
        }
    }
    
    /**
     * 提取业务特征
     */
    private void extractBusinessFeatures(SalesData salesData, FeatureVector.FeatureVectorBuilder builder) {
        // 销售生命周期阶段推断
        String lifecycleStage = inferLifecycleStage(salesData);
        builder.addFeature("inferred_lifecycle_stage", lifecycleStage);
        
        // 销售稳定性评分
        double stabilityScore = calculateSalesStability(salesData);
        builder.addFeature("sales_stability_score", stabilityScore);
        
        // 需求可预测性
        double predictability = calculatePredictability(salesData);
        builder.addFeature("demand_predictability", predictability);
        
        // 销售动量
        double momentum = calculateSalesMomentum(salesData);
        builder.addFeature("sales_momentum", momentum);
    }
    
    // 辅助计算方法
    
    private double calculatePercentile(List<Double> sortedData, double percentile) {
        if (sortedData.isEmpty()) return 0.0;
        
        int index = (int) Math.ceil(percentile * sortedData.size()) - 1;
        index = Math.max(0, Math.min(index, sortedData.size() - 1));
        return sortedData.get(index);
    }
    
    private double calculateLinearTrend(List<Integer> data) {
        if (data.size() < 2) return 0.0;
        
        int n = data.size();
        double sumX = n * (n - 1) / 2.0;
        double sumY = data.stream().mapToDouble(Integer::doubleValue).sum();
        double sumXY = IntStream.range(0, n).mapToDouble(i -> i * data.get(i)).sum();
        double sumX2 = n * (n - 1) * (2 * n - 1) / 6.0;
        
        return (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
    }
    
    private double calculateGrowthRate(List<Integer> data) {
        if (data.size() < 2) return 0.0;
        
        double firstHalf = data.subList(0, data.size() / 2).stream().mapToDouble(Integer::doubleValue).average().orElse(0.0);
        double secondHalf = data.subList(data.size() / 2, data.size()).stream().mapToDouble(Integer::doubleValue).average().orElse(0.0);
        
        return firstHalf > 0 ? (secondHalf - firstHalf) / firstHalf : 0.0;
    }
    
    private double calculateAcceleration(List<Integer> data) {
        if (data.size() < 3) return 0.0;
        
        // 计算二阶差分的平均值
        List<Double> firstDiff = new ArrayList<>();
        for (int i = 1; i < data.size(); i++) {
            firstDiff.add((double) (data.get(i) - data.get(i - 1)));
        }
        
        double sumSecondDiff = 0.0;
        for (int i = 1; i < firstDiff.size(); i++) {
            sumSecondDiff += firstDiff.get(i) - firstDiff.get(i - 1);
        }
        
        return sumSecondDiff / (firstDiff.size() - 1);
    }
    
    private double calculateTrendConsistency(List<Integer> data) {
        if (data.size() < 3) return 0.0;
        
        int consistentDirections = 0;
        int totalDirections = 0;
        
        for (int i = 2; i < data.size(); i++) {
            int dir1 = Integer.compare(data.get(i - 1), data.get(i - 2));
            int dir2 = Integer.compare(data.get(i), data.get(i - 1));
            
            if (dir1 != 0 && dir2 != 0) {
                totalDirections++;
                if (dir1 == dir2) {
                    consistentDirections++;
                }
            }
        }
        
        return totalDirections > 0 ? (double) consistentDirections / totalDirections : 0.0;
    }
    
    private double detectWeeklySeasonality(List<Integer> data) {
        if (data.size() < 14) return 0.0;
        
        // 计算每个星期几的平均销量
        Map<Integer, List<Integer>> weekdayGroups = new HashMap<>();
        for (int i = 0; i < data.size(); i++) {
            int weekday = i % 7;
            weekdayGroups.computeIfAbsent(weekday, k -> new ArrayList<>()).add(data.get(i));
        }
        
        // 计算星期几之间的方差
        List<Double> weekdayAverages = new ArrayList<>();
        for (int i = 0; i < 7; i++) {
            List<Integer> dayData = weekdayGroups.get(i);
            if (dayData != null && !dayData.isEmpty()) {
                weekdayAverages.add(dayData.stream().mapToDouble(Integer::doubleValue).average().orElse(0.0));
            }
        }
        
        if (weekdayAverages.size() < 7) return 0.0;
        
        double overallMean = weekdayAverages.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double weekdayVariance = weekdayAverages.stream()
            .mapToDouble(avg -> Math.pow(avg - overallMean, 2))
            .average().orElse(0.0);
        
        return overallMean > 0 ? Math.sqrt(weekdayVariance) / overallMean : 0.0;
    }
    
    @Override
    public List<String> getFeatureNames() {
        return Arrays.asList(
            // 基础统计特征
            "avg_daily_sales_7d", "avg_daily_sales_30d", "max_daily_sales_30d", "min_daily_sales_30d",
            "sales_range_30d", "sales_q25_30d", "sales_median_30d", "sales_q75_30d", "sales_q90_30d",
            
            // 趋势特征
            "linear_trend_30d", "trend_strength", "growth_rate_30d", "sales_acceleration", "trend_consistency",
            
            // 季节性特征
            "weekly_seasonality_strength", "has_weekly_seasonality", "weekday_avg_sales", "weekend_avg_sales",
            
            // 波动性特征
            "coefficient_of_variation", "sales_std_30d", "volatility_level", "max_consecutive_increases",
            
            // 时间特征
            "days_since_last_sale", "consecutive_zero_sales_days", "active_days_ratio_30d", "sales_frequency",
            
            // 高级统计特征
            "sales_skewness", "sales_kurtosis", "sales_autocorr_lag1", "zero_sales_ratio", "sales_concentration",
            
            // 业务特征
            "sales_stability_score", "demand_predictability", "sales_momentum"
        );
    }
    
    @Override
    public FeatureMetadata getMetadata() {
        return FeatureMetadata.builder()
            .extractorName("SalesFeatureExtractor")
            .version("1.0.0")
            .featureCount(getFeatureNames().size())
            .description("Extracts comprehensive sales-related features for demand forecasting")
            .build();
    }
}
