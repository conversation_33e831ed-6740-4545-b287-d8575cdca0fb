package com.mercaso.ims.infrastructure.event.applicationevent.listener;

import com.mercaso.ims.application.command.UpdateItemCostChangeRequestCommand;
import com.mercaso.ims.application.command.UpdateVendorItemCommand;
import com.mercaso.ims.application.dto.ItemCostChangeRequestDto;
import com.mercaso.ims.application.dto.event.ItemCostChangeRequestCreatedEvent;
import com.mercaso.ims.application.dto.event.ItemCostChangeRequestUpdatedEvent;
import com.mercaso.ims.application.dto.payload.ItemCostChangeRequestCreatedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemCostChangeRequestUpdatedPayloadDto;
import com.mercaso.ims.application.service.ItemCostChangeRequestApplicationService;
import com.mercaso.ims.application.service.VendorItemApplicationService;
import com.mercaso.ims.domain.itemcostchangerequest.enums.CostType;
import com.mercaso.ims.domain.itemcostchangerequest.enums.ItemCostChangeRequestStatus;
import com.mercaso.ims.domain.itemcostchangerequest.enums.MatchedType;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.VendorItemSpecification;
import com.mercaso.ims.domain.vendoritem.service.VendorItemService;
import java.math.BigDecimal;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class ItemCostChangeRequestApplicationEventListener {

    private final ItemCostChangeRequestApplicationService itemCostChangeRequestApplicationService;
    private final VendorItemApplicationService vendorItemApplicationService;
    private final VendorItemService vendorItemService;
    private final VendorItemSpecification vendorItemSpecification;

    @EventListener
    public void handleItemCostChangeRequestUpdatedEvent(
        ItemCostChangeRequestUpdatedEvent itemCostChangeRequestUpdatedEvent) {

        ItemCostChangeRequestUpdatedPayloadDto payload = itemCostChangeRequestUpdatedEvent.getPayload();
        ItemCostChangeRequestDto itemCostChangeRequestDto = payload.getData();
        if (!ItemCostChangeRequestStatus.APPROVED.equals(itemCostChangeRequestDto.getStatus())) {
            log.warn("[handleItemCostChangeRequestUpdatedEvent] not handle for status:{}",
                itemCostChangeRequestDto.getStatus());
            return;
        }

        VendorItem vendorItem = vendorItemService.findByVendorIDAndItemId(itemCostChangeRequestDto.getVendorId(),
            itemCostChangeRequestDto.getItemId());

        if (null == vendorItem) {
            log.warn("[handleItemCostChangeRequestUpdatedEvent] not find vendor item by vendorId:{}, itemId:{}",
                itemCostChangeRequestDto.getVendorId(),
                itemCostChangeRequestDto.getItemId());
            return;
        }
        Boolean availability = itemCostChangeRequestDto.getAvailability() == null ? vendorItem.getAvailability()
            : itemCostChangeRequestDto.getAvailability();

        UpdateVendorItemCommand updateVendorItemCommand = UpdateVendorItemCommand.builder()
            .vendorItemId(vendorItem.getId())
            .vendorSkuNumber(vendorItem.getVendorSkuNumber())
            .vendorItemName(itemCostChangeRequestDto.getVendorItemName())
            .note(vendorItem.getNote())
            .itemCostChangeRequestId(itemCostChangeRequestDto.getId())
            .availability(availability)
            .aisle(itemCostChangeRequestDto.getAisle() == null ? vendorItem.getAisle() : itemCostChangeRequestDto.getAisle())
            .build();

        log.info("[handleItemCostChangeRequestUpdatedEvent]  for cost type:{}", itemCostChangeRequestDto.getCostType());
        if (CostType.DIRECT_COST.getCostTypeName().equals(itemCostChangeRequestDto.getCostType())) {
            updateVendorItemCommand.setVendorItemName(vendorItem.getVendorItemName());
            updateVendorItemCommand.setCost(itemCostChangeRequestDto.getTargetCost());
            updateVendorItemCommand.setBackupCost(vendorItem.getBackupPackPlusCrvCost());
            updateVendorItemCommand.setIsCostRefreshed(true);
        } else {
            updateVendorItemCommand.setCost(vendorItem.getCost());
            updateVendorItemCommand.setBackupCost(itemCostChangeRequestDto.getTargetCost());
            updateVendorItemCommand.setIsBackupCostRefreshed(true);
        }

        vendorItemApplicationService.update(updateVendorItemCommand);

        log.info("[handleItemCostChangeRequestUpdatedEvent] for request ={}", payload);
    }

    @EventListener
    public void handleItemCostChangeRequestCreatedEvent(
        ItemCostChangeRequestCreatedEvent itemCostChangeRequestCreatedEvent) {
        log.info("[handleItemCostChangeRequestCreatedEvent] for request ={}", itemCostChangeRequestCreatedEvent);
        ItemCostChangeRequestCreatedPayloadDto payload = itemCostChangeRequestCreatedEvent.getPayload();
        ItemCostChangeRequestDto itemCostChangeRequestDto = payload.getData();
        if (itemCostChangeRequestDto.getMatchType() == MatchedType.AUTO_MATCHED_AND_UPDATED && checkIfVendorItemCostShouldChange(
            itemCostChangeRequestDto.getVendorId(),
            itemCostChangeRequestDto.getItemId(),
            itemCostChangeRequestDto.getTargetCost())) {
            log.info("[handleItemCostChangeRequestCreatedEvent] auto approve for request ={}",
                itemCostChangeRequestDto);
            UpdateItemCostChangeRequestCommand command = UpdateItemCostChangeRequestCommand.builder()
                .id(itemCostChangeRequestDto.getId())
                .previousCost(itemCostChangeRequestDto.getPreviousCost())
                .status(ItemCostChangeRequestStatus.APPROVED)
                .build();
            itemCostChangeRequestApplicationService.updateItemCostChangeRequest(command);
        }
        log.info("[handleItemCostChangeRequestCreatedEvent] for request ={}", payload);
    }

    private boolean checkIfVendorItemCostShouldChange(UUID vendorId, UUID itemId, BigDecimal newCost) {
        VendorItem vendorItem = vendorItemService.findByVendorIDAndItemId(vendorId, itemId);
        return vendorItemSpecification.isReasonableMargin(vendorItem, newCost);
    }


}
