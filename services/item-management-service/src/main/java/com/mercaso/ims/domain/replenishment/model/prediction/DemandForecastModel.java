package com.mercaso.ims.domain.replenishment.model.prediction;

import com.mercaso.ims.domain.replenishment.model.feature.FeatureVector;
import com.mercaso.ims.domain.replenishment.model.prediction.algorithm.*;
import com.mercaso.ims.domain.replenishment.model.prediction.ensemble.EnsemblePredictor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 需求预测模型 - 智能补货系统的核心预测引擎
 * 
 * 功能特性：
 * 1. 多算法集成预测（时间序列 + 机器学习 + 深度学习）
 * 2. 自适应模型选择
 * 3. 置信度区间计算
 * 4. 异常检测和处理
 * 5. 季节性和趋势分析
 */
@Slf4j
@Component
public class DemandForecastModel implements PredictionModel<FeatureVector, DemandForecast> {

    private final TimeSeriesPredictor timeSeriesPredictor;
    private final MachineLearningPredictor mlPredictor;
    private final DeepLearningPredictor deepLearningPredictor;
    private final EnsemblePredictor ensemblePredictor;
    private final ModelSelector modelSelector;
    private final AnomalyDetector anomalyDetector;
    
    private double currentConfidence = 0.0;
    private ModelMetadata metadata;

    public DemandForecastModel(
            TimeSeriesPredictor timeSeriesPredictor,
            MachineLearningPredictor mlPredictor,
            DeepLearningPredictor deepLearningPredictor,
            EnsemblePredictor ensemblePredictor,
            ModelSelector modelSelector,
            AnomalyDetector anomalyDetector) {
        this.timeSeriesPredictor = timeSeriesPredictor;
        this.mlPredictor = mlPredictor;
        this.deepLearningPredictor = deepLearningPredictor;
        this.ensemblePredictor = ensemblePredictor;
        this.modelSelector = modelSelector;
        this.anomalyDetector = anomalyDetector;
        this.metadata = ModelMetadata.builder()
            .modelName("DemandForecastModel")
            .version("1.0.0")
            .createdAt(new Date())
            .build();
    }

    @Override
    public DemandForecast predict(FeatureVector features) {
        log.info("Starting demand forecast prediction for item: {}", features.getItemId());
        
        try {
            // 1. 异常检测
            if (anomalyDetector.isAnomalous(features)) {
                log.warn("Anomalous data detected for item: {}, applying robust prediction", features.getItemId());
                return predictWithRobustMethod(features);
            }
            
            // 2. 模型选择 - 根据数据特征选择最适合的模型组合
            ModelSelectionResult selection = modelSelector.selectOptimalModels(features);
            
            // 3. 多模型预测
            List<DemandForecast> predictions = new ArrayList<>();
            
            // 时间序列预测
            if (selection.shouldUseTimeSeries()) {
                DemandForecast tsForecast = timeSeriesPredictor.predict(features);
                predictions.add(tsForecast);
                log.debug("Time series prediction: {}", tsForecast.getPredictedDemand());
            }
            
            // 机器学习预测
            if (selection.shouldUseMachineLearning()) {
                DemandForecast mlForecast = mlPredictor.predict(features);
                predictions.add(mlForecast);
                log.debug("ML prediction: {}", mlForecast.getPredictedDemand());
            }
            
            // 深度学习预测（适用于复杂模式）
            if (selection.shouldUseDeepLearning()) {
                DemandForecast dlForecast = deepLearningPredictor.predict(features);
                predictions.add(dlForecast);
                log.debug("Deep learning prediction: {}", dlForecast.getPredictedDemand());
            }
            
            // 4. 集成预测
            DemandForecast ensembleForecast = ensemblePredictor.combine(predictions, selection.getWeights());
            
            // 5. 后处理和置信度计算
            DemandForecast finalForecast = postProcess(ensembleForecast, features);
            
            // 6. 更新模型置信度
            this.currentConfidence = calculateOverallConfidence(predictions, finalForecast);
            
            log.info("Demand forecast completed for item: {}, predicted demand: {}, confidence: {}", 
                features.getItemId(), finalForecast.getPredictedDemand(), currentConfidence);
            
            return finalForecast;
            
        } catch (Exception e) {
            log.error("Error in demand forecast prediction for item: {}", features.getItemId(), e);
            return createFallbackForecast(features);
        }
    }

    /**
     * 异常数据的鲁棒预测方法
     */
    private DemandForecast predictWithRobustMethod(FeatureVector features) {
        // 使用中位数和分位数方法进行鲁棒预测
        double historicalMedian = features.getFeature("sales_median_30d");
        double q25 = features.getFeature("sales_q25_30d");
        double q75 = features.getFeature("sales_q75_30d");
        
        double robustPrediction = historicalMedian;
        double lowerBound = q25;
        double upperBound = q75;
        
        return DemandForecast.builder()
            .predictedDemand(robustPrediction)
            .confidence(0.6) // 异常情况下置信度较低
            .lowerBound(lowerBound)
            .upperBound(upperBound)
            .forecastHorizon(generateRobustHorizon(robustPrediction, 7))
            .metadata(Map.of("method", "robust", "reason", "anomaly_detected"))
            .build();
    }

    /**
     * 后处理：应用业务规则和约束
     */
    private DemandForecast postProcess(DemandForecast forecast, FeatureVector features) {
        double processedDemand = forecast.getPredictedDemand();
        
        // 1. 非负约束
        processedDemand = Math.max(0, processedDemand);
        
        // 2. 季节性调整
        if (features.getFeature("is_seasonal") > 0.5) {
            double seasonalFactor = features.getFeature("seasonal_factor");
            processedDemand *= seasonalFactor;
        }
        
        // 3. 促销期调整
        if (features.getFeature("is_promotional_period") > 0.5) {
            double promotionalLift = features.getFeature("promotional_lift_factor");
            processedDemand *= promotionalLift;
        }
        
        // 4. 生命周期调整
        String lifecycleStage = features.getStringFeature("lifecycle_stage");
        processedDemand = applyLifecycleAdjustment(processedDemand, lifecycleStage);
        
        // 5. 重新计算置信区间
        double adjustmentRatio = processedDemand / forecast.getPredictedDemand();
        double newLowerBound = forecast.getLowerBound() * adjustmentRatio;
        double newUpperBound = forecast.getUpperBound() * adjustmentRatio;
        
        return DemandForecast.builder()
            .predictedDemand(processedDemand)
            .confidence(forecast.getConfidence())
            .lowerBound(newLowerBound)
            .upperBound(newUpperBound)
            .forecastHorizon(adjustForecastHorizon(forecast.getForecastHorizon(), adjustmentRatio))
            .metadata(enrichMetadata(forecast.getMetadata(), features))
            .build();
    }

    /**
     * 生命周期调整
     */
    private double applyLifecycleAdjustment(double demand, String lifecycleStage) {
        switch (lifecycleStage) {
            case "新品":
                return demand * 1.2; // 新品通常需求增长
            case "成长期":
                return demand * 1.1;
            case "成熟期":
                return demand; // 保持不变
            case "衰退期":
                return demand * 0.8;
            case "清仓":
                return demand * 0.5;
            default:
                return demand;
        }
    }

    /**
     * 计算整体置信度
     */
    private double calculateOverallConfidence(List<DemandForecast> predictions, DemandForecast finalForecast) {
        if (predictions.isEmpty()) {
            return 0.5; // 默认置信度
        }
        
        // 1. 基于模型一致性的置信度
        double variance = calculatePredictionVariance(predictions);
        double consistencyConfidence = Math.exp(-variance / 100); // 方差越小，一致性越高
        
        // 2. 基于历史准确率的置信度
        double historicalAccuracy = getHistoricalAccuracy();
        
        // 3. 基于数据质量的置信度
        double dataQualityScore = calculateDataQualityScore(predictions);
        
        // 4. 综合置信度计算
        double overallConfidence = (consistencyConfidence * 0.4 + 
                                   historicalAccuracy * 0.4 + 
                                   dataQualityScore * 0.2);
        
        return Math.min(Math.max(overallConfidence, 0.1), 0.95); // 限制在[0.1, 0.95]范围内
    }

    /**
     * 计算预测方差
     */
    private double calculatePredictionVariance(List<DemandForecast> predictions) {
        if (predictions.size() < 2) return 0.0;
        
        double mean = predictions.stream()
            .mapToDouble(DemandForecast::getPredictedDemand)
            .average()
            .orElse(0.0);
        
        return predictions.stream()
            .mapToDouble(p -> Math.pow(p.getPredictedDemand() - mean, 2))
            .average()
            .orElse(0.0);
    }

    /**
     * 获取历史准确率
     */
    private double getHistoricalAccuracy() {
        // 这里应该从模型性能监控系统获取历史准确率
        // 暂时返回默认值
        return 0.75;
    }

    /**
     * 计算数据质量分数
     */
    private double calculateDataQualityScore(List<DemandForecast> predictions) {
        // 基于预测结果的置信度计算数据质量分数
        double avgConfidence = predictions.stream()
            .mapToDouble(DemandForecast::getConfidence)
            .average()
            .orElse(0.5);
        
        return avgConfidence;
    }

    /**
     * 创建降级预测
     */
    private DemandForecast createFallbackForecast(FeatureVector features) {
        // 使用简单的移动平均作为降级方案
        double avgDailySales = features.getFeature("avg_daily_sales_30d");
        
        return DemandForecast.builder()
            .predictedDemand(avgDailySales)
            .confidence(0.3) // 降级方案置信度较低
            .lowerBound(avgDailySales * 0.7)
            .upperBound(avgDailySales * 1.3)
            .forecastHorizon(generateSimpleHorizon(avgDailySales, 7))
            .metadata(Map.of("method", "fallback", "reason", "prediction_error"))
            .build();
    }

    /**
     * 生成简单的预测区间
     */
    private List<Double> generateSimpleHorizon(double baseDemand, int days) {
        List<Double> horizon = new ArrayList<>();
        for (int i = 0; i < days; i++) {
            horizon.add(baseDemand);
        }
        return horizon;
    }

    /**
     * 生成鲁棒预测区间
     */
    private List<Double> generateRobustHorizon(double baseDemand, int days) {
        List<Double> horizon = new ArrayList<>();
        Random random = new Random();
        for (int i = 0; i < days; i++) {
            // 添加小幅随机波动
            double variation = baseDemand * (random.nextGaussian() * 0.1);
            horizon.add(Math.max(0, baseDemand + variation));
        }
        return horizon;
    }

    /**
     * 调整预测区间
     */
    private List<Double> adjustForecastHorizon(List<Double> originalHorizon, double adjustmentRatio) {
        return originalHorizon.stream()
            .map(value -> value * adjustmentRatio)
            .collect(Collectors.toList());
    }

    /**
     * 丰富元数据
     */
    private Map<String, Object> enrichMetadata(Map<String, Object> originalMetadata, FeatureVector features) {
        Map<String, Object> enrichedMetadata = new HashMap<>(originalMetadata);
        enrichedMetadata.put("prediction_timestamp", new Date());
        enrichedMetadata.put("feature_version", features.getVersion());
        enrichedMetadata.put("model_version", metadata.getVersion());
        enrichedMetadata.put("confidence_level", currentConfidence);
        return enrichedMetadata;
    }

    @Override
    public double getConfidence() {
        return currentConfidence;
    }

    @Override
    public ModelMetadata getMetadata() {
        return metadata;
    }

    @Override
    public void updateModel(TrainingData trainingData) {
        log.info("Updating demand forecast model with new training data");
        
        // 更新各个子模型
        timeSeriesPredictor.updateModel(trainingData);
        mlPredictor.updateModel(trainingData);
        deepLearningPredictor.updateModel(trainingData);
        
        // 更新集成权重
        ensemblePredictor.updateWeights(trainingData);
        
        // 更新元数据
        this.metadata = metadata.toBuilder()
            .lastUpdated(new Date())
            .trainingDataSize(trainingData.size())
            .build();
        
        log.info("Demand forecast model updated successfully");
    }
}
