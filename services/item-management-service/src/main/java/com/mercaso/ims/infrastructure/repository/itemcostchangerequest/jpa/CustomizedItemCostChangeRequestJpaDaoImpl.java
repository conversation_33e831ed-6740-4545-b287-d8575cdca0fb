package com.mercaso.ims.infrastructure.repository.itemcostchangerequest.jpa;

import com.mercaso.ims.application.dto.ItemCostChangeRequestDto;
import com.mercaso.ims.application.query.ItemCostChangeRequestQuery;
import com.mercaso.ims.application.query.ItemCostChangeRequestQuery.SortType;
import com.mercaso.ims.domain.itemcostchangerequest.enums.ItemCostChangeRequestStatus;
import com.mercaso.ims.domain.itemcostchangerequest.enums.MatchedType;
import com.mercaso.ims.infrastructure.repository.DynamicSearchCondition;
import com.mercaso.ims.infrastructure.repository.SearchConditionResolver;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class CustomizedItemCostChangeRequestJpaDaoImpl implements CustomizedItemCostChangeRequestJpaDao {

    private static final String BACKUP_MARGIN_SORT_SQL_DESC =
        """
            ORDER BY
                CASE
                        WHEN COALESCE(iccr.previous_cost, vi.backup_pack_plus_crv_cost) >= 0 THEN
                            (COALESCE(iccr.previous_cost, vi.backup_pack_plus_crv_cost) - iccr.target_cost) / COALESCE(irg.reg_price_plus_crv, irg.reg_price)
                        ELSE NULL
                    END DESC
            """;

    private static final String BACKUP_MARGIN_SORT_SQL_ASC =
        """
            ORDER BY
                CASE
                        WHEN COALESCE(iccr.previous_cost, vi.backup_pack_plus_crv_cost) >= 0 THEN
                            (COALESCE(iccr.previous_cost, vi.backup_pack_plus_crv_cost) - iccr.target_cost) / COALESCE(irg.reg_price_plus_crv, irg.reg_price)
                        ELSE NULL
                    END ASC
            """;

    private static final String MATCH_TYPE_DESC_SORT_SQL = " ORDER BY iccr.match_type DESC";
    private static final String STATUS_DESC_SORT_SQL = " ORDER BY iccr.status DESC";


    private static final String QUERY_ITEM_COST_CHANGE_DTO_LIST = """
             SELECT
                      iccr.id AS item_cost_change_request_id,
                      iccr.item_cost_collection_id,
                      iccr.vendor_id,
                      iccr.item_id,
                      COALESCE(irg.reg_price_plus_crv, irg.reg_price) AS reg_price,
                      COALESCE(iccr.sku_number, i.sku_number) AS sku_number,
                      i.availability_status,
                      iccr.vendor_sku_number,
                      iccr.vendor_item_name,
                      iccr.status,
                      iccr.match_type,
                      iccr.cost_type,
                      COALESCE(iccr.previous_cost, vi.pack_plus_crv_cost, vi.pack_no_crv_cost) AS previous_cost,
                      COALESCE(iccr.previous_cost, vi.backup_pack_plus_crv_cost) AS previous_cost_backup,
                      iccr.target_cost,
                      iccr.tax,
                      iccr.crv,
                      i.primary_vendor_id,
                      i.backup_vendor_id,
                      CASE WHEN i.primary_vendor_id IS NOT NULL AND i.primary_vendor_id = iccr.vendor_id THEN true ELSE false END AS is_primary_vendor,
                      CASE WHEN i.backup_vendor_id IS NOT NULL AND i.backup_vendor_id = iccr.vendor_id THEN true ELSE false END AS is_backup_vendor,
                      iccr.vendor_item_upc
                  FROM
                      item_cost_change_request iccr
                          LEFT JOIN
                      item i ON iccr.item_id = i.id
                          LEFT JOIN
                      item_reg_price irg ON i.id = irg.item_id
                          LEFT JOIN
                      vendor_item vi ON iccr.vendor_id = vi.vendor_id AND iccr.item_id = vi.item_id
                  WHERE
                 iccr.deleted_at  IS NULL
        """;
    private static final String COUNT_QUERY = """
        select  count(iccr.*)  FROM
                      item_cost_change_request iccr
                          LEFT JOIN
                      item i ON iccr.item_id = i.id
                  WHERE
                 iccr.deleted_at  IS NULL
        """;
    private final JdbcTemplate jdbcTemplate;


    @Override
    public List<ItemCostChangeRequestDto> fetchItemCostChangeRequestDtoList(ItemCostChangeRequestQuery itemCostChangeRequestQuery) {
        DynamicSearchCondition<ItemCostChangeRequestQuery> dynamicSearch = new ItemCostCollectionIdDynamicSearch();

        StringBuilder sql = new StringBuilder(QUERY_ITEM_COST_CHANGE_DTO_LIST);
        sql.append(dynamicSearch.generateConditionBlock(itemCostChangeRequestQuery));
        sql.append(this.buildSortSql(itemCostChangeRequestQuery.getSortType()));
        sql.append(itemCostChangeRequestQuery.offsetLimitSql());
        return jdbcTemplate.query(sql.toString(),
            ps -> dynamicSearch.bindSqlParameter(ps, itemCostChangeRequestQuery),
            new RowMapper<ItemCostChangeRequestDto>() {
                @Override
                public ItemCostChangeRequestDto mapRow(ResultSet rs, int rowNum) throws SQLException {
                    ItemCostChangeRequestDto itemCostChangeRequestDto = new ItemCostChangeRequestDto();
                    itemCostChangeRequestDto.setId(getUUID(rs, "item_cost_change_request_id"));
                    itemCostChangeRequestDto.setItemCostCollectionId(getUUID(rs, "item_cost_collection_id"));
                    itemCostChangeRequestDto.setItemId(getUUID(rs, "item_id"));
                    itemCostChangeRequestDto.setVendorId(getUUID(rs, "vendor_id"));
                    itemCostChangeRequestDto.setVendorItemName(rs.getString("vendor_item_name"));
                    itemCostChangeRequestDto.setVendorSkuNumber(rs.getString("vendor_sku_number"));
                    itemCostChangeRequestDto.setMatchType(MatchedType.valueOf(rs.getString("match_type")));
                    itemCostChangeRequestDto.setStatus(ItemCostChangeRequestStatus.valueOf(rs.getString("status")));
                    itemCostChangeRequestDto.setPreviousCost(rs.getBigDecimal("previous_cost_backup"));
                    itemCostChangeRequestDto.setRegPrice(rs.getBigDecimal("reg_price"));
                    itemCostChangeRequestDto.setTargetCost(rs.getBigDecimal("target_cost"));
                    itemCostChangeRequestDto.setTax(rs.getBigDecimal("tax"));
                    itemCostChangeRequestDto.setCrv(rs.getBigDecimal("crv"));
                    itemCostChangeRequestDto.setSkuNumber(rs.getString("sku_number"));
                    itemCostChangeRequestDto.setPrimaryVendorId(getUUID(rs, "primary_vendor_id"));
                    itemCostChangeRequestDto.setIsPrimaryVendor(rs.getBoolean("is_primary_vendor"));
                    itemCostChangeRequestDto.setBackupVendorId(getUUID(rs, "backup_vendor_id"));
                    itemCostChangeRequestDto.setIsBackupVendor(rs.getBoolean("is_backup_vendor"));
                    itemCostChangeRequestDto.setVendorItemUpc(rs.getString("vendor_item_upc"));
                    itemCostChangeRequestDto.setItemStatus(rs.getString("availability_status"));
                    itemCostChangeRequestDto.setCostType(rs.getString("cost_type"));
                    return itemCostChangeRequestDto;
                }
            });
    }

    @Override
    public long countQuery(ItemCostChangeRequestQuery itemCostChangeRequestQuery) {
        DynamicSearchCondition<ItemCostChangeRequestQuery> dynamicSearch = new ItemCostCollectionIdDynamicSearch();
        StringBuilder sql = new StringBuilder(COUNT_QUERY);
        sql.append(dynamicSearch.generateConditionBlock(itemCostChangeRequestQuery));

        List<Long> query = jdbcTemplate.query(sql.toString(),
            ps -> dynamicSearch.bindSqlParameter(ps, itemCostChangeRequestQuery),
            (rs, rowNum) -> rs.getLong(1));

        return query.get(0);
    }

    private UUID getUUID(ResultSet rs, String columnName) throws SQLException {
        String columnValue = rs.getString(columnName);
        return Objects.nonNull(columnValue) ? UUID.fromString(columnValue) : null;
    }

    private String buildSortSql(SortType sort) {
        if (sort == null) {
            return "";
        }
        return switch (sort) {
            case MATCH_TYPE_DESC -> MATCH_TYPE_DESC_SORT_SQL;
            case STATUS_DESC -> STATUS_DESC_SORT_SQL;
            case MARGIN_SORT_DESC -> BACKUP_MARGIN_SORT_SQL_DESC;
            case MARGIN_SORT_ASC -> BACKUP_MARGIN_SORT_SQL_ASC;
            default -> "";
        };
    }

    static class ItemCostCollectionIdDynamicSearch extends DynamicSearchCondition<ItemCostChangeRequestQuery> {

        public ItemCostCollectionIdDynamicSearch() {
            super(List.of(new ItemCostCollectionIdCondition(),
                new ItemCostCollectionStatusCondition(),
                new ItemCostCollectionItemStatusCondition(),
                new ItemCostCollectionItemSkuCondition(),
                new ItemCostCollectionPrimaryCondition(),
                new ItemCostCollectionBackupCondition()
            ));
        }
    }

    private static class ItemCostCollectionIdCondition implements SearchConditionResolver<ItemCostChangeRequestQuery> {

        @Override
        public String generateConditionBlock(ItemCostChangeRequestQuery query) {
            if (null == query.getItemCostCollectionId()) {
                return StringUtils.EMPTY;
            }
            return " AND iccr.item_cost_collection_id = ? ";
        }

        @Override
        public int bindSqlParameter(PreparedStatement ps, ItemCostChangeRequestQuery query, int index) throws SQLException {

            if (null != query.getItemCostCollectionId()) {
                ps.setObject(index++, query.getItemCostCollectionId());
            }
            return index;
        }
    }

    private static class ItemCostCollectionStatusCondition implements SearchConditionResolver<ItemCostChangeRequestQuery> {

        @Override
        public String generateConditionBlock(ItemCostChangeRequestQuery query) {
            if (null == query.getStatus()) {
                return StringUtils.EMPTY;
            }
            return " AND iccr.status = ? ";
        }

        @Override
        public int bindSqlParameter(PreparedStatement ps, ItemCostChangeRequestQuery query, int index) throws SQLException {

            if (null != query.getStatus()) {
                ps.setObject(index++, query.getStatus());
            }
            return index;
        }
    }

    private static class ItemCostCollectionItemStatusCondition implements SearchConditionResolver<ItemCostChangeRequestQuery> {

        @Override
        public String generateConditionBlock(ItemCostChangeRequestQuery query) {
            if (CollectionUtils.isEmpty(query.getItemStatus())) {
                return StringUtils.EMPTY;
            }
            int count = query.getItemStatus().size();
            String placeholders = count > 0 ? String.join(",", java.util.Collections.nCopies(count, "?")) : "";
            return " AND i.availability_status  IN (" + placeholders + ") ";
        }

        @Override
        public int bindSqlParameter(PreparedStatement ps, ItemCostChangeRequestQuery query, int index) throws SQLException {
            if (!CollectionUtils.isEmpty(query.getItemStatus())) {
                List<String> values = query.getItemStatus();
                for (String val : values) {
                    ps.setString(index++, val.trim());
                }
            }
            return index;
        }
    }


    private static class ItemCostCollectionItemSkuCondition implements SearchConditionResolver<ItemCostChangeRequestQuery> {

        @Override
        public String generateConditionBlock(ItemCostChangeRequestQuery query) {
            if (CollectionUtils.isEmpty(query.getItemSkuNumbers())) {
                return StringUtils.EMPTY;
            }
            int count = query.getItemSkuNumbers().size();
            String placeholders = count > 0 ? String.join(",", java.util.Collections.nCopies(count, "?")) : "";
            return " AND iccr.sku_number IN (" + placeholders + ") ";
        }

        @Override
        public int bindSqlParameter(PreparedStatement ps, ItemCostChangeRequestQuery query, int index) throws SQLException {
            if (!CollectionUtils.isEmpty(query.getItemSkuNumbers())) {
                List<String> values = query.getItemSkuNumbers();
                for (String val : values) {
                    ps.setString(index++, val.trim());
                }
            }
            return index;
        }
    }

    @Deprecated
    private static class ItemCostCollectionPrimaryCondition implements SearchConditionResolver<ItemCostChangeRequestQuery> {

        @Override
        public String generateConditionBlock(ItemCostChangeRequestQuery query) {
            if (null == query.getIsPrimaryVendor()) {
                return StringUtils.EMPTY;
            }

            if (Boolean.TRUE.equals(query.getIsPrimaryVendor())) {
                return " AND i.primary_vendor_id = iccr.vendor_id ";
            } else {
                return " AND i.primary_vendor_id != iccr.vendor_id ";
            }
        }

        @Override
        public int bindSqlParameter(PreparedStatement ps, ItemCostChangeRequestQuery query, int index) throws SQLException {
            return index;
        }
    }


    private static class ItemCostCollectionBackupCondition implements SearchConditionResolver<ItemCostChangeRequestQuery> {

        @Override
        public String generateConditionBlock(ItemCostChangeRequestQuery query) {
            if (null == query.getIsBackupVendor()) {
                return StringUtils.EMPTY;
            }

            if (Boolean.TRUE.equals(query.getIsBackupVendor())) {
                return " AND i.backup_vendor_id IS NOT NULL AND i.backup_vendor_id = iccr.vendor_id ";
            } else {
                return " AND (i.backup_vendor_id IS NULL OR i.backup_vendor_id != iccr.vendor_id) ";
            }
        }

        @Override
        public int bindSqlParameter(PreparedStatement ps, ItemCostChangeRequestQuery query, int index) throws SQLException {
            return index;
        }
    }

}
