package com.mercaso.ims.application.dto;

import com.mercaso.ims.domain.vendor.enums.VendorStatus;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class VendorDto extends BaseDto {

    private UUID id;

    private String vendorName;

    private String vendorContactName;

    private String vendorContactTel;

    private String vendorCompanyName;

    private VendorStatus vendorStatus;

    private Boolean externalPicking;

    private String finaleId;

}
