package com.mercaso.ims.domain.businessevent.enums;

import com.mercaso.ims.application.dto.BaseDto;
import com.mercaso.ims.application.dto.event.*;
import com.mercaso.ims.application.dto.payload.*;
import com.mercaso.ims.infrastructure.event.BusinessEventPayloadDto;
import com.mercaso.ims.infrastructure.event.applicationevent.BaseApplicationEvent;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum EventTypeEnums {
    TEST_ORDER_CREATED(TestOrderCreatedPayloadDto.class, TestOrderCreatedApplicationEvent.class),
    ITEM_ADJUSTMENT_REQUEST_CREATED(ItemAdjustmentRequestCreatedPayloadDto.class,
        ItemAdjustmentRequestCreatedApplicationEvent.class),
    ITEM_ADJUSTMENT_REQUEST_DETAIL_CREATED(ItemAdjustmentRequestDetailCreatedPayloadDto.class,
        ItemAdjustmentRequestDetailCreatedApplicationEvent.class),
    ITEM_ADJUSTMENT_REQUEST_DETAIL_COMPLETED(ItemAdjustmentRequestDetailCompletedPayloadDto.class,
        ItemAdjustmentRequestDetailCompletedApplicationEvent.class),
    ITEM_ADJUSTMENT_REQUEST_DETAIL_IMS_UPDATED(ItemAdjustmentRequestDetailImsUpdatedPayloadDto.class,
        ItemAdjustmentRequestDetailImsUpdatedApplicationEvent.class),
    ITEM_ADJUSTMENT_REQUEST_DETAIL_SHOPIFY_SYNCHRONIZED(ItemAdjustmentRequestDetailShopifySynchronizedPayloadDto.class,
        ItemAdjustmentRequestDetailShopifySynchronizedApplicationEvent.class),
    ITEM_ADJUSTMENT_REQUEST_COMPLETED(ItemAdjustmentRequestCompletedPayloadDto.class,
        ItemAdjustmentRequestCompletedApplicationEvent.class),
    ITEM_ADJUSTMENT_REQUEST_PROCESSED(ItemAdjustmentRequestProcessedPayloadDto.class,
        ItemAdjustmentRequestProcessedApplicationEvent.class),
    ITEM_ADJUSTMENT_REQUEST_FAILURE_PROCESSED(ItemAdjustmentFailureProcessedPayloadDto.class,
        ItemAdjustmentRequestFailureProcessedApplicationEvent.class),

    ITEM_CREATED(ItemCreatedPayloadDto.class, ItemCreatedApplicationEvent.class),
    ITEM_AMEND(ItemAmendPayloadDto.class, ItemAmendApplicationEvent.class),
    ITEM_BOUND_TO_PRICE_GROUP(ItemBoundToPriceGroupPayloadDto.class, ItemBoundToPriceGroupApplicationEvent.class),
    ITEM_UNBOUND_FROM_PRICE_GROUP(ItemUnboundFromPriceGroupPayloadDto.class, ItemUnboundFromPriceGroupApplicationEvent.class),
    ITEM_DELETED(ItemDeletedPayloadDto.class, ItemDeletedApplicationEvent.class),
    ITEM_COST_COLLECTION_CREATED(ItemCostCollectionCreatedPayloadDto.class, ItemCostCollectionCreatedEvent.class),
    ITEM_COST_CHANGE_REQUEST_CREATED(ItemCostChangeRequestCreatedPayloadDto.class, ItemCostChangeRequestCreatedEvent.class),
    ITEM_COST_CHANGE_REQUEST_UPDATED(ItemCostChangeRequestUpdatedPayloadDto.class, ItemCostChangeRequestUpdatedEvent.class),
    ANALYZE_VENDOR_PO_RECORD_APPROVED(CreateVendorPoAnalyzeExpenseRecordPayloadDto.class,
        CreateAnalyzeExpenseRecordApplicationEvent.class),
    VENDOR_ITEM_AMEND(VendorItemAmendPayloadDto.class, VendorItemAmendApplicationEvent.class),
    VENDOR_ITEM_CREATED(VendorItemCreatedPayloadDto.class, VendorItemCreatedApplicationEvent.class),
    ITEM_PRICE_GROUP_CREATED(ItemPriceGroupCreatedPayloadDto.class, ItemPriceGroupCreatedApplicationEvent.class),
    ITEM_PRICE_GROUP_AMEND(ItemPriceGroupAmendPayloadDto.class, ItemPriceGroupAmendApplicationEvent.class),
    CATEGORY_CREATED(CategoryCreatedPayloadDto.class, CategoryCreatedApplicationEvent.class),
    CATEGORY_UPDATED(CategoryUpdatedPayloadDto.class, CategoryUpdatedApplicationEvent.class),
    CATEGORY_DELETED(CategoryDeletedPayloadDto.class, CategoryDeletedApplicationEvent.class),
    EXCEPTION_RECORD_CREATED(ExceptionRecordCreatedPayloadDto.class, ExceptionRecordCreatedApplicationEvent.class),
    EXCEPTION_RECORD_CONFIRMED(ExceptionRecordConfirmedPayloadDto.class, ExceptionRecordConfirmedApplicationEvent.class),
    EXCEPTION_RECORD_DISPUTED(ExceptionRecordDisputedPayloadDto.class, ExceptionRecordDisputedApplicationEvent.class),
    BULK_EXPORT_RECORDS(BulkExportRecordsPayloadDto.class, BulkExportRecordsApplicationEvent.class),
    BRAND_UPDATED(BrandUpdatedPayloadDto.class, BrandUpdatedApplicationEvent.class),
    ;

    ;

    private final Class<? extends BusinessEventPayloadDto<? extends BaseDto>> payloadClass;

    private final Class<? extends BaseApplicationEvent<? extends BusinessEventPayloadDto<? extends BaseDto>>> eventClass;

    public static EventTypeEnums forPayload(Class<?> payloadClass) {
        for (EventTypeEnums eventType : values()) {
            if (eventType.getPayloadClass() == payloadClass) {
                return eventType;
            }
        }
        return null;
    }

}
