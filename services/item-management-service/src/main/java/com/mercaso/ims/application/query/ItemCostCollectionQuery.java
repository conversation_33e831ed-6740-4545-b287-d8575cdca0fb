package com.mercaso.ims.application.query;

import com.mercaso.ims.infrastructure.repository.PageQuery;
import java.time.Instant;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@SuperBuilder
public class ItemCostCollectionQuery extends PageQuery {

    private UUID id;

    private Instant createdAtBegin;

    private Instant createdAtEnd;

    private String createdUserName;

    private String createdBy;

    private UUID vendorId;

    private SortType sort;

    @Getter
    public enum SortType {
        CREATED_AT_DESC,
    }
}
