package com.mercaso.ims.domain.vendoritem;

import com.mercaso.ims.domain.Specification;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.ItemRepository;
import com.mercaso.ims.domain.itemregprice.ItemRegPrice;
import com.mercaso.ims.domain.itemregprice.ItemRegPriceRepository;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class VendorItemSpecification implements Specification<ItemRegPrice> {

    private final ItemRepository itemRepository;
    private final ItemRegPriceRepository itemRegPriceRepository;

    public boolean isReasonableMargin(VendorItem vendorItem, BigDecimal newCost) {
        if (vendorItem == null || vendorItem.getItemId() == null) {
            return true;
        }
        if (newCost == null || newCost.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }
        return isReasonableNewMargin(vendorItem.getItemId(), vendorItem.getPackPlusCrvCost(), newCost);
    }

    public boolean isReasonableNewMargin(UUID itemId, BigDecimal originalCost, BigDecimal newCost) {
        String exception = getExceptionMessage(itemId, originalCost, newCost);
        if (exception != null) {
            log.warn("Exception found for SKU :{}", itemId);
            return false;
        }
        return true;
    }

    public String getExceptionMessage(UUID itemId, BigDecimal originalCost, BigDecimal newCost) {
        Item item = itemRepository.findById(itemId);
        if (item == null) {
            log.error("Item not found for id :{}", itemId);
            return null;
        }

        ItemRegPrice price = itemRegPriceRepository.findByItemId(item.getId());
        if (price == null || originalCost == null || newCost == null) {
            log.warn("VendorItem cost or ItemRegPrice not found for SKU :{}", item.getSkuNumber());
            return null;
        }

        BigDecimal originalMargin = calculateMargin(price.getRegPrice(), originalCost);
        BigDecimal newMargin = calculateMargin(price.getRegPrice(), newCost);

        BigDecimal marginChange = newMargin.subtract(originalMargin);
        if (marginChange.abs().compareTo(BigDecimal.valueOf(0.2)) > 0) {
            return MessageFormat.format(
                "New margin for SKU {0} is {1}, and the rate of change of the margin is higher than 20%.",
                item.getSkuNumber(), newMargin
            );
        }

        return null;
    }


    private BigDecimal calculateMargin(BigDecimal regPrice, BigDecimal cost) {
        return regPrice.subtract(cost).divide(regPrice, 2, RoundingMode.HALF_UP);
    }

}
