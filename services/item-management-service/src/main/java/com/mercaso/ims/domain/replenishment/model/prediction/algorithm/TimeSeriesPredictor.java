package com.mercaso.ims.domain.replenishment.model.prediction.algorithm;

import com.mercaso.ims.domain.replenishment.model.feature.FeatureVector;
import com.mercaso.ims.domain.replenishment.model.prediction.DemandForecast;
import com.mercaso.ims.domain.replenishment.model.prediction.TrainingData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.IntStream;

/**
 * 时间序列预测器
 * 
 * 支持多种时间序列预测算法：
 * 1. ARIMA (自回归积分滑动平均模型)
 * 2. 指数平滑法 (Exponential Smoothing)
 * 3. 季节性分解 (Seasonal Decomposition)
 * 4. 移动平均法 (Moving Average)
 */
@Slf4j
@Component
public class TimeSeriesPredictor {
    
    private static final int DEFAULT_FORECAST_HORIZON = 7; // 默认预测7天
    private static final double ALPHA = 0.3; // 指数平滑参数
    private static final double BETA = 0.1;  // 趋势平滑参数
    private static final double GAMMA = 0.1; // 季节性平滑参数
    
    /**
     * 执行时间序列预测
     */
    public DemandForecast predict(FeatureVector features) {
        log.debug("Starting time series prediction for item: {}", features.getItemId());
        
        try {
            // 提取历史销售数据
            List<Double> historicalSales = extractHistoricalSales(features);
            
            if (historicalSales.isEmpty() || historicalSales.size() < 7) {
                return createSimpleForecast(features);
            }
            
            // 选择最适合的时间序列方法
            TimeSeriesMethod method = selectOptimalMethod(historicalSales, features);
            
            // 执行预测
            DemandForecast forecast = switch (method) {
                case EXPONENTIAL_SMOOTHING -> predictWithExponentialSmoothing(historicalSales, features);
                case SEASONAL_DECOMPOSITION -> predictWithSeasonalDecomposition(historicalSales, features);
                case MOVING_AVERAGE -> predictWithMovingAverage(historicalSales, features);
                case ARIMA -> predictWithARIMA(historicalSales, features);
                default -> predictWithExponentialSmoothing(historicalSales, features);
            };
            
            log.debug("Time series prediction completed: method={}, prediction={}", 
                method, forecast.getPredictedDemand());
            
            return forecast;
            
        } catch (Exception e) {
            log.error("Error in time series prediction", e);
            return createFallbackForecast(features);
        }
    }
    
    /**
     * 选择最优的时间序列方法
     */
    private TimeSeriesMethod selectOptimalMethod(List<Double> historicalSales, FeatureVector features) {
        // 1. 检查数据长度
        if (historicalSales.size() < 14) {
            return TimeSeriesMethod.MOVING_AVERAGE;
        }
        
        // 2. 检查季节性
        boolean hasSeasonality = features.getFeature("has_seasonality") > 0.5;
        if (hasSeasonality && historicalSales.size() >= 28) {
            return TimeSeriesMethod.SEASONAL_DECOMPOSITION;
        }
        
        // 3. 检查趋势强度
        double trendStrength = calculateTrendStrength(historicalSales);
        if (trendStrength > 0.3) {
            return TimeSeriesMethod.EXPONENTIAL_SMOOTHING;
        }
        
        // 4. 检查数据复杂性
        double complexity = calculateDataComplexity(historicalSales);
        if (complexity > 0.5 && historicalSales.size() >= 30) {
            return TimeSeriesMethod.ARIMA;
        }
        
        // 5. 默认使用指数平滑
        return TimeSeriesMethod.EXPONENTIAL_SMOOTHING;
    }
    
    /**
     * 指数平滑预测
     */
    private DemandForecast predictWithExponentialSmoothing(List<Double> historicalSales, FeatureVector features) {
        // Holt-Winters 三重指数平滑
        int n = historicalSales.size();
        int seasonLength = 7; // 假设周季节性
        
        // 初始化参数
        double[] level = new double[n];
        double[] trend = new double[n];
        double[] seasonal = new double[n];
        
        // 初始值计算
        level[0] = historicalSales.get(0);
        trend[0] = (historicalSales.get(1) - historicalSales.get(0));
        
        // 初始季节性因子
        for (int i = 0; i < Math.min(seasonLength, n); i++) {
            seasonal[i] = historicalSales.get(i) / level[0];
        }
        
        // 递推计算
        for (int i = 1; i < n; i++) {
            double prevLevel = level[i - 1];
            double prevTrend = trend[i - 1];
            double prevSeasonal = i >= seasonLength ? seasonal[i - seasonLength] : 1.0;
            
            level[i] = ALPHA * (historicalSales.get(i) / prevSeasonal) + 
                      (1 - ALPHA) * (prevLevel + prevTrend);
            trend[i] = BETA * (level[i] - prevLevel) + (1 - BETA) * prevTrend;
            
            if (i >= seasonLength) {
                seasonal[i] = GAMMA * (historicalSales.get(i) / level[i]) + 
                             (1 - GAMMA) * prevSeasonal;
            }
        }
        
        // 预测
        double prediction = (level[n - 1] + trend[n - 1]) * 
                           (n >= seasonLength ? seasonal[n - seasonLength] : 1.0);
        
        // 计算预测区间
        double mse = calculateMSE(historicalSales, level, trend, seasonal, seasonLength);
        double standardError = Math.sqrt(mse);
        
        // 生成预测序列
        List<Double> forecastHorizon = generateForecastHorizon(
            level[n - 1], trend[n - 1], seasonal, seasonLength, DEFAULT_FORECAST_HORIZON);
        
        return DemandForecast.builder()
            .predictedDemand(Math.max(0, prediction))
            .confidence(calculateConfidence(mse, historicalSales))
            .lowerBound(Math.max(0, prediction - 1.96 * standardError))
            .upperBound(prediction + 1.96 * standardError)
            .forecastHorizon(forecastHorizon)
            .method("exponential_smoothing")
            .standardError(standardError)
            .metadata(Map.of(
                "alpha", ALPHA,
                "beta", BETA,
                "gamma", GAMMA,
                "mse", mse
            ))
            .build();
    }
    
    /**
     * 季节性分解预测
     */
    private DemandForecast predictWithSeasonalDecomposition(List<Double> historicalSales, FeatureVector features) {
        int seasonLength = 7; // 周季节性
        
        // 分解时间序列
        TimeSeriesDecomposition decomposition = decomposeTimeSeries(historicalSales, seasonLength);
        
        // 预测趋势
        double trendPrediction = predictTrend(decomposition.getTrend());
        
        // 获取季节性因子
        double seasonalFactor = getSeasonalFactor(decomposition.getSeasonal(), seasonLength);
        
        // 组合预测
        double prediction = trendPrediction * seasonalFactor;
        
        // 计算置信区间
        double residualStd = calculateStandardDeviation(decomposition.getResidual());
        
        return DemandForecast.builder()
            .predictedDemand(Math.max(0, prediction))
            .confidence(calculateSeasonalConfidence(decomposition))
            .lowerBound(Math.max(0, prediction - 1.96 * residualStd))
            .upperBound(prediction + 1.96 * residualStd)
            .forecastHorizon(generateSeasonalForecast(decomposition, DEFAULT_FORECAST_HORIZON))
            .method("seasonal_decomposition")
            .seasonalityFactor(seasonalFactor)
            .trendFactor(trendPrediction)
            .standardError(residualStd)
            .build();
    }
    
    /**
     * 移动平均预测
     */
    private DemandForecast predictWithMovingAverage(List<Double> historicalSales, FeatureVector features) {
        int windowSize = Math.min(7, historicalSales.size());
        
        // 计算移动平均
        double sum = historicalSales.subList(historicalSales.size() - windowSize, historicalSales.size())
            .stream().mapToDouble(Double::doubleValue).sum();
        double prediction = sum / windowSize;
        
        // 计算标准差
        double variance = historicalSales.subList(historicalSales.size() - windowSize, historicalSales.size())
            .stream().mapToDouble(x -> Math.pow(x - prediction, 2)).average().orElse(0.0);
        double standardError = Math.sqrt(variance);
        
        return DemandForecast.builder()
            .predictedDemand(Math.max(0, prediction))
            .confidence(0.6) // 移动平均置信度中等
            .lowerBound(Math.max(0, prediction - 1.96 * standardError))
            .upperBound(prediction + 1.96 * standardError)
            .forecastHorizon(Collections.nCopies(DEFAULT_FORECAST_HORIZON, prediction))
            .method("moving_average")
            .standardError(standardError)
            .metadata(Map.of("window_size", windowSize))
            .build();
    }
    
    /**
     * ARIMA预测（简化版本）
     */
    private DemandForecast predictWithARIMA(List<Double> historicalSales, FeatureVector features) {
        // 这里实现简化的ARIMA模型
        // 实际生产环境中应该使用专业的时间序列库
        
        // 差分处理
        List<Double> diffSeries = calculateDifference(historicalSales);
        
        // 自回归预测
        double prediction = predictWithAutoRegression(diffSeries, 3);
        
        // 还原差分
        prediction += historicalSales.get(historicalSales.size() - 1);
        
        // 计算预测误差
        double mse = calculateARIMAError(historicalSales);
        double standardError = Math.sqrt(mse);
        
        return DemandForecast.builder()
            .predictedDemand(Math.max(0, prediction))
            .confidence(calculateARIMAConfidence(mse, historicalSales))
            .lowerBound(Math.max(0, prediction - 1.96 * standardError))
            .upperBound(prediction + 1.96 * standardError)
            .forecastHorizon(generateARIMAForecast(historicalSales, DEFAULT_FORECAST_HORIZON))
            .method("arima")
            .standardError(standardError)
            .build();
    }
    
    // 辅助方法实现...
    
    private List<Double> extractHistoricalSales(FeatureVector features) {
        // 从特征向量中提取历史销售数据
        List<Double> sales = new ArrayList<>();
        
        // 提取最近30天的销售数据
        for (int i = 1; i <= 30; i++) {
            String featureName = "daily_sales_" + i + "d_ago";
            if (features.hasFeature(featureName)) {
                sales.add(0, features.getFeature(featureName)); // 倒序添加，最新的在最后
            }
        }
        
        return sales;
    }
    
    private double calculateTrendStrength(List<Double> data) {
        if (data.size() < 3) return 0.0;
        
        // 计算线性回归斜率作为趋势强度
        int n = data.size();
        double sumX = n * (n - 1) / 2.0;
        double sumY = data.stream().mapToDouble(Double::doubleValue).sum();
        double sumXY = IntStream.range(0, n).mapToDouble(i -> i * data.get(i)).sum();
        double sumX2 = n * (n - 1) * (2 * n - 1) / 6.0;
        
        double slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
        double avgY = sumY / n;
        
        return Math.abs(slope) / (avgY + 1); // 标准化趋势强度
    }
    
    private double calculateDataComplexity(List<Double> data) {
        // 计算数据的复杂性（变异系数）
        double mean = data.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double variance = data.stream().mapToDouble(x -> Math.pow(x - mean, 2)).average().orElse(0.0);
        double std = Math.sqrt(variance);
        
        return mean > 0 ? std / mean : 0.0;
    }
    
    private DemandForecast createSimpleForecast(FeatureVector features) {
        double avgSales = features.getFeature("avg_daily_sales_7d");
        return DemandForecast.builder()
            .predictedDemand(avgSales)
            .confidence(0.5)
            .lowerBound(avgSales * 0.7)
            .upperBound(avgSales * 1.3)
            .forecastHorizon(Collections.nCopies(DEFAULT_FORECAST_HORIZON, avgSales))
            .method("simple_average")
            .build();
    }
    
    private DemandForecast createFallbackForecast(FeatureVector features) {
        double fallbackValue = features.getFeature("avg_daily_sales_30d");
        return DemandForecast.builder()
            .predictedDemand(fallbackValue)
            .confidence(0.3)
            .lowerBound(fallbackValue * 0.5)
            .upperBound(fallbackValue * 1.5)
            .forecastHorizon(Collections.nCopies(DEFAULT_FORECAST_HORIZON, fallbackValue))
            .method("fallback")
            .build();
    }
    
    private double calculateMSE(List<Double> actual, double[] level, double[] trend, double[] seasonal, int seasonLength) {
        double sumSquaredError = 0.0;
        int n = actual.size();

        for (int i = 1; i < n; i++) {
            double predicted = (level[i-1] + trend[i-1]) *
                              (i >= seasonLength ? seasonal[i - seasonLength] : 1.0);
            sumSquaredError += Math.pow(actual.get(i) - predicted, 2);
        }

        return sumSquaredError / (n - 1);
    }

    private double calculateConfidence(double mse, List<Double> historicalSales) {
        double mean = historicalSales.stream().mapToDouble(Double::doubleValue).average().orElse(1.0);
        double rmse = Math.sqrt(mse);
        double cv = rmse / (mean + 1); // 变异系数

        return Math.max(0.1, Math.min(0.95, 1.0 - cv));
    }

    private List<Double> generateForecastHorizon(double level, double trend, double[] seasonal,
                                               int seasonLength, int horizon) {
        List<Double> forecast = new ArrayList<>();
        for (int i = 1; i <= horizon; i++) {
            double seasonalFactor = seasonal.length >= seasonLength ?
                seasonal[seasonal.length - seasonLength + (i - 1) % seasonLength] : 1.0;
            forecast.add(Math.max(0, (level + i * trend) * seasonalFactor));
        }
        return forecast;
    }

    private TimeSeriesDecomposition decomposeTimeSeries(List<Double> data, int seasonLength) {
        int n = data.size();
        List<Double> trend = new ArrayList<>();
        List<Double> seasonal = new ArrayList<>();
        List<Double> residual = new ArrayList<>();

        // 简化的分解方法
        for (int i = 0; i < n; i++) {
            // 趋势计算（移动平均）
            int start = Math.max(0, i - seasonLength/2);
            int end = Math.min(n, i + seasonLength/2 + 1);
            double trendValue = data.subList(start, end).stream()
                .mapToDouble(Double::doubleValue).average().orElse(0.0);
            trend.add(trendValue);

            // 季节性计算
            double seasonalValue = trendValue > 0 ? data.get(i) / trendValue : 1.0;
            seasonal.add(seasonalValue);

            // 残差
            residual.add(data.get(i) - trendValue * seasonalValue);
        }

        return new TimeSeriesDecomposition(trend, seasonal, residual);
    }

    private double predictTrend(List<Double> trend) {
        if (trend.size() < 2) return trend.isEmpty() ? 0.0 : trend.get(0);

        // 线性外推
        int n = trend.size();
        double lastTrend = trend.get(n - 1);
        double secondLastTrend = trend.get(n - 2);

        return lastTrend + (lastTrend - secondLastTrend);
    }

    private double getSeasonalFactor(List<Double> seasonal, int seasonLength) {
        if (seasonal.isEmpty()) return 1.0;

        int n = seasonal.size();
        int index = (n - 1) % seasonLength;
        return seasonal.get(Math.max(0, n - seasonLength + index));
    }

    private double calculateStandardDeviation(List<Double> data) {
        double mean = data.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double variance = data.stream().mapToDouble(x -> Math.pow(x - mean, 2)).average().orElse(0.0);
        return Math.sqrt(variance);
    }

    private double calculateSeasonalConfidence(TimeSeriesDecomposition decomposition) {
        double residualStd = calculateStandardDeviation(decomposition.getResidual());
        double trendStd = calculateStandardDeviation(decomposition.getTrend());

        // 基于残差和趋势的稳定性计算置信度
        return Math.max(0.1, Math.min(0.9, 1.0 - (residualStd / (trendStd + 1))));
    }

    private List<Double> generateSeasonalForecast(TimeSeriesDecomposition decomposition, int horizon) {
        List<Double> forecast = new ArrayList<>();
        List<Double> trend = decomposition.getTrend();
        List<Double> seasonal = decomposition.getSeasonal();

        for (int i = 1; i <= horizon; i++) {
            double trendValue = predictTrend(trend);
            double seasonalValue = getSeasonalFactor(seasonal, 7); // 假设周季节性
            forecast.add(Math.max(0, trendValue * seasonalValue));
        }

        return forecast;
    }

    private List<Double> calculateDifference(List<Double> data) {
        List<Double> diff = new ArrayList<>();
        for (int i = 1; i < data.size(); i++) {
            diff.add(data.get(i) - data.get(i - 1));
        }
        return diff;
    }

    private double predictWithAutoRegression(List<Double> data, int order) {
        if (data.size() < order) return 0.0;

        // 简化的AR模型
        double prediction = 0.0;
        double[] coefficients = {0.5, 0.3, 0.2}; // 简化的AR系数

        for (int i = 0; i < Math.min(order, data.size()); i++) {
            prediction += coefficients[i] * data.get(data.size() - 1 - i);
        }

        return prediction;
    }

    private double calculateARIMAError(List<Double> data) {
        // 简化的ARIMA误差计算
        double mean = data.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        return data.stream().mapToDouble(x -> Math.pow(x - mean, 2)).average().orElse(1.0);
    }

    private double calculateARIMAConfidence(double mse, List<Double> data) {
        double mean = data.stream().mapToDouble(Double::doubleValue).average().orElse(1.0);
        double rmse = Math.sqrt(mse);
        return Math.max(0.1, Math.min(0.9, 1.0 - rmse / (mean + 1)));
    }

    private List<Double> generateARIMAForecast(List<Double> data, int horizon) {
        List<Double> forecast = new ArrayList<>();
        double lastValue = data.get(data.size() - 1);

        for (int i = 0; i < horizon; i++) {
            // 简化的ARIMA预测
            forecast.add(Math.max(0, lastValue));
        }

        return forecast;
    }

    public void updateModel(TrainingData trainingData) {
        log.info("Updating time series predictor with {} training samples", trainingData.size());
        // 实现模型更新逻辑
        // 1. 重新估计模型参数
        // 2. 验证模型性能
        // 3. 更新预测精度统计
    }
    
    /**
     * 时间序列预测方法枚举
     */
    private enum TimeSeriesMethod {
        EXPONENTIAL_SMOOTHING,
        SEASONAL_DECOMPOSITION,
        MOVING_AVERAGE,
        ARIMA
    }
    
    /**
     * 时间序列分解结果
     */
    private static class TimeSeriesDecomposition {
        private final List<Double> trend;
        private final List<Double> seasonal;
        private final List<Double> residual;
        
        public TimeSeriesDecomposition(List<Double> trend, List<Double> seasonal, List<Double> residual) {
            this.trend = trend;
            this.seasonal = seasonal;
            this.residual = residual;
        }
        
        public List<Double> getTrend() { return trend; }
        public List<Double> getSeasonal() { return seasonal; }
        public List<Double> getResidual() { return residual; }
    }
}
