package com.mercaso.ims.infrastructure.excel.listener;

import static com.mercaso.ims.infrastructure.util.FormatUtils.cleanInput;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.application.command.CreateItemAdjustmentRequestDetailCommand;
import com.mercaso.ims.application.service.CategoryApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestDetailApplicationService;
import com.mercaso.ims.domain.brand.BrandRepository;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.ItemRepository;
import com.mercaso.ims.domain.item.enums.AvailabilityStatus;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentFailureReason;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentType;
import com.mercaso.ims.domain.vendor.VendorRepository;
import com.mercaso.ims.domain.vendoritem.VendorItemRepository;
import com.mercaso.ims.infrastructure.excel.data.CreateOrUpdateItemRequestData;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class CreateItemRequestDataListener extends ItemAdjustmentRequestDataListener<CreateOrUpdateItemRequestData> {


    public CreateItemRequestDataListener(UUID itemAdjustmentRequestId,
        ItemAdjustmentRequestDetailApplicationService itemAdjustmentRequestDetailApplicationService,
        ItemAdjustmentRequestApplicationService itemAdjustmentRequestApplicationService,
        ItemRepository itemRepository,
        VendorRepository vendorRepository,
        VendorItemRepository vendorItemRepository,
        CategoryApplicationService categoryApplicationService,
        FeatureFlagsManager featureFlagsManager,
        BrandRepository brandRepository) {
        super(itemAdjustmentRequestId,
            itemAdjustmentRequestDetailApplicationService,
            itemAdjustmentRequestApplicationService,
            itemRepository,
            vendorRepository,
            vendorItemRepository,
            categoryApplicationService,
            featureFlagsManager,
            brandRepository);
    }

    @Override
    CreateItemAdjustmentRequestDetailCommand convertToCreateItemAdjustmentRequestDetailCommand(CreateOrUpdateItemRequestData createItemRequestData) {
        return CreateItemAdjustmentRequestDetailCommand.builder()
            .requestId(itemAdjustmentRequestId)
            .type(getItemAdjustmentType())
            .sku(cleanInput(createItemRequestData.getSku()))
            .companyId(createItemRequestData.getCompanyId())
            .locationId(createItemRequestData.getLocationId())
            .newDescription(createItemRequestData.getNewDescription())
            .aisle(createItemRequestData.getPrimaryVendorItemAisle())
            .itemStatus(createItemRequestData.getStatus() == null ? null
                : AvailabilityStatus.fromString(createItemRequestData.getStatus()))
            .primaryPoVendor(createItemRequestData.getPrimaryPoVendor())
            .primaryJitVendor(createItemRequestData.getPrimaryJitVendor())
            .title(createItemRequestData.getItemDescription())
            .packageSize(createItemRequestData.getPackSize())
            .itemSize(createItemRequestData.getItemSize())
            .itemUnitMeasure(createItemRequestData.getItemUnitMeasure())
            .department(createItemRequestData.getDepartment())
            .category(createItemRequestData.getCategory())
            .subCategory(createItemRequestData.getSubCategory())
            .classType(createItemRequestData.getClassType())
            .brand(createItemRequestData.getBrand())
            .regPricePackNoCrv(createItemRequestData.getRegPricePack())
            .caseUpc(createItemRequestData.getCaseUpc())
            .eachUpc(createItemRequestData.getEachUpc())
            .vendorItemNumber(createItemRequestData.getPrimaryVendorItemNumber())
            .primaryPoVendorItemCost(createItemRequestData.getPrimaryPoVendorItemCost())
            .primaryJitVendorItemCost(createItemRequestData.getPrimaryJitVendorItemCost())
            .promoFlag(createItemRequestData.getPromoFlag())
            .promoPricePackNoCrv(createItemRequestData.getPromoPrice())
            .crvFlag(createItemRequestData.getCrvFlag())
            .imageUrl(createItemRequestData.getImageUrl())
            .tags(createItemRequestData.getTags())
            .disposition(createItemRequestData.getNotes())
            .length(createItemRequestData.getLength())
            .height(createItemRequestData.getHeight())
            .width(createItemRequestData.getWidth())
            .caseWeight(createItemRequestData.getCaseWeight())
            .caseWeightUnit(createItemRequestData.getCaseWeightUnit())
            .eachWeight(createItemRequestData.getEachWeight())
            .eachWeightUnit(createItemRequestData.getEachWeightUnit())
            .cooler(createItemRequestData.getCooler())
            .build();

    }

    @Override
    List<ItemAdjustmentFailureReason> validateInput(CreateOrUpdateItemRequestData createItemRequestData) {
        List<ItemAdjustmentFailureReason> failureReasons = new ArrayList<>();
        String sku = cleanInput(createItemRequestData.getSku());
        if (StringUtils.isBlank(sku)) {
            failureReasons.add(ItemAdjustmentFailureReason.SKU_IS_REQUIRED);
        }

        if (StringUtils.isNotBlank(sku)) {
            if (sku.contains(",") || sku.chars().anyMatch(Character::isWhitespace)) {
                failureReasons.add(ItemAdjustmentFailureReason.INVALID_SKU_FORMAT);
            }
            Item item = itemRepository.findBySku(sku);
            if (item != null) {
                failureReasons.add(ItemAdjustmentFailureReason.ITEM_ALREADY_EXISTS);
            }
        }

        if (StringUtils.isBlank(createItemRequestData.getStatus())) {
            failureReasons.add(ItemAdjustmentFailureReason.INVALID_STATUS);
        }

        if (StringUtils.isBlank(createItemRequestData.getPrimaryPoVendor())
            && StringUtils.isBlank(createItemRequestData.getPrimaryJitVendor())) {
            failureReasons.add(ItemAdjustmentFailureReason.PRIMARY_VENDOR_AND_BACKUP_VENDOR_CANNOT_BE_EMPTY);
        }
        if (StringUtils.isNoneBlank(createItemRequestData.getPrimaryPoVendor())
            && null == createItemRequestData.getPrimaryPoVendorItemCost()) {
            failureReasons.add(ItemAdjustmentFailureReason.PRIMARY_DIRECT_VENDOR_ITEM_COST_IS_REQUIRED);
        }
        if (StringUtils.isNoneBlank(createItemRequestData.getPrimaryJitVendor())
            && null == createItemRequestData.getPrimaryJitVendorItemCost()) {
            failureReasons.add(ItemAdjustmentFailureReason.PRIMARY_JIT_VENDOR_ITEM_COST_IS_REQUIRED);
        }

        if (StringUtils.isBlank(createItemRequestData.getItemDescription())) {
            failureReasons.add(ItemAdjustmentFailureReason.INVALID_ITEM_DESCRIPTION);
        }
        if (StringUtils.isBlank(createItemRequestData.getNewDescription())) {
            failureReasons.add(ItemAdjustmentFailureReason.NEW_DESCRIPTION_IS_REQUIRED);
        }
        if (!createItemRequestData.isCategoryChanged()) {
            failureReasons.add(ItemAdjustmentFailureReason.DEPARTMENT_NOT_FOUND);
        }
        ItemAdjustmentFailureReason validateCRVFlagResult = validateCRVFlag(createItemRequestData.getDepartment(),
            createItemRequestData.getCrvFlag());
        if (validateCRVFlagResult != null) {
            failureReasons.add(validateCRVFlagResult);
        }

        List<ItemAdjustmentFailureReason> newTemplateInputCheckResult = newTemplateInputCheck(createItemRequestData);
        failureReasons.addAll(newTemplateInputCheckResult);
        return failureReasons;
    }


    @Override
    ItemAdjustmentType getItemAdjustmentType() {
        return ItemAdjustmentType.CREATE;
    }

    @Override
    protected ItemAdjustmentFailureReason validateCategory(String department,
        String category,
        String subCategory,
        String clazz) {
        // Use feature flag to determine which validation method to use
        if (StringUtils.isAnyBlank(department, category, subCategory, clazz)) {
            return ItemAdjustmentFailureReason.TAXONOMY_IS_REQUIRED;
        }

        return super.validateCategory(department, category, subCategory, clazz);
    }
}
