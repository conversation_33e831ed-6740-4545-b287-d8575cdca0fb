package com.mercaso.ims.infrastructure.schedule;


import com.mercaso.ims.application.command.CreateItemCostCollectionCommand;
import com.mercaso.ims.application.service.ItemCostCollectionApplicationService;
import com.mercaso.ims.domain.itemcostcollection.ItemCostCollection;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionSources;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionTypes;
import com.mercaso.ims.domain.itemcostcollection.service.ItemCostCollectionService;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.infrastructure.config.PgAdvisoryLock;
import com.mercaso.ims.infrastructure.external.finale.FinaleExternalApiAdaptor;
import com.mercaso.ims.infrastructure.external.finale.dto.QueryFinalePurchaseOrderResultDto.PurchaseOrderPageInfo;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
@RequiredArgsConstructor
public class FinalePurchaseOderScheduler {

    private static final Integer SYNC_FINALE_PURCHASE_ORDER_LOCK_KEY = "[FinalePurchaseOderScheduler.syncFinalePurchaseOrders]".hashCode();

    private final PgAdvisoryLock pgAdvisoryLock;
    private final VendorService vendorService;
    private final ItemCostCollectionApplicationService itemCostCollectionApplicationService;
    private final FinaleExternalApiAdaptor finaleExternalApiAdaptor;
    private final ItemCostCollectionService itemCostCollectionService;
    private final EntityManagerFactory managerFactory;

    @Scheduled(fixedRate = 60 * 60 * 1000)
    public void syncFinalePurchaseOrders() {
        log.info(" Starting [SyncFinalePO] Task");
        EntityManager entityManager = managerFactory.createEntityManager();
        try {
            if (!tryAcquireLock(entityManager)) {
                log.warn("[SyncFinalePO] Already in progress, exiting...");
                return;
            }

            List<PurchaseOrderPageInfo> poList = finaleExternalApiAdaptor.queryPurchaseOrders(40);
            if (CollectionUtils.isEmpty(poList)) {
                log.info("[SyncFinalePO] No purchase orders found from Finale");
                return;
            }

            poList.stream()
                .filter(PurchaseOrderPageInfo::isReceived)
                .filter(purchaseOrderPageInfo -> !purchaseOrderPageInfo.isJitPurchaseOrder())
                .filter(this::isValidPurchaseOrder)
                .forEach(this::processPurchaseOrder);

        } catch (Exception e) {
            log.error("[SyncFinalePO] Exception occurred: {}", e.getMessage(), e);
        } finally {
            releaseLock(entityManager);
            entityManager.close();
            log.info("Finished [SyncFinalePO] Task ");
        }

    }

    private boolean tryAcquireLock(EntityManager entityManager) {
        return Boolean.TRUE.equals(pgAdvisoryLock.tryLockWithSessionLevel(
            entityManager, SYNC_FINALE_PURCHASE_ORDER_LOCK_KEY, "SyncFinalePO"));
    }

    private void releaseLock(EntityManager entityManager) {
        pgAdvisoryLock.unLock(entityManager, SYNC_FINALE_PURCHASE_ORDER_LOCK_KEY, "Unlock SyncFinalePO");
    }

    private boolean isValidPurchaseOrder(PurchaseOrderPageInfo po) {
        return StringUtils.isNotBlank(po.getOrderId())
            && po.getPartySupplierGroupName() != null
            && StringUtils.isNotBlank(po.getPartySupplierGroupName().getPartyId());
    }

    private void processPurchaseOrder(PurchaseOrderPageInfo po) {
        String orderId = po.getOrderId();
        String vendorFinaleId = po.getPartySupplierGroupName().getPartyId();
        log.info("[SyncFinalePO] Processing orderId: {}, vendorFinaleId: {}", orderId, vendorFinaleId);
        createPOItemCostCollection(vendorFinaleId, orderId);
    }

    private void createPOItemCostCollection(String vendorFinaleId, String purchaseOrderNumber) {
        log.info("[SyncFinalePO] Creating item cost collection for Finale PO: {}", purchaseOrderNumber);
        Vendor vendor = vendorService.findByFinaleId(vendorFinaleId);
        if (vendor == null) {
            log.warn("[SyncFinalePO] Vendor not found for finaleId: {}", vendorFinaleId);
            return;
        }

        List<ItemCostCollection> itemCostCollections = itemCostCollectionService.findByVendorId(vendor.getId());
        if (itemCostCollections.isEmpty() || itemCostCollections.stream()
            .noneMatch(itemCostCollection -> itemCostCollection.getVendorCollectionNumber()
                .equals(purchaseOrderNumber))) {
            CreateItemCostCollectionCommand command = CreateItemCostCollectionCommand.builder()
                .vendorId(vendor.getId())
                .vendorName(vendor.getVendorName())
                .source(ItemCostCollectionSources.FINALE_PURCHASE_ORDER)
                .type(ItemCostCollectionTypes.ON_LINE_PURCHASE_ORDER)
                .vendorCollectionNumber(purchaseOrderNumber)
                .fileName(purchaseOrderNumber)
                .build();
            log.info("[SyncFinalePO] Creating item cost collection : {}", command);

            itemCostCollectionApplicationService.create(command);
        }

    }
}