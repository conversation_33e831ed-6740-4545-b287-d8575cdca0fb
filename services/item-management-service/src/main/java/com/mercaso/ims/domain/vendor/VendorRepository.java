package com.mercaso.ims.domain.vendor;

import com.mercaso.ims.domain.BaseDomainRepository;
import java.util.List;
import java.util.UUID;

public interface VendorRepository extends BaseDomainRepository<Vendor, UUID> {

    Vendor findByVendorName(String vendorName);

    Vendor findByFinaleId(String finaleId);

    List<Vendor> findAllByIdIn(List<UUID> ids);

    List<Vendor> findAll();

    List<Vendor> findByFuzzyName(String vendorName);

    List<Vendor> findByExternalPicking(Boolean externalPicking);
}
