package com.mercaso.ims.application.command;

import com.mercaso.ims.application.BaseCommand;
import com.mercaso.ims.application.dto.ItemUPCDto;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
public class UpdateItemUpcCommand extends BaseCommand {


    @NotNull(message = "Item ID must not be null.")
    private UUID itemId;

    private List<ItemUPCDto> itemUPCs;

}