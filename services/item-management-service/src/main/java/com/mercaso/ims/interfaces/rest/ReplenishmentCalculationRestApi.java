package com.mercaso.ims.interfaces.rest;

import com.mercaso.ims.application.command.CalculateReplenishmentCommand;
import com.mercaso.ims.application.dto.ReplenishmentRecommendationDto;
import com.mercaso.ims.application.service.ReplenishmentApplicationService;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping(value = "/v1/replenishment-calculation", produces = {MediaType.APPLICATION_JSON_VALUE})
@RequiredArgsConstructor
public class ReplenishmentCalculationRestApi {

    private final ReplenishmentApplicationService replenishmentApplicationService;

    @PostMapping("/calculate")
    @PreAuthorize("hasAuthority('ims:write:replenishment')")
    public List<ReplenishmentRecommendationDto> calculate(@RequestBody Map<String, Object> body) {
        @SuppressWarnings("unchecked") List<String> itemIdStrs = (List<String>) body.getOrDefault("itemIds", List.of());
        boolean onlyReplenishmentNeeded = Boolean.TRUE.equals(body.get("onlyReplenishmentNeeded"));
        boolean includeDetails = Boolean.TRUE.equals(body.get("includeDetails")); // currently not used; reserved

        List<UUID> itemIds = itemIdStrs.stream().map(UUID::fromString).toList();
        List<ReplenishmentRecommendationDto> results = replenishmentApplicationService.calculateReplenishmentBatch(itemIds);
        if (onlyReplenishmentNeeded) {
            results = results.stream().filter(dto -> Boolean.TRUE.equals(dto.getReorderFlag())).toList();
        }
        return results;
    }

    @GetMapping("/item/{itemId}")
    @PreAuthorize("hasAuthority('ims:read:replenishment')")
    public ReplenishmentRecommendationDto calculateByItem(@PathVariable UUID itemId,
                                                          @RequestParam(defaultValue = "true") boolean useDefaultConfig,
                                                          @RequestParam(defaultValue = "false") boolean includeDetails) {
        // Redirect to existing calculate flow
        CalculateReplenishmentCommand cmd = CalculateReplenishmentCommand.builder()
            .itemId(itemId)
            .forceRecalculation(true)
            .build();
        return replenishmentApplicationService.calculateReplenishment(cmd);
    }

    @GetMapping("/sku/{skuNumber}")
    @PreAuthorize("hasAuthority('ims:read:replenishment')")
    public ReplenishmentRecommendationDto calculateBySku(@PathVariable String skuNumber,
                                                         @RequestParam(defaultValue = "true") boolean useDefaultConfig) {
        CalculateReplenishmentCommand cmd = CalculateReplenishmentCommand.builder()
            .skuNumber(skuNumber)
            .forceRecalculation(true)
            .build();
        return replenishmentApplicationService.calculateReplenishment(cmd);
    }

    @PostMapping("/calculate-all")
    @PreAuthorize("hasAuthority('ims:write:replenishment')")
    public List<ReplenishmentRecommendationDto> calculateAll(@RequestParam(defaultValue = "false") boolean onlyReplenishmentNeeded) {
        List<ReplenishmentRecommendationDto> results = replenishmentApplicationService.calculateAllReplenishments();
        if (onlyReplenishmentNeeded) {
            results = results.stream().filter(dto -> Boolean.TRUE.equals(dto.getReorderFlag())).toList();
        }
        return results;
    }
}

