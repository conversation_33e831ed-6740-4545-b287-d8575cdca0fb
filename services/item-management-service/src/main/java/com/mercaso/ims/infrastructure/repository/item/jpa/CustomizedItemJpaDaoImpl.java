package com.mercaso.ims.infrastructure.repository.item.jpa;

import static com.mercaso.ims.application.query.ItemQuery.ItemQueryFilterDbType.CITEXT;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.ITEM_LIST_SEARCH_INVALID_FILTER_KEY;

import com.mercaso.ims.application.dto.CategoryItemCountsDto;
import com.mercaso.ims.application.dto.ItemAttributeDto;
import com.mercaso.ims.application.dto.ItemCategoryDto;
import com.mercaso.ims.application.dto.ItemSerachDto;
import com.mercaso.ims.application.query.ItemQuery;
import com.mercaso.ims.application.query.ItemQuery.ItemQueryFilterDbType;
import com.mercaso.ims.application.query.ItemQuery.ItemQueryFilterKey;
import com.mercaso.ims.application.query.ItemQuery.ItemQueryFilterType;
import com.mercaso.ims.application.query.ItemQuery.SortType;
import com.mercaso.ims.domain.item.enums.AttributeType;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.DynamicSearchCondition;
import com.mercaso.ims.infrastructure.repository.SearchConditionResolver;
import com.mercaso.ims.infrastructure.util.DateUtils;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.sql.Types;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

import com.mercaso.ims.infrastructure.util.FormatUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.PreparedStatementSetter;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Slf4j
@Component
@RequiredArgsConstructor
public class CustomizedItemJpaDaoImpl implements CustomizedItemJpaDao {

    private static final String ITEM_ID = "item_id";
    private static final String PRIMARY_VENDOR_ID = "primary_vendor_id";
    private static final String BACKUP_VENDOR_ID = "backup_vendor_id";
    private static final String AND = " AND ";

  private static final String QUERY_ITEM_DTO_LIST_OPTIMIZED =
      """
              filtered_agg_data AS (
                  SELECT
                      base_item.id AS item_id,
                      string_agg(DISTINCT tag_name, ',') AS tag_name,
                      string_agg(DISTINCT upc_number, ',') AS upc_number,
                      string_agg(DISTINCT CASE WHEN upc_type = 'CASE_UPC' THEN upc_number END, ',') AS case_upc,
                      string_agg(DISTINCT CASE WHEN upc_type = 'EACH_UPC' THEN upc_number END, ',') AS each_upc,
                      string_agg(DISTINCT CASE WHEN v.vendor_name IS NOT NULL AND vi.vendor_sku_number IS NOT NULL
                                              AND vi.vendor_sku_number != ''
                                              THEN CONCAT(v.vendor_name, ':', vi.vendor_sku_number) END, ',') AS vendor_sku_info
                  FROM paged_items pi
                  INNER JOIN item base_item ON pi.item_id = base_item.id
                  LEFT JOIN item_tag it ON base_item.id = it.item_id AND it.deleted_at IS NULL
                  LEFT JOIN item_upc iu ON base_item.id = iu.item_id AND iu.deleted_at IS NULL
                  LEFT JOIN vendor_item vi ON base_item.id = vi.item_id AND vi.deleted_at IS NULL
                  LEFT JOIN vendor v ON vi.vendor_id = v.id AND v.deleted_at IS NULL
                  GROUP BY base_item.id
              ),
              filtered_attr_data AS (
                  SELECT
                      base_item.id AS item_id,
                      string_agg(DISTINCT CASE WHEN a.name IS NOT NULL AND ia.value IS NOT NULL AND ia.unit IS NOT NULL
                                              THEN CONCAT(a.name, ':', ia.value || ' ' || ia.unit) END, ',') AS item_attribute,
                      string_agg(DISTINCT CASE WHEN a.id = '9f6fb540-6e4d-4b52-9fd8-5cf9901118e2' AND ia.value IS NOT NULL
                                              THEN CONCAT(ia.value || ' ' || ia.unit) END, ',') AS case_weight,
                      string_agg(DISTINCT CASE WHEN a.id = '13aba5c7-64a4-47d4-a179-8c223553dfec' AND ia.value IS NOT NULL
                                              THEN CONCAT(ia.value || ' ' || ia.unit) END, ',') AS each_weight,
                      string_agg(DISTINCT CASE WHEN a.id = 'a8e3f151-2151-44f8-a2d8-3ce0b90b8cdc' AND ia.value IS NOT NULL
                                              THEN CONCAT(ia.value || ' ' || ia.unit) END, ',') AS item_size,
                      string_agg(DISTINCT CASE WHEN a.id = 'd6b888b8-8e53-4dcd-b5e3-63be5036e0c0' AND ia.value IS NOT NULL
                                              THEN CONCAT(ia.value || ' ' || ia.unit) END, ',') AS bottle_size,
                      string_agg(DISTINCT CASE WHEN a.id = '0e973c70-f376-4d86-b6dd-90edcbedc37e' AND ia.value IS NOT NULL
                                              THEN CONCAT(ia.value) END, ',') AS cooler
                  FROM paged_items pi
                  INNER JOIN item base_item ON pi.item_id = base_item.id
                  LEFT JOIN item_attribute ia ON base_item.id = ia.item_id AND ia.deleted_at IS NULL
                  LEFT JOIN attribute a ON ia.attribute_id = a.id AND a.deleted_at IS NULL
                  GROUP BY base_item.id
              )
              SELECT item.id AS item_id,
                     item.sku_number AS sku_number,
                     item.title AS title,
                     item.name AS name,
                     item.description AS description,
                     item.new_description AS newDescription,
                     item.detail AS detail,
                     item.note AS note,
                     item.handle AS handle,
                     item.availability_status AS availability_status,
                     item.created_at AS created_at,
                     item.created_by AS created_by,
                     item.created_user_name AS created_user_name,
                     item.updated_at AS updated_at,
                     item.updated_by AS updated_by,
                     item.updated_user_name AS updated_user_name,
                     item.photo AS photo_name,
                     item.package_type AS package_type,
                     item.package_size AS package_size,
                     item.item_type AS item_type,
                     item.item_length AS item_length,
                     item.item_height AS item_height,
                     item.item_width AS item_width,
                     item.shelf_life shelf_life,
                     item.department department,
                     item.category category,
                     item.sub_category sub_category,
                     item.clazz clazz,
                     item.company_id company_id,
                     item.location_id location_id,
                     item.missing_each_upc_reason AS missing_each_upc_reason,
                     item.missing_case_upc_reason AS missing_case_upc_reason,
                     c.name category_name,
                     c.id category_id,
                     b.name brand_name,
                     b.id brand_id,
                     primaryVendor.vendor_name primary_vendor_name,
                     primaryVendor.id  primary_vendor_id,
                     primaryVendorItem.vendor_sku_number  primary_vendor_sku_number,
                     primaryVendorItem.pack_plus_crv_cost  primary_vendor_pack_plus_crv_cost,
                     backupVendor.vendor_name backup_vendor_name,
                     backupVendor.id  backup_vendor_id,
                     backupVendorItem.vendor_sku_number  backup_vendor_sku_number,
                     backupVendorItem.backup_pack_plus_crv_cost  backup_pack_plus_crv_cost,
                     backupVendorItem.availability vendor_item_availability,
                     irp.crv AS crv,
                     irp.crv_flag AS crv_flag,
                     irp.reg_price_individual AS reg_price_individual,
                     irp.reg_price AS reg_price,
                     irp.reg_price_plus_crv AS reg_price_plus_crv,
                     irp.item_price_group_id AS item_price_group_id,
                     ipg.group_name AS item_price_group_name,
                     ipp.promo_flag AS promo_flag,
                     ipp.promo_price AS promo_price,
                     ipp.promo_price_individual AS promo_price_individual,
                     ipp.promo_price_plus_crv AS promo_price_plus_crv,
                     ipp.promo_begin_time AS promo_begin_time,
                     ipp.promo_end_time AS promo_end_time,
                     dept_cat.name AS new_department,
                     dept_cat.id AS new_department_id,
                     main_cat.name AS new_category,
                     main_cat.id AS new_category_id,
                     sub_cat.name AS new_sub_category,
                     sub_cat.id AS new_sub_category_id,
                     class_cat.name AS new_clazz,
                     class_cat.id AS new_clazz_id,
                     ig.pareto_grade AS grade,
                     ig.wos_1_week AS wos_1_week,
                     ig.wos_4_weeks AS wos_4_weeks,
                     COALESCE(agg_data.tag_name, '') AS tag_name,
                     COALESCE(agg_data.upc_number, '') AS upc_number,
                     COALESCE(agg_data.case_upc, '') AS case_upc,
                     COALESCE(agg_data.each_upc, '') AS each_upc,
                     COALESCE(agg_data.vendor_sku_info, '') AS vendor_sku_info,
                     COALESCE(attr_data.item_attribute, '') AS item_attribute,
                     COALESCE(attr_data.case_weight, '') AS case_weight,
                     COALESCE(attr_data.each_weight, '') AS each_weight,
                     COALESCE(attr_data.item_size, '') AS item_size,
                     COALESCE(attr_data.bottle_size, '') AS bottle_size,
                     COALESCE(attr_data.cooler, '') AS cooler
              FROM paged_items pi
              INNER JOIN item item ON pi.item_id = item.id
              LEFT JOIN category c ON item.category_id = c.id AND c.deleted_at IS NULL
              LEFT JOIN brand b ON item.brand_id = b.id AND b.deleted_at IS NULL
              LEFT JOIN vendor primaryVendor ON item.primary_vendor_id = primaryVendor.id AND primaryVendor.deleted_at IS NULL
              LEFT JOIN vendor backupVendor ON item.backup_vendor_id = backupVendor.id AND backupVendor.deleted_at IS NULL
              LEFT JOIN vendor_item primaryVendorItem ON primaryVendor.id = primaryVendorItem.vendor_id
                  AND item.id = primaryVendorItem.item_id AND primaryVendorItem.deleted_at IS NULL
              LEFT JOIN vendor_item backupVendorItem ON backupVendor.id = backupVendorItem.vendor_id
                  AND item.id = backupVendorItem.item_id AND backupVendorItem.deleted_at IS NULL
              LEFT JOIN item_reg_price irp ON item.id = irp.item_id AND irp.deleted_at IS NULL
              LEFT JOIN item_price_group ipg ON irp.item_price_group_id = ipg.id AND ipg.deleted_at IS NULL
              LEFT JOIN item_promo_price ipp ON item.id = ipp.item_id AND ipp.deleted_at IS NULL
              LEFT JOIN item_grade ig ON (ig.item_id::uuid) = item.id
              LEFT JOIN category class_cat ON item.category_id = class_cat.id AND class_cat.deleted_at IS NULL
              LEFT JOIN category_hierarchy ch_sub ON class_cat.id = ch_sub.category_id AND ch_sub.depth = 1 AND ch_sub.deleted_at IS NULL
              LEFT JOIN category sub_cat ON ch_sub.ancestor_category_id = sub_cat.id AND sub_cat.deleted_at IS NULL
              LEFT JOIN category_hierarchy ch_main ON sub_cat.id = ch_main.category_id AND ch_main.depth = 1 AND ch_main.deleted_at IS NULL
              LEFT JOIN category main_cat ON ch_main.ancestor_category_id = main_cat.id AND main_cat.deleted_at IS NULL
              LEFT JOIN category_hierarchy ch_dept ON main_cat.id = ch_dept.category_id AND ch_dept.depth = 1 AND ch_dept.deleted_at IS NULL
              LEFT JOIN category dept_cat ON ch_dept.ancestor_category_id = dept_cat.id AND dept_cat.deleted_at IS NULL
              LEFT JOIN filtered_agg_data agg_data ON item.id = agg_data.item_id
              LEFT JOIN filtered_attr_data attr_data ON item.id = attr_data.item_id
              """;

    private static final String QUERY_ITEM_CATEGORY = """
                                      WITH AttributeId AS (
                                          SELECT id FROM attribute WHERE name = 'Bottle Size'
                                      ),
                                      MaxItemVersion AS (
                                              SELECT item_id, MAX(version_number) AS max_version_number
                                              FROM item_version
                                              GROUP BY item_id
                                          )
                                      SELECT
                                          item.id as item_id,
                                          item.primary_vendor_id,
                                          item.backup_vendor_id,
                                          item.title,
                                          item.package_size as pack,
                                          b.name as brand_name,
                                          CONCAT(ia.value, ' ',ia.unit) as bottle_size,
                                          item.sku_number,
                                          miv.max_version_number as last_version_number,
                                          irg.crv as crv,
                                          item.department,
                                          item.category,
                                          item.sub_category,
                                          item.clazz,
                                          -- New category hierarchy fields
                                          MAX(CASE WHEN ch.depth = 3 THEN c.name END) as new_department,
                                          MAX(CASE WHEN ch.depth = 2 THEN c.name END) as new_category,
                                          MAX(CASE WHEN ch.depth = 1 THEN c.name END) as new_sub_category,
                                          leaf_c.name as new_clazz
                                      FROM
                                          item item
                                      LEFT JOIN item_reg_price irg ON item.id = irg.item_id
                                      LEFT JOIN plytix_other_data pod ON item.id = pod.item_id
                                      LEFT JOIN brand b ON item.brand_id = b.id
                                      LEFT JOIN item_attribute ia ON item.id = ia.item_id AND ia.attribute_id = (SELECT id FROM AttributeId)
                                      -- Join for leaf category
                                      LEFT JOIN category leaf_c ON item.category_id = leaf_c.id AND leaf_c.deleted_at IS NULL
                                      -- Join for ancestor categories
                                      LEFT JOIN category_hierarchy ch ON item.category_id = ch.category_id AND ch.deleted_at IS NULL
                                      LEFT JOIN category c ON ch.ancestor_category_id = c.id AND c.deleted_at IS NULL
                                      LEFT JOIN MaxItemVersion miv ON item.id = miv.item_id
                                      WHERE 1=1 AND item.deleted_at IS NULL
        """;

  private static final String QUERY_ITEM_ID_LIST =
      """
        SELECT item.id AS item_id
            FROM item item
            LEFT JOIN vendor_item primaryVendorItem ON item.id = primaryVendorItem.item_id AND item.primary_vendor_id = primaryVendorItem.vendor_id AND primaryVendorItem.deleted_at IS NULL
            LEFT JOIN vendor_item backupVendorItem ON item.id = backupVendorItem.item_id AND item.backup_vendor_id = backupVendorItem.vendor_id AND backupVendorItem.deleted_at IS NULL
            LEFT JOIN item_upc item_upc on item.id = item_upc.item_id
            LEFT JOIN item_reg_price irp ON item.id = irp.item_id AND irp.deleted_at IS NULL
            LEFT JOIN item_promo_price ipp ON item.id = ipp.item_id AND ipp.deleted_at IS NULL
            LEFT JOIN category class_cat ON item.category_id = class_cat.id AND class_cat.deleted_at IS NULL
            LEFT JOIN category_hierarchy ch_any ON
                 (class_cat.id = ch_any.category_id OR class_cat.id = ch_any.ancestor_category_id) AND ch_any.deleted_at IS NULL
            LEFT JOIN category related_cat ON
                 (related_cat.id = ch_any.category_id OR related_cat.id = ch_any.ancestor_category_id) AND related_cat.deleted_at IS NULL
            LEFT JOIN item_grade ig ON (ig.item_id::uuid) = item.id
        WHERE item.deleted_at IS NULL
        """;

    private static final String COUNT_QUERY = """
        SELECT COUNT(DISTINCT item.id)
            FROM item item
            LEFT JOIN vendor_item primaryVendorItem ON item.id = primaryVendorItem.item_id AND item.primary_vendor_id = primaryVendorItem.vendor_id AND primaryVendorItem.deleted_at IS NULL
            LEFT JOIN vendor_item backupVendorItem ON item.id = backupVendorItem.item_id AND item.backup_vendor_id = backupVendorItem.vendor_id AND backupVendorItem.deleted_at IS NULL
            LEFT JOIN item_upc item_upc on item.id = item_upc.item_id
            LEFT JOIN item_reg_price irp ON item.id = irp.item_id AND irp.deleted_at IS NULL
            LEFT JOIN item_promo_price ipp ON item.id = ipp.item_id AND ipp.deleted_at IS NULL
            LEFT JOIN category class_cat ON item.category_id = class_cat.id AND class_cat.deleted_at IS NULL
            LEFT JOIN category_hierarchy ch_any ON
                 (class_cat.id = ch_any.category_id OR class_cat.id = ch_any.ancestor_category_id) AND ch_any.deleted_at IS NULL
            LEFT JOIN category related_cat ON
                 (related_cat.id = ch_any.category_id OR related_cat.id = ch_any.ancestor_category_id) AND related_cat.deleted_at IS NULL
            LEFT JOIN item_grade ig ON (ig.item_id::uuid) = item.id
        WHERE item.deleted_at IS NULL
        """;

    private static final String QUERY_ITEM_BY_CATEGORY_GROUP = """
                                      SELECT 
                                                category_id,
                                                SUM(CASE WHEN availability_status = 'ACTIVE' THEN 1 ELSE 0 END) as active_count,
                                                SUM(CASE WHEN availability_status = 'DRAFT' THEN 1 ELSE 0 END) as draft_count,
                                                SUM(CASE WHEN availability_status = 'ARCHIVED' THEN 1 ELSE 0 END) as archived_count
                                            FROM item
                                            WHERE deleted_at IS NULL 
        """;
    private static final String QUERY_ITEM_CATEGORY_GROUP_BY = " GROUP BY item.id,  b.name, irg.crv, CONCAT(ia.value, ' ',ia.unit), leaf_c.name, miv.max_version_number ";

    private static final String ITEM_ATTRIBUTE_DTO_QUERY = """
        SELECT a.id as attribute_id, a.name, ia.value, ia.unit, ia.attribute_type, ia.sort
        FROM item i
                 left join item_attribute ia on i.id = ia.item_id
                 left join attribute a on ia.attribute_id = a.id
        WHERE ia.deleted_at is null
          AND i.deleted_at is null
          AND a.id is not null
          AND i.id = ?
        """;

    private final JdbcTemplate jdbcTemplate;

    private static String generatePlaceholders(int count, ItemQueryFilterDbType dbType) {
        String join = String.join(",", Collections.nCopies(count, "?" + (CITEXT.equals(dbType) ? "::CITEXT" : "")));
        return count > 0 ? join : "";
    }

    @Override
    public List<UUID> getItemIdListV2(ItemQuery itemQuery) {

        DynamicSearchCondition<ItemQuery> dynamicSearch = new ItemCustomFilterDynamicSearch();
        StringBuilder sql = new StringBuilder(QUERY_ITEM_ID_LIST);
        sql.append(dynamicSearch.generateConditionBlock(itemQuery));
        sql.append("  GROUP BY item.id, irp.id, ipp.id, ig.id, primaryVendorItem.id, backupVendorItem.id ");
        sql.append(this.buildSortSql(itemQuery.getSorts()));
        sql.append(itemQuery.offsetLimitSql());

        return jdbcTemplate.query(sql.toString(),
            ps -> dynamicSearch.bindSqlParameter(ps, itemQuery),
            (rs, i) -> UUID.fromString(rs.getString(ITEM_ID)));
    }

    @Override
    public List<ItemSerachDto> getItemDtoList(ItemQuery itemQuery) {
        DynamicSearchCondition<ItemQuery> dynamicSearch = new ItemCustomFilterDynamicSearch();

        StringBuilder sql = new StringBuilder("WITH paged_items AS ( ");
        sql.append(QUERY_ITEM_ID_LIST);
        sql.append(dynamicSearch.generateConditionBlock(itemQuery));
        sql.append("  GROUP BY item.id, irp.id, ipp.id, ig.id, primaryVendorItem.id, backupVendorItem.id ");
        sql.append(this.buildSortSql(itemQuery.getSorts()));
        sql.append(itemQuery.offsetLimitSql());
        sql.append(" ),");
        sql.append(QUERY_ITEM_DTO_LIST_OPTIMIZED);
        sql.append(this.buildSortSql(itemQuery.getSorts()));

        return jdbcTemplate.query(sql.toString(),
                ps -> dynamicSearch.bindSqlParameter(ps, itemQuery),
                new RowMapper<ItemSerachDto>() {
            @Override
            public ItemSerachDto mapRow(ResultSet rs, int rowNum) throws SQLException {
                ItemSerachDto itemSerachDto = new ItemSerachDto();
                itemSerachDto.setId(UUID.fromString(rs.getString(ITEM_ID)));
                itemSerachDto.setName(rs.getString("name"));
                itemSerachDto.setTitle(rs.getString("title"));
                itemSerachDto.setSkuNumber(rs.getString("sku_number"));
                itemSerachDto.setDescription(rs.getString("description"));
                itemSerachDto.setNewDescription(rs.getString("newDescription"));
                itemSerachDto.setNote(rs.getString("note"));
                itemSerachDto.setPhotoName(rs.getString("photo_name"));
                itemSerachDto.setPrimaryVendorId(getUUID(rs, PRIMARY_VENDOR_ID));
                itemSerachDto.setPrimaryVendorName(rs.getString("primary_vendor_name"));
                itemSerachDto.setPrimaryVendorSkuNumber(rs.getString("primary_vendor_sku_number"));
                itemSerachDto.setBackupVendorId(getUUID(rs, BACKUP_VENDOR_ID));
                itemSerachDto.setBackupVendorName(rs.getString("backup_vendor_name"));
                itemSerachDto.setDetail(rs.getString("detail"));
                itemSerachDto.setHandle(rs.getString("handle"));
                itemSerachDto.setPackageType(rs.getString("package_type"));
                itemSerachDto.setPackageSize(null != rs.getObject("package_size") ? rs.getInt("package_size") : null);
                itemSerachDto.setItemType(rs.getString("item_type"));
                itemSerachDto.setAvailabilityStatus(rs.getString("availability_status"));
                itemSerachDto.setItemAttribute(rs.getString("item_attribute"));
                itemSerachDto.setItemTags(rs.getString("tag_name"));
                itemSerachDto.setItemUPCs(rs.getString("upc_number"));
                itemSerachDto.setVendorItem(rs.getString("vendor_sku_info"));
                itemSerachDto.setCategoryName(rs.getString("category_name"));
                itemSerachDto.setDepartmentId(getUUID(rs, "new_department_id"));
                itemSerachDto.setCategoryId(getUUID(rs, "new_category_id"));
                itemSerachDto.setSubCategoryId(getUUID(rs, "new_sub_category_id"));
                itemSerachDto.setClazzId(getUUID(rs, "new_clazz_id"));
                itemSerachDto.setBrandName(rs.getString("brand_name"));
                itemSerachDto.setBrandId(getUUID(rs, "brand_id"));
                itemSerachDto.setShelfLife(rs.getString("shelf_life"));
                itemSerachDto.setDepartment(rs.getString("new_department"));
                itemSerachDto.setCategory(rs.getString("new_category"));
                itemSerachDto.setSubCategory(rs.getString("new_sub_category"));
                itemSerachDto.setClazz(rs.getString("new_clazz"));
                itemSerachDto.setCrv(rs.getBigDecimal("crv"));
                itemSerachDto.setRegPrice(rs.getBigDecimal("reg_price"));
                itemSerachDto.setRegIndividualPrice(rs.getBigDecimal("reg_price_individual"));
                itemSerachDto.setRegPlusCrvPrice(rs.getBigDecimal("reg_price_plus_crv"));
                itemSerachDto.setWidth(null != rs.getObject("item_width") ? rs.getDouble("item_width") : null);
                itemSerachDto.setLength(null != rs.getObject("item_length") ? rs.getDouble("item_length") : null);
                itemSerachDto.setHeight(null != rs.getObject("item_height") ? rs.getDouble("item_height") : null);
                itemSerachDto.setCreatedAt(DateUtils.fromTimestamp(rs.getTimestamp("created_at")));
                itemSerachDto.setCreatedBy(rs.getString("created_by"));
                itemSerachDto.setCreatedUserName(rs.getString("created_user_name"));
                itemSerachDto.setUpdatedAt(DateUtils.fromTimestamp(rs.getTimestamp("updated_at")));
                itemSerachDto.setUpdatedBy(rs.getString("updated_by"));
                itemSerachDto.setUpdatedUserName(rs.getString("updated_user_name"));
                itemSerachDto.setPrimaryVendorCost(rs.getBigDecimal("primary_vendor_pack_plus_crv_cost"));
                itemSerachDto.setPromoFlag(rs.getBoolean("promo_flag"));
                itemSerachDto.setItemPriceGroupId(getUUID(rs, "item_price_group_id"));
                itemSerachDto.setItemPriceGroupName(rs.getString("item_price_group_name"));
                itemSerachDto.setBackupVendorCost(rs.getBigDecimal("backup_pack_plus_crv_cost"));
                itemSerachDto.setBackupVendorSkuNumber(rs.getString("backup_vendor_sku_number"));
                itemSerachDto.setGrade(rs.getString("grade"));
                itemSerachDto.setWos1Week(rs.getObject("wos_1_week") != null ? rs.getLong("wos_1_week") : null);
                itemSerachDto.setWos4Weeks(rs.getObject("wos_4_weeks") != null ? rs.getLong("wos_4_weeks") : null);
                itemSerachDto.setCompanyId(rs.getLong("company_id"));
                itemSerachDto.setLocationId(rs.getLong("location_id"));
                itemSerachDto.setCaseUpc(rs.getString("case_upc"));
                itemSerachDto.setEachUpc(rs.getString("each_upc"));
                itemSerachDto.setCaseWeight(FormatUtils.formatToTwoDecimalPlaces(rs.getString("case_weight")));
                itemSerachDto.setEachWeight(FormatUtils.formatToTwoDecimalPlaces(rs.getString("each_weight")));
                itemSerachDto.setItemSize(FormatUtils.formatToTwoDecimalPlaces(rs.getString("item_size")));
                itemSerachDto.setBottleSize(FormatUtils.formatToTwoDecimalPlaces(rs.getString("bottle_size")));
                itemSerachDto.setCooler(rs.getString("cooler"));
                itemSerachDto.setVendorItemAvailability(rs.getBoolean("vendor_item_availability"));
                itemSerachDto.setCrvFlag(rs.getBoolean("crv_flag"));
                itemSerachDto.setPromoPrice(rs.getBigDecimal("promo_price"));
                itemSerachDto.setPromoPriceIndividual(rs.getBigDecimal("promo_price_individual"));
                itemSerachDto.setPromoPricePlusCrv(rs.getBigDecimal("promo_price_plus_crv"));
                itemSerachDto.setPromoBeginTime(DateUtils.fromTimestamp(rs.getTimestamp("promo_begin_time")));
                itemSerachDto.setPromoEndTime(DateUtils.fromTimestamp(rs.getTimestamp("promo_end_time")));
                itemSerachDto.setMissingEachUpcReason(rs.getString("missing_each_upc_reason"));
                itemSerachDto.setMissingCaseUpcReason(rs.getString("missing_case_upc_reason"));
                return itemSerachDto;
            }
        });
    }

    @Override
    public long countQueryV2(ItemQuery itemQuery) {
        DynamicSearchCondition<ItemQuery> dynamicSearch = new ItemCustomFilterDynamicSearch();

        StringBuilder sql = new StringBuilder(COUNT_QUERY);
        sql.append(dynamicSearch.generateConditionBlock(itemQuery));

        List<Long> query = jdbcTemplate.query(sql.toString(),
            ps -> dynamicSearch.bindSqlParameter(ps, itemQuery),
            (rs, rowNum) -> rs.getLong(1));
        return query.getFirst();
    }

    @Override
    public List<ItemCategoryDto> findItemCategoryByIdIn(List<UUID> itemIds) {

        StringBuilder sql = new StringBuilder(QUERY_ITEM_CATEGORY);
        sql.append(this.buildIdInCondition(itemIds));
        sql.append(QUERY_ITEM_CATEGORY_GROUP_BY);

        return jdbcTemplate.query(sql.toString(), new PreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps) throws SQLException {
                int index = 1;
                for (UUID id : itemIds) {
                    ps.setObject(index++, id);
                }
            }
        }, new RowMapper<ItemCategoryDto>() {
            @Override
            public ItemCategoryDto mapRow(ResultSet rs, int rowNum) throws SQLException {
                return getItemCategoryDto(rs);
            }
        });
    }

    @Override
    public List<ItemCategoryDto> findItemCategoryBySkuIn(List<String> skus) {
        StringBuilder sql = new StringBuilder(QUERY_ITEM_CATEGORY);
        sql.append(this.buildSkuInCondition(skus));
        sql.append(QUERY_ITEM_CATEGORY_GROUP_BY);

        return jdbcTemplate.query(sql.toString(), ps -> {
            int index = 1;
            for (String sku : skus) {
                ps.setObject(index++, sku);
            }
        }, (rs, rowNum) -> getItemCategoryDto(rs));
    }

    @NotNull
    private ItemCategoryDto getItemCategoryDto(ResultSet rs) throws SQLException {
        ItemCategoryDto itemCategoryDto = new ItemCategoryDto();
        itemCategoryDto.setId(UUID.fromString(rs.getString(ITEM_ID)));
        itemCategoryDto.setTitle(rs.getString("title"));
        itemCategoryDto.setPack(rs.getInt("pack"));
        itemCategoryDto.setBrand(rs.getString("brand_name"));
        itemCategoryDto.setBottleSize(rs.getString("bottle_size"));
        itemCategoryDto.setSkuNumber(rs.getString("sku_number"));
        itemCategoryDto.setCrv(rs.getBigDecimal("crv"));
        itemCategoryDto.setDepartment(rs.getString("new_department"));
        itemCategoryDto.setCategory(rs.getString("new_category"));
        itemCategoryDto.setSubCategory(rs.getString("new_sub_category"));
        itemCategoryDto.setClazz(rs.getString("new_clazz"));
        itemCategoryDto.setPrimaryVendorId(
            StringUtils.isBlank(rs.getString(PRIMARY_VENDOR_ID)) ? null : UUID.fromString(rs.getString(PRIMARY_VENDOR_ID)));
        itemCategoryDto.setBackupVendorId(
            StringUtils.isBlank(rs.getString(BACKUP_VENDOR_ID)) ? null : UUID.fromString(rs.getString(BACKUP_VENDOR_ID)));
        itemCategoryDto.setVersionNumber(rs.getInt("last_version_number"));
        return itemCategoryDto;
    }

    private String buildSortSql(List<SortType> sorts) {
        SqlSortBuilder sqlSortBuilder = new SqlSortBuilder();
        return sqlSortBuilder.addSorts(sorts).build();
    }

    private String buildIdInCondition(List<UUID> ids) {
        if (!CollectionUtils.isEmpty(ids)) {
            return " AND item.id in (" + generatePlaceholders(ids.size(), ItemQueryFilterDbType.UUID) + ") ";
        }
        return StringUtils.EMPTY;
    }

    private String buildSkuInCondition(List<String> skus) {
        if (!CollectionUtils.isEmpty(skus)) {
            return " AND item.sku_number in (" + generatePlaceholders(skus.size(), CITEXT) + ") ";
        }
        return StringUtils.EMPTY;
    }

    private UUID getUUID(ResultSet rs, String columnName) throws SQLException {
        String columnValue = rs.getString(columnName);
        return Objects.nonNull(columnValue) ? UUID.fromString(columnValue) : null;
    }

    static class ItemCustomFilterDynamicSearch extends DynamicSearchCondition<ItemQuery> {

        public ItemCustomFilterDynamicSearch() {
            super(List.of(new CustomFilterCondition()));
        }
    }

    private static class CustomFilterCondition implements SearchConditionResolver<ItemQuery> {

        @Override
        public String generateConditionBlock(ItemQuery query) {
            if (query.getCustomFilter() == null || query.getCustomFilter().isEmpty()) {
                return StringUtils.EMPTY;
            }

            StringBuilder conditionBlock = new StringBuilder();
            query.getCustomFilter().forEach((key, value) -> {
                if (StringUtils.isNotBlank(value)) {
                    ItemQueryFilterKey itemQueryFilterKey = ItemQueryFilterKey.fromParamKey(key);
                    if (itemQueryFilterKey == null) {
                        log.warn("[generateConditionBlock] Invalid filter key: {}", key);
                        throw new ImsBusinessException(ITEM_LIST_SEARCH_INVALID_FILTER_KEY);
                    }
                    ItemQueryFilterType filterType = itemQueryFilterKey.getType();
                    String dbKey = itemQueryFilterKey.getDbKey();
                    switch (filterType) {
                        case IN:
                            conditionBlock.append(AND)
                                .append(itemQueryFilterKey.getTableName())
                                .append(".")
                                .append(dbKey)
                                .append(" IN (")
                                .append(generatePlaceholders(query.getCustomFilterValueList(value).size(),
                                    itemQueryFilterKey.getDbType()))
                                .append(") ");
                            break;
                        case LIKE:
                            conditionBlock.append(AND)
                                .append(itemQueryFilterKey.getTableName())
                                .append(".")
                                .append(dbKey)
                                .append(" ILIKE ? ");
                            break;
                        case RANGE:
                            String[] rangeValues = value.split(",");
                            if (rangeValues.length == 2) {
                                if (StringUtils.isNotBlank(rangeValues[0])) {
                                    conditionBlock.append(AND)
                                        .append(itemQueryFilterKey.getTableName())
                                        .append(".")
                                        .append(dbKey)
                                        .append(" >= ? ");
                                }
                                if (StringUtils.isNotBlank(rangeValues[1])) {
                                    conditionBlock.append(AND)
                                        .append(itemQueryFilterKey.getTableName())
                                        .append(".")
                                        .append(dbKey)
                                        .append(" <= ? ");
                                }
                            }
                            break;
                        case EQUALS:
                        default:
                            if (value.trim().equals("N/A")) {
                                conditionBlock.append(AND)
                                    .append(itemQueryFilterKey.getTableName())
                                    .append(".")
                                    .append(dbKey)
                                    .append(" is null ");
                            } else {
                                conditionBlock.append(AND)
                                    .append(itemQueryFilterKey.getTableName())
                                    .append(".")
                                    .append(dbKey)
                                    .append(" = ? ");
                            }
                            break;
                    }
                }
            });

            return conditionBlock.toString();
        }

        @Override
        public int bindSqlParameter(PreparedStatement ps, ItemQuery query, int index) throws SQLException {
            if (query.getCustomFilter() != null && !query.getCustomFilter().isEmpty()) {
                for (Map.Entry<String, String> entry : query.getCustomFilter().entrySet()) {

                    ItemQueryFilterKey itemQueryFilterKey = ItemQueryFilterKey.fromParamKey(entry.getKey());
                    if (itemQueryFilterKey == null) {
                        log.warn("[bindSqlParameter] Invalid filter key: {}", entry.getKey());
                        throw new ImsBusinessException(ITEM_LIST_SEARCH_INVALID_FILTER_KEY);
                    }

                    if (StringUtils.isNotBlank(entry.getValue())) {
                        ItemQueryFilterType filterType = itemQueryFilterKey.getType();
                        switch (filterType) {
                            case IN:
                                List<String> values = query.getCustomFilterValueList(entry.getValue());
                                for (String val : values) {
                                    if (ItemQueryFilterDbType.UUID.equals(itemQueryFilterKey.getDbType())) {
                                        ps.setObject(index++, UUID.fromString(val.trim()), Types.OTHER);
                                    } else {
                                        ps.setString(index++, val.trim());
                                    }
                                }
                                break;
                            case LIKE:
                                ps.setString(index++, "%" + entry.getValue().trim() + "%");
                                break;
                            case RANGE:
                                String[] rangeValues = entry.getValue().split(",");
                                if (rangeValues.length == 2) {
                                    if (StringUtils.isNotBlank(rangeValues[0])) {
                                        ps.setTimestamp(index++,
                                            Timestamp.from(ZonedDateTime.parse(rangeValues[0].trim()).toInstant()));
                                    }
                                    if (StringUtils.isNotBlank(rangeValues[1])) {
                                        ps.setTimestamp(index++,
                                            Timestamp.from(ZonedDateTime.parse(rangeValues[1].trim()).toInstant()));
                                    }
                                }
                                break;
                            case EQUALS:
                            default:
                                if (!entry.getValue().trim().equals("N/A")) {
                                    if (ItemQueryFilterDbType.UUID.equals(itemQueryFilterKey.getDbType())) {
                                        ps.setObject(index++, UUID.fromString(entry.getValue().trim()), Types.OTHER);
                                    } else if (ItemQueryFilterDbType.BOOLEAN.equals(itemQueryFilterKey.getDbType())) {
                                        ps.setBoolean(index++, Boolean.parseBoolean(entry.getValue()));
                                    } else {
                                        ps.setString(index++, entry.getValue().trim());
                                    }
                                }
                                break;
                        }
                    }
                }
            }
            return index;
        }
    }

    @Override
    public List<CategoryItemCountsDto> countItemsByCategoryIdAndStatus(List<UUID> categoryIds) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(categoryIds)) {
            return List.of();
        }
        StringBuilder sql = new StringBuilder(QUERY_ITEM_BY_CATEGORY_GROUP);
        sql.append(this.buildCategoryIdInCondition(categoryIds));
        sql.append(" GROUP BY category_id ");
        return jdbcTemplate.query(sql.toString(), new PreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps) throws SQLException {
                int index = 1;
                for (UUID categoryId : categoryIds) {
                    ps.setObject(index++, categoryId);
                }
            }
        }, new RowMapper<CategoryItemCountsDto>() {
            @Override
            public CategoryItemCountsDto mapRow(ResultSet rs, int rowNum) throws SQLException {
                CategoryItemCountsDto categoryItemCountsDto = new CategoryItemCountsDto();
                categoryItemCountsDto.setCategoryId(UUID.fromString(rs.getString("category_id")));
                categoryItemCountsDto.setActiveCount(rs.getLong("active_count"));
                categoryItemCountsDto.setDraftCount(rs.getLong("draft_count"));
                categoryItemCountsDto.setArchivedCount(rs.getLong("archived_count"));
                return categoryItemCountsDto;
            }
        });
    }

    @Override
    public List<ItemAttributeDto> findItemAttributeDtoByItemId(UUID itemId) {
        StringBuilder sql = new StringBuilder(ITEM_ATTRIBUTE_DTO_QUERY);

        return jdbcTemplate.query(sql.toString(), new PreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps) throws SQLException {
                ps.setObject(1, itemId);
            }
        }, new RowMapper<ItemAttributeDto>() {
            @Override
            public ItemAttributeDto mapRow(ResultSet rs, int rowNum) throws SQLException {
                ItemAttributeDto itemAttributeDto = new ItemAttributeDto();
                itemAttributeDto.setAttributeId(UUID.fromString(rs.getString("attribute_id")));
                itemAttributeDto.setAttributeName(rs.getString("name"));
                itemAttributeDto.setAttributeType(AttributeType.fromString(rs.getString("attribute_type")));
                itemAttributeDto.setValue(rs.getString("value"));
                itemAttributeDto.setUnit(rs.getString("unit"));
                itemAttributeDto.setSort(rs.getInt("sort"));
                return itemAttributeDto;
            }
        });
    }

    public static class SqlSortBuilder {

        private static final String DEFAULT_SORT = " ORDER BY item.updated_at DESC NULLS LAST ";

        private final List<SortField> sortFields;

        public SqlSortBuilder() {
            this.sortFields = new ArrayList<>();
        }

        public SqlSortBuilder addSort(SortType sortType) {
            if (sortType != null) {
                sortFields.add(new SortField(sortType));
            }
            return this;
        }

        public SqlSortBuilder addSorts(List<SortType> sortTypes) {
            if (!CollectionUtils.isEmpty(sortTypes)) {
                sortTypes.stream()
                    .filter(Objects::nonNull)
                    .forEach(sortType -> sortFields.add(new SortField(sortType)));
            }
            return this;
        }


        public String build() {
            if (sortFields.isEmpty()) {
                return DEFAULT_SORT;
            }

            String sortClauses = sortFields.stream()
                .map(this::formatSortField)
                .collect(Collectors.joining(", "));

            return String.format(" ORDER BY %s NULLS LAST ", sortClauses);
        }

        private String formatSortField(SortField sortField) {
            return String.format("%s.%s %s",
                sortField.getSortType().getTable(),
                sortField.getSortType().getField(),
                sortField.getSortType().getDirection());
        }


        @Data
        @AllArgsConstructor
        private static class SortField {

            private SortType sortType;
        }
    }

    private String buildCategoryIdInCondition(List<UUID> categoryIds) {
        if (!CollectionUtils.isEmpty(categoryIds)) {
            return " AND category_id in (" + generatePlaceholders(categoryIds.size(), ItemQueryFilterDbType.UUID) + ") ";
        }
        return StringUtils.EMPTY;
    }
}
