package com.mercaso.ims.domain.item;

import static com.mercaso.ims.domain.item.enums.ItemType.SELF_OPERATED;
import static com.mercaso.ims.domain.item.enums.SalesStatus.LISTING;

import com.mercaso.ims.application.command.CreateItemCommand;
import com.mercaso.ims.application.dto.ItemAttributeDto;
import com.mercaso.ims.domain.item.enums.PackageType;
import com.mercaso.ims.domain.itemattribute.ItemAttribute;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;

public class ItemFactory {

    private ItemFactory() {
    }

    public static Item createItem(CreateItemCommand command) {
        List<ItemAttribute> attributes = buildItemAttributes(command.getItemAttributes());

        List<ItemUPC> itemUPCs = new ArrayList<>();
        if (command.getItemUPCs() != null) {
            command.getItemUPCs()
                .stream().filter(itemUPCDto -> StringUtils.isNotBlank(itemUPCDto.getUpcNumber()))
                .forEach(
                    itemUPCDto -> itemUPCs.add(ItemUPC.builder()
                        .upcNumber(itemUPCDto.getUpcNumber().trim())
                        .itemUpcType(itemUPCDto.getItemUpcType())
                        .build())
                );
        }

        Item item = Item.builder()
                .categoryId(command.getCategoryId())
                .brandId(command.getBrandId())
                .name(command.getTitle())
                .title(command.getTitle())
                .skuNumber(command.getSkuNumber())
                .description(command.getDescription())
                .note(command.getNote())
                .photo(command.getPhotoName())
                .primaryVendorId(command.getPrimaryVendorId())
                .backupVendorId(command.getBackupVendorId())
                .detail(command.getDetail())
                .packageType(command.getPackageType())
                .packageSize(command.getPackageSize())
                .shelfLife(command.getShelfLife())
                .itemType(command.getItemType())
                .availabilityStatus(command.getAvailabilityStatus())
                .salesStatus(command.getSalesStatus())
                .companyId(command.getCompanyId() == null ? 10000L : command.getCompanyId())
                .locationId(command.getLocationId() == null ? 10001L : command.getLocationId())
                .handle(command.getHandle())
                .packageType(PackageType.PACK)
                .itemType(SELF_OPERATED)
                .salesStatus(LISTING)
                .availabilityStatus(command.getAvailabilityStatus())
                .department(command.getDepartment())
                .category(command.getCategory())
                .subCategory(command.getSubCategory())
                .clazz(command.getClazz())
                .newDescription(command.getNewDescription())
                .itemUPCs(itemUPCs)
                .itemAttributes(attributes)
                .width(command.getWidth())
                .length(command.getLength())
                .height(command.getHeight())
                .build();

        item.setMissingUpcReason(command.getMissingEachUpcReason(), command.getMissingCaseUpcReason());
        return item;
    }

    public static List<ItemAttribute> buildItemAttributes(List<ItemAttributeDto> itemAttributeDtos) {
        List<ItemAttribute> attributes = new ArrayList<>();
        if (itemAttributeDtos != null) {
            itemAttributeDtos.forEach(
                itemAttributeDto -> attributes.add(buildItemAttribute(itemAttributeDto))
            );
        }

        return attributes;
    }

    public static ItemAttribute buildItemAttribute(ItemAttributeDto itemAttributeDto) {

        return itemAttributeDto == null ? null : ItemAttribute.builder()
            .attributeId(itemAttributeDto.getAttributeId())
            .attributeType(itemAttributeDto.getAttributeType())
            .unit(itemAttributeDto.getUnit())
            .value(itemAttributeDto.getValue())
            .build();
    }
}
