package com.mercaso.ims.infrastructure.repository.itemcostcollection.jpa;


import com.mercaso.ims.application.dto.ItemCostCollectionDetailDto;
import com.mercaso.ims.application.query.ItemCostCollectionQuery;
import com.mercaso.ims.application.query.ItemCostCollectionQuery.SortType;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionSources;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionTypes;
import com.mercaso.ims.infrastructure.repository.DynamicSearchCondition;
import com.mercaso.ims.infrastructure.repository.SearchConditionResolver;
import com.mercaso.ims.infrastructure.util.DateUtils;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class CustomizedItemCostCollectionJpaDaoImpl implements CustomizedItemCostCollectionJpaDao {


    private static final String QUERY_ITEM_COST_COLLECTION_DTO_LIST = """
        SELECT
            icc.id,
            icc.source,
            icc.vendor_id,
            icc.vendor_name,
            icc.collection_number,
            icc.type,
            CASE
                    WHEN icc.file_name IS NULL THEN ''
                    WHEN icc.file_name LIKE 'documents/%' THEN SUBSTRING(icc.file_name FROM 11)
                    ELSE icc.file_name
                END AS file_name,
            CASE
                WHEN icc.source ='FINALE_PURCHASE_ORDER' THEN 'DIRECT'
                ELSE 'JIT'
                END AS vendor_type,
            COUNT(icr.id) AS total_count,
            COUNT(CASE WHEN icr.status = 'PENDING' THEN 1 END) AS pending_count,
            COUNT(CASE WHEN icr.status = 'APPROVED' THEN 1 END) AS approved_count,
            COUNT(CASE WHEN icr.status = 'REJECTED' THEN 1 END) AS rejected_count,
            COUNT(CASE WHEN icr.status = 'INVALID' THEN 1 END) AS invalid_count,
            icc.created_at,
            icc.created_by,
            icc.created_user_name
        FROM
            item_cost_collection icc
                LEFT JOIN
            item_cost_change_request icr
            ON icc.id = icr.item_cost_collection_id
        WHERE
            icc.deleted_at IS NULL
          AND icr.deleted_at IS NULL
        """;

    private static final String COUNT_QUERY = """
        select count(icc.*)
               from item_cost_collection icc
        WHERE
            icc.deleted_at IS NULL
        """;

    private static final String GROUP_SQL = """
        GROUP BY
            icc.id,
            icc.source,
            icc.vendor_id,
            icc.vendor_name,
            icc.collection_number,
            icc.type,
            icc.file_name,
            icc.created_at,
            icc.created_by,
            icc.created_user_name
        """;

    private final JdbcTemplate jdbcTemplate;

    @Override
    public List<ItemCostCollectionDetailDto> fetchItemCostCollectionDtoList(ItemCostCollectionQuery itemCostCollectionQuery) {

        DynamicSearchCondition<ItemCostCollectionQuery> dynamicSearch = new ItemCostCollectionDynamicSearch();
        StringBuilder sql = new StringBuilder(QUERY_ITEM_COST_COLLECTION_DTO_LIST);
        sql.append(dynamicSearch.generateConditionBlock(itemCostCollectionQuery));
        sql.append(GROUP_SQL);
        sql.append(this.buildSortSql(itemCostCollectionQuery.getSort()));
        sql.append(itemCostCollectionQuery.offsetLimitSql());

        return jdbcTemplate.query(sql.toString(), ps -> dynamicSearch.bindSqlParameter(ps, itemCostCollectionQuery),
            (rs, rowNumber) -> ItemCostCollectionDetailDto.builder()
                .id(UUID.fromString(rs.getString("id")))
                .vendorId(UUID.fromString(rs.getString("vendor_id")))
                .source(ItemCostCollectionSources.valueOf(rs.getString("source")))
                .type(ItemCostCollectionTypes.valueOf(rs.getString("type")))
                .vendorName(rs.getString("vendor_name"))
                .createdAt(DateUtils.fromTimestamp(rs.getTimestamp("created_at")))
                .createdBy(rs.getString("created_by"))
                .createdUserName(rs.getString("created_user_name"))
                .fileName(rs.getString("file_name"))
                .collectionNumber(rs.getString("collection_number"))
                .approvedCount(rs.getInt("approved_count"))
                .pendingCount(rs.getInt("pending_count"))
                .rejectedCount(rs.getInt("rejected_count"))
                .invalidCount(rs.getInt("invalid_count"))
                .vendorType(rs.getString("vendor_type"))
                .build());
    }

    @Override
    public ItemCostCollectionDetailDto getItemCostCollectionDto(UUID id) {

        List<ItemCostCollectionDetailDto> itemCostCollectionDetailDtos = fetchItemCostCollectionDtoList(ItemCostCollectionQuery.builder()
            .id(id)
            .build());
        return itemCostCollectionDetailDtos.isEmpty() ? null : itemCostCollectionDetailDtos.getFirst();
    }

    @Override
    public long countQuery(ItemCostCollectionQuery itemCostCollectionQuery) {
        DynamicSearchCondition<ItemCostCollectionQuery> dynamicSearch = new ItemCostCollectionDynamicSearch();
        StringBuilder sql = new StringBuilder(COUNT_QUERY);
        sql.append(dynamicSearch.generateConditionBlock(itemCostCollectionQuery));

        List<Long> query = jdbcTemplate.query(sql.toString(),
            ps -> dynamicSearch.bindSqlParameter(ps, itemCostCollectionQuery),
            (rs, rowNum) -> rs.getLong(1));

        return query.get(0);
    }

    private String buildSortSql(SortType sort) {
        String sql = StringUtils.EMPTY;
        if (sort == null || sort == SortType.CREATED_AT_DESC) {
            return " ORDER BY icc.created_at DESC ";
        }
        return sql;
    }

    static class ItemCostCollectionDynamicSearch extends DynamicSearchCondition<ItemCostCollectionQuery> {

        public ItemCostCollectionDynamicSearch() {
            super(List.of(new CreatedAtCondition(),
                new CreatedByCondition(),
                new VendorCondition(),
                new IdCondition()));
        }
    }

    private static class CreatedAtCondition implements SearchConditionResolver<ItemCostCollectionQuery> {

        @Override
        public String generateConditionBlock(ItemCostCollectionQuery query) {
            if (null == query.getCreatedAtBegin() || null == query.getCreatedAtEnd()) {
                return StringUtils.EMPTY;
            }
            return " AND icc.created_at BETWEEN ? AND ? ";
        }

        @Override
        public int bindSqlParameter(PreparedStatement ps, ItemCostCollectionQuery query, int index) throws SQLException {

            if (null != query.getCreatedAtBegin() && null != query.getCreatedAtEnd()) {
                ps.setTimestamp(index++, Timestamp.from(query.getCreatedAtBegin()));
                ps.setTimestamp(index++, Timestamp.from(query.getCreatedAtEnd()));
            }
            return index;
        }
    }

    private static class CreatedByCondition implements SearchConditionResolver<ItemCostCollectionQuery> {

        @Override
        public String generateConditionBlock(ItemCostCollectionQuery query) {
            if (null == query.getCreatedBy()) {
                return StringUtils.EMPTY;
            }
            return " AND icc.created_by = ? ";
        }

        @Override
        public int bindSqlParameter(PreparedStatement ps, ItemCostCollectionQuery query, int index) throws SQLException {

            if (StringUtils.isNotBlank(query.getCreatedBy())) {
                ps.setString(index++, query.getCreatedBy());
            }
            return index;
        }
    }

    private static class VendorCondition implements SearchConditionResolver<ItemCostCollectionQuery> {

        @Override
        public String generateConditionBlock(ItemCostCollectionQuery query) {
            if (null == query.getVendorId()) {
                return StringUtils.EMPTY;
            }
            return " AND icc.vendor_id = ? ";
        }

        @Override
        public int bindSqlParameter(PreparedStatement ps, ItemCostCollectionQuery query, int index) throws SQLException {

            if (null != query.getVendorId()) {
                ps.setObject(index++, query.getVendorId());
            }
            return index;
        }
    }

    private static class IdCondition implements SearchConditionResolver<ItemCostCollectionQuery> {

        @Override
        public String generateConditionBlock(ItemCostCollectionQuery query) {
            if (null == query.getId()) {
                return StringUtils.EMPTY;
            }
            return " AND icc.id = ? ";
        }

        @Override
        public int bindSqlParameter(PreparedStatement ps, ItemCostCollectionQuery query, int index) throws SQLException {

            if (null != query.getId()) {
                ps.setObject(index++, query.getId());
            }
            return index;
        }
    }
}
