package com.mercaso.ims.domain.vendor;

import com.mercaso.ims.application.command.CreateVendorCommand;

public class VendorFactory {

    private VendorFactory() {
    }

    public static Vendor createVendor(CreateVendorCommand command) {
        return Vendor.builder()
            .vendorName(command.getVendorName())
            .externalPicking(command.getExternalPicking())
            .build();
    }
}
