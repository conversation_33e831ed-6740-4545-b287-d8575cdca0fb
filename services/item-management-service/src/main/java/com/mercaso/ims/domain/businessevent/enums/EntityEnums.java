package com.mercaso.ims.domain.businessevent.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum EntityEnums {

    TEST_ORDER("TestOrder"),
    ITEM_ADJUSTMENT_REQUEST("ItemAdjustmentRequest"),
    ITEM_ADJUSTMENT_REQUEST_DETAIL("ItemAdjustmentRequestDetail"),
    ITEM("Item"),
    ITEM_COST_COLLECTION("ItemCostCollection"),
    ITEM_COST_CHANGE_REQUEST("ItemCostChangeRequest"),
    ANALYZE_VENDOR_PO_RECORD("AnalyzeVendorPoInvoice"),
    VENDOR_ITEM("VendorItem"),
    ITEM_PRICE_GROUP("ItemPriceGroup"),
    CATEGORY("Category"),
    EXCEPTION_RECORD("ExceptionRecord"),
    BULK_EXPORT_RECORDS("BulkExportRecords"),
    BRAND("Brand"),
    ;

    private final String value;

}
