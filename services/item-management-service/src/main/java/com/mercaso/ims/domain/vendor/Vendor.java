package com.mercaso.ims.domain.vendor;

import com.mercaso.ims.domain.BaseDomain;
import com.mercaso.ims.domain.vendor.enums.VendorStatus;
import java.util.UUID;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
@ToString
@SuperBuilder
public class Vendor extends BaseDomain {

    private final UUID id;

    private String vendorName;

    private String vendorContactName;

    private String vendorContactTel;

    private String vendorCompanyName;

    private VendorStatus vendorStatus;

    private Boolean externalPicking;

    private String finaleId;

    public Vendor create(String vendorName, String vendorContactName, String vendorContactTel, String vendorCompanyName) {
        return Vendor.builder()
            .vendorName(vendorName)
            .vendorContactName(vendorContactName)
            .vendorContactTel(vendorContactTel)
            .vendorCompanyName(vendorCompanyName)
            .vendorStatus(VendorStatus.ACTIVE)
            .build();
    }

    public void updateVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    public void setexternalPicking(Boolean externalPicking) {
        this.externalPicking = externalPicking;
    }

    public void updateVendorContactInfo(String vendorContactName, String vendorContactTel) {
        this.vendorContactName = vendorContactName;
        this.vendorContactTel = vendorContactTel;
    }

    public void updateStatus(VendorStatus vendorStatus) {
        this.vendorStatus = vendorStatus;
    }

    public Boolean judgePrimaryVendor(UUID primaryVendorId) {
        return this.id.equals(primaryVendorId);

    }

    public void updateFinaleId(String finaleId) {
        this.finaleId = finaleId;
    }

}
