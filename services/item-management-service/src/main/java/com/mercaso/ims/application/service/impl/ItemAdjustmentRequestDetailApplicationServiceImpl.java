package com.mercaso.ims.application.service.impl;

import static com.mercaso.ims.domain.vendor.VendorConstant.NO_BACKUP;
import static com.mercaso.ims.domain.vendor.VendorConstant.NO_SECONDARY;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.BRAND_NOT_FOUND;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.ITEM_ADJUSTMENT_NOT_FOUND;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.ITEM_NOT_FOUND;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.VENDOR_NOT_FOUND;

import com.mercaso.ims.application.command.CleanItemUpcCommand;
import com.mercaso.ims.application.command.CreateItemAdjustmentRequestDetailCommand;
import com.mercaso.ims.application.command.CreateItemCommand;
import com.mercaso.ims.application.command.CreateVendorItemCommand;
import com.mercaso.ims.application.command.DeleteItemCommand;
import com.mercaso.ims.application.command.UpdateItemCommand;
import com.mercaso.ims.application.command.UpdateVendorItemCommand;
import com.mercaso.ims.application.dto.CategoryDto;
import com.mercaso.ims.application.dto.CategoryGroupDto;
import com.mercaso.ims.application.dto.ItemAdjustmentRequestDetailDto;
import com.mercaso.ims.application.dto.ItemAttributeDto;
import com.mercaso.ims.application.dto.ItemPromoPriceDto;
import com.mercaso.ims.application.dto.ItemTagDto;
import com.mercaso.ims.application.dto.ItemUPCDto;
import com.mercaso.ims.application.dto.VendorItemDto;
import com.mercaso.ims.application.dto.payload.ItemAdjustmentRequestDetailCompletedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemAdjustmentRequestDetailCreatedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemAdjustmentRequestDetailImsUpdatedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemAdjustmentRequestDetailShopifySynchronizedPayloadDto;
import com.mercaso.ims.application.mapper.item.ItemDtoApplicationMapper;
import com.mercaso.ims.application.mapper.itemadjustmentrequestdetail.ItemAdjustmentRequestDetailDtoApplicationMapper;
import com.mercaso.ims.application.service.CategoryApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestDetailApplicationService;
import com.mercaso.ims.application.service.ItemApplicationService;
import com.mercaso.ims.application.service.VendorItemApplicationService;
import com.mercaso.ims.domain.attribute.AttributeConstant;
import com.mercaso.ims.domain.brand.Brand;
import com.mercaso.ims.domain.brand.service.BrandService;
import com.mercaso.ims.domain.businessevent.service.BusinessEventService;
import com.mercaso.ims.domain.category.CategoryConstant;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.ItemUPC;
import com.mercaso.ims.domain.item.enums.AttributeType;
import com.mercaso.ims.domain.item.enums.ItemUpcType;
import com.mercaso.ims.domain.item.service.ItemService;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.ItemAdjustmentRequestDetail;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.ItemAdjustmentRequestDetailFactory;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.ItemAdjustmentRequestDetailRepository;
import com.mercaso.ims.domain.itemattribute.ItemAttribute;
import com.mercaso.ims.domain.itempromoprice.ItemPromoPrice;
import com.mercaso.ims.domain.itempromoprice.service.ItemPromoPriceService;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.enums.VendorItemType;
import com.mercaso.ims.domain.vendoritem.service.VendorItemService;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ItemAdjustmentRequestDetailApplicationServiceImpl implements ItemAdjustmentRequestDetailApplicationService {

    private final ItemAdjustmentRequestDetailDtoApplicationMapper adjustmentRequestDetailDtoApplicationMapper;
    private final ItemAdjustmentRequestDetailRepository itemAdjustmentRequestDetailRepository;
    private final BusinessEventService businessEventService;
    private final BrandService brandService;
    private final VendorService vendorService;
    private final VendorItemService vendorItemService;
    private final VendorItemApplicationService vendorItemApplicationService;

    private final ItemService itemService;
    private final ItemPromoPriceService itemPromoPriceService;
    private final ItemApplicationService itemApplicationService;
    private final ItemDtoApplicationMapper itemDtoApplicationMapper;
    private final CategoryApplicationService categoryApplicationService;

    @Override
    public ItemAdjustmentRequestDetailDto create(CreateItemAdjustmentRequestDetailCommand command) {
        ItemAdjustmentRequestDetail detail = ItemAdjustmentRequestDetailFactory.create(command);
        detail = itemAdjustmentRequestDetailRepository.save(detail);
        ItemAdjustmentRequestDetailDto detailDto = adjustmentRequestDetailDtoApplicationMapper.domainToDto(detail);
        businessEventService.dispatch(ItemAdjustmentRequestDetailCreatedPayloadDto.builder()
            .itemAdjustmentRequestDetailId(detailDto.getId())
            .data(detailDto)
            .build());
        return detailDto;
    }

    @Override
    public void updateIms(UUID id) {
        ItemAdjustmentRequestDetail detail = fetchItemAdjustmentRequestDetailOrThrow(id);
        detail = detail.updateIms();
        itemAdjustmentRequestDetailRepository.save(detail);
    }

    @Override
    public ItemAdjustmentRequestDetailDto completeItemAdjustmentRequest(UUID id) {

        ItemAdjustmentRequestDetail detail = fetchItemAdjustmentRequestDetailOrThrow(id);

        detail = itemAdjustmentRequestDetailRepository.save(detail);
        ItemAdjustmentRequestDetailDto detailDto = adjustmentRequestDetailDtoApplicationMapper.domainToDto(detail);
        businessEventService.dispatch(ItemAdjustmentRequestDetailCompletedPayloadDto.builder()
            .itemAdjustmentRequestDetailId(detailDto.getId())
            .data(detailDto)
            .build());
        return detailDto;
    }

    @Override
    @Transactional
    public void updateImsAsItemAdjustmentRequest(ItemAdjustmentRequestDetailDto requestDetailDto) {

        ItemAdjustmentRequestDetail detail = fetchItemAdjustmentRequestDetailOrThrow(requestDetailDto.getId());

        try {
            switch (requestDetailDto.getType()) {
                case CREATE:
                    processCreateItemRequest(requestDetailDto);
                    break;
                case UPDATE:
                    processUpdateItemRequest(requestDetailDto);
                    break;
                case DELETE:
                    processDeleteItemRequest(requestDetailDto);
                    break;
                case CLEAN_UPC:
                    processCleanUpcRequest(requestDetailDto);
                    break;
                default:
                    break;
            }
            detail = detail.updateIms();
        } catch (ImsBusinessException ie) {
            log.error("updateImsAsItemAdjustmentRequest error :{}", ie.getMessage(), ie);
            detail = detail.updateImsFailure(ie.getMessage());
        } catch (Exception e) {
            log.error("updateImsAsItemAdjustmentRequest error :{}", e.getMessage(), e);
            detail = detail.updateImsFailure("IMS_UPDATED_FAILURE");
        }
        detail = itemAdjustmentRequestDetailRepository.save(detail);
        ItemAdjustmentRequestDetailDto detailDto = adjustmentRequestDetailDtoApplicationMapper.domainToDto(detail);

        businessEventService.dispatch(ItemAdjustmentRequestDetailImsUpdatedPayloadDto.builder()
            .itemAdjustmentRequestDetailId(detailDto.getId())
            .data(detailDto)
            .build());
    }

    @Override
    public void syncToShopify(UUID id) {
        ItemAdjustmentRequestDetail detail = fetchItemAdjustmentRequestDetailOrThrow(id);
        detail = detail.syncToShopify();
        detail = itemAdjustmentRequestDetailRepository.save(detail);
        ItemAdjustmentRequestDetailDto detailDto = adjustmentRequestDetailDtoApplicationMapper.domainToDto(detail);
        businessEventService.dispatch(ItemAdjustmentRequestDetailShopifySynchronizedPayloadDto.builder()
            .itemAdjustmentRequestDetailId(detailDto.getId())
            .data(detailDto)
            .build());
    }

    @Override
    public void syncToShopifyFailure(UUID id, String failureReason) {
        ItemAdjustmentRequestDetail detail = fetchItemAdjustmentRequestDetailOrThrow(id);

        detail = detail.syncToShopifyFailure(failureReason);
        itemAdjustmentRequestDetailRepository.save(detail);
    }

    private void processCreateItemRequest(ItemAdjustmentRequestDetailDto requestDetailDto) {
        CreateItemCommand createItemCommand = covertToCreateItemCommand(requestDetailDto);
        itemApplicationService.create(createItemCommand);
    }

    private void processUpdateItemRequest(ItemAdjustmentRequestDetailDto requestDetailDto) {
        Item item = itemService.findBySku(requestDetailDto.getSku());
        if (item == null) {
            throw new ImsBusinessException(ITEM_NOT_FOUND.getCode());
        }
        if (requestDetailDto.getInventory() != null) {
            log.warn("Inventory is not supported in item adjustment request, please use stock adjustment request");
            return;
        }
        checkAndUpdateVendor(requestDetailDto, item.getId());
        if (requestDetailDto.isItemChanged()) {
            itemApplicationService.update(covertToUpdateItemCommand(requestDetailDto, item));
        }
    }

    private void processDeleteItemRequest(ItemAdjustmentRequestDetailDto requestDetailDto) {

        Item item = itemService.findBySku(requestDetailDto.getSku());
        if (item == null) {
            throw new ImsBusinessException(ITEM_NOT_FOUND.getCode());
        }
        DeleteItemCommand deleteItemCommand = DeleteItemCommand.builder()
            .id(item.getId())
            .itemAdjustmentRequestDetailId(requestDetailDto.getId())
            .build();
        itemApplicationService.deleteItemById(deleteItemCommand);
    }

    private void processCleanUpcRequest(ItemAdjustmentRequestDetailDto requestDetailDto) {
        Item item = itemService.findBySku(requestDetailDto.getSku());
        if (item == null) {
            throw new ImsBusinessException(ITEM_NOT_FOUND.getCode());
        }
        CleanItemUpcCommand cleanItemUpcCommand = CleanItemUpcCommand.builder()
            .id(item.getId())
            .itemAdjustmentRequestDetailId(requestDetailDto.getId())
            .build();

        itemApplicationService.cleanItemUPCs(cleanItemUpcCommand);
    }

    private CreateItemCommand covertToCreateItemCommand(ItemAdjustmentRequestDetailDto requestDetailDto) {
        Brand brand = parsingBrand(requestDetailDto.getBrand());

        List<ItemAttributeDto> itemAttributes = buildItemAttributes(new ArrayList<>(),
            requestDetailDto.getDepartment(),
            requestDetailDto.getItemSize(),
            requestDetailDto.getItemUnitMeasure(),
            requestDetailDto.getCaseWeight(),
            requestDetailDto.getCaseWeightUnit(),
            requestDetailDto.getEachWeight(),
            requestDetailDto.getEachWeightUnit(),
            requestDetailDto.getCooler());
        List<ItemUPCDto> itemUPCs = buildItemUPCs(requestDetailDto.getUpc(),
            requestDetailDto.getCaseUpc(),
            requestDetailDto.getEachUpc(), null);
        Vendor primaryVendor = getVendor(requestDetailDto.getPrimaryPoVendor());
        UUID primaryVendorId = Objects.nonNull(primaryVendor) ? primaryVendor.getId() : null;
        UUID backupVendorId = null;

        if (StringUtils.isNotBlank(requestDetailDto.getPrimaryJitVendor()) && !requestDetailDto.getPrimaryJitVendor()
            .equals(NO_SECONDARY)
            && !requestDetailDto.getPrimaryJitVendor()
            .equals(NO_BACKUP)) {
            Vendor backupVendor = getVendor(requestDetailDto.getPrimaryJitVendor());
            backupVendorId = Objects.nonNull(backupVendor) ? backupVendor.getId() : null;
        }

        List<VendorItemDto> vendorItems = buildVendorItemsForCreate(primaryVendorId,
            backupVendorId,
            requestDetailDto.getVendorItemNumber(),
            requestDetailDto.getPrimaryPoVendorItemCost(),
            requestDetailDto.getPrimaryJitVendorItemCost(),
            requestDetailDto.getPrimaryVendorItemAisle());
        List<ItemTagDto> itemTags = buildIdTags(requestDetailDto.getTags());

        UUID categoryId = null;
        // Find category ID based on the category hierarchy
        categoryId = findCategoryIdFromHierarchy(
            requestDetailDto.getDepartment(),
            requestDetailDto.getCategory(),
            requestDetailDto.getSubCategory(),
            requestDetailDto.getClassType()
        );
        List<ItemPromoPriceDto> itemPromoPrices = new ArrayList<>();
        if (requestDetailDto.getPromoPricePackNoCrv() != null) {
            itemPromoPrices.add(creteItemPromoPrice(requestDetailDto.getPromoFlag(), requestDetailDto.getPromoPricePackNoCrv()));
        }

        return CreateItemCommand.builder().brandId(Objects.nonNull(brand) ? brand.getId() : null)
            .department(requestDetailDto.getDepartment())
            .category(requestDetailDto.getCategory())
            .subCategory(requestDetailDto.getSubCategory())
            .clazz(requestDetailDto.getClassType())
            .categoryId(categoryId) // Set the leaf category ID
            .primaryVendorId(primaryVendorId)
            .backupVendorId(backupVendorId)
            .title(requestDetailDto.getTitle())
            .name(requestDetailDto.getTitle())
            .skuNumber(requestDetailDto.getSku())
            .packageSize(requestDetailDto.getPackageSize())
            .itemAdjustmentRequestDetailId(requestDetailDto.getId())
            .companyId(requestDetailDto.getCompanyId() == null ? null : Long.valueOf(requestDetailDto.getCompanyId()))
            .locationId(requestDetailDto.getLocationId() == null ? null : Long.valueOf(requestDetailDto.getLocationId()))
            .newDescription(requestDetailDto.getNewDescription())
            .availabilityStatus(requestDetailDto.getItemStatus())
            .photoUrl(requestDetailDto.getImageUrl())
            .crvFlag(requestDetailDto.getCrvFlag())
            .regPrice(requestDetailDto.getRegPricePackNoCrv())
            .vendorItems(vendorItems)
            .itemTags(itemTags)
            .itemAttributes(itemAttributes)
            .itemUPCs(itemUPCs)
            .note(requestDetailDto.getDisposition())
            .length(requestDetailDto.getLength())
            .height(requestDetailDto.getHeight())
            .width(requestDetailDto.getWidth())
            .itemPromoPrices(itemPromoPrices)
            .build();
    }

    /**
     * Find the category ID based on the category hierarchy
     *
     * @param department Department name
     * @param category Category name
     * @param subCategory Sub-category name
     * @param clazz Class name
     * @return The category ID of the leaf node, or null if not found
     */
    private UUID findCategoryIdFromHierarchy(String department, String category, String subCategory, String clazz) {
        // If class is not provided, we can't find the category
        if (StringUtils.isBlank(department) && StringUtils.isBlank(category) && StringUtils.isBlank(subCategory)
            && StringUtils.isBlank(clazz)) {
            return null;
        }

        try {
            // Step 1: Find all leaf categories with the given name
            List<CategoryDto> leafCategories = categoryApplicationService.searchCategoriesByName(clazz);

            if (CollectionUtils.isEmpty(leafCategories)) {
                log.warn("No leaf categories found with name: {}", clazz);
                return null;
            }

            // Step 2: For each leaf category, check if its hierarchy matches
            for (CategoryDto leafCategory : leafCategories) {
                // Get the complete category tree for this leaf category
                CategoryGroupDto categoryTree = categoryApplicationService.getCategoryTreeByLeafCategoryId(
                    leafCategory.getCategoryId());

                // Step 3: Check if the hierarchy matches
                boolean matches = true;

                // Check department (depth 3)
                if (StringUtils.isNotBlank(department) &&
                    !department.equals(categoryTree.getDepartmentName())) {
                    matches = false;
                }

                // Check category (depth 2)
                if (matches && StringUtils.isNotBlank(category) &&
                    !category.equals(categoryTree.getCategoryName())) {
                    matches = false;
                }

                // Check sub-category (depth 1)
                if (matches && StringUtils.isNotBlank(subCategory) &&
                    !subCategory.equals(categoryTree.getSubCategoryName())) {
                    matches = false;
                }

                // If all checks pass, return this category ID
                if (matches) {
                    log.info("Found matching category ID {} for hierarchy: department={}, category={}, subCategory={}, class={}",
                        leafCategory.getCategoryId(), department, category, subCategory, clazz);
                    return leafCategory.getCategoryId();
                }
            }

            // If no matching category is found
            log.warn("No matching category found for hierarchy: department={}, category={}, subCategory={}, class={}",
                department, category, subCategory, clazz);
            return null;

        } catch (Exception e) {
            log.error("Error finding category ID from hierarchy: {}", e.getMessage(), e);
            return null;
        }
    }

    private UpdateItemCommand covertToUpdateItemCommand(ItemAdjustmentRequestDetailDto requestDetailDto,
        Item item) {
        Brand brand = parsingBrand(requestDetailDto.getBrand());
        Vendor primaryVendor = vendorService.findByVendorName(requestDetailDto.getPrimaryPoVendor());

        UUID backupvendorId = item.getBackupVendorId();
        if (StringUtils.isNotBlank(requestDetailDto.getPrimaryJitVendor())) {
            if (requestDetailDto.getPrimaryJitVendor().equals(NO_SECONDARY) || requestDetailDto.getPrimaryJitVendor()
                .equals(NO_BACKUP)) {
                backupvendorId = null;
            } else {
                Vendor backupvendor = getVendor(requestDetailDto.getPrimaryJitVendor());
                if (backupvendor != null) {
                    backupvendorId = backupvendor.getId();
                }
            }
        }

        String department =
            StringUtils.isBlank(requestDetailDto.getDepartment()) ? item.getDepartment() : requestDetailDto.getDepartment();

        List<ItemAttributeDto> itemAttributes = buildItemAttributes(item.getItemAttributes(),
            department,
            requestDetailDto.getItemSize(),
            requestDetailDto.getItemUnitMeasure(),
            requestDetailDto.getCaseWeight(),
            requestDetailDto.getCaseWeightUnit(),
            requestDetailDto.getEachWeight(),
            requestDetailDto.getEachWeightUnit(),
            requestDetailDto.getCooler()
        );
        List<ItemUPCDto> itemUPCs = buildItemUPCs(requestDetailDto.getUpc(),
            requestDetailDto.getCaseUpc(),
            requestDetailDto.getEachUpc(), item);
        List<ItemTagDto> itemTags = buildIdTags(requestDetailDto.getTags());
        List<ItemPromoPriceDto> itemPromoPrices = buildItemPromoPrices(item.getId(), requestDetailDto.getPromoFlag(),
            requestDetailDto.getPromoPricePackNoCrv());

        UUID categoryId = null;
        // Find category ID based on the category hierarchy
        categoryId = findCategoryIdFromHierarchy(
            requestDetailDto.getDepartment(),
            requestDetailDto.getCategory(),
            requestDetailDto.getSubCategory(),
            requestDetailDto.getClassType()
        );

        return UpdateItemCommand.builder()
            .brandId(Objects.nonNull(brand) ? brand.getId() : null)
            .department(requestDetailDto.getDepartment())
            .category(requestDetailDto.getCategory())
            .subCategory(requestDetailDto.getSubCategory())
            .clazz(requestDetailDto.getClassType())
            .categoryId(categoryId) // Set the leaf category ID
            .primaryVendorId(primaryVendor == null ? null : primaryVendor.getId())
            .backupVendorId(backupvendorId)
            .skuNumber(requestDetailDto.getSku())
            .name(requestDetailDto.getTitle())
            .title(requestDetailDto.getTitle())
            .packageSize(requestDetailDto.getPackageSize())
            .itemAdjustmentRequestDetailId(requestDetailDto.getId())
            .companyId(requestDetailDto.getCompanyId() == null ? null : Long.valueOf(requestDetailDto.getCompanyId()))
            .locationId(requestDetailDto.getLocationId() == null ? null : Long.valueOf(requestDetailDto.getLocationId()))
            .newDescription(requestDetailDto.getNewDescription())
            .availabilityStatus(requestDetailDto.getItemStatus())
            .photoUrl(requestDetailDto.getImageUrl())
            .note(requestDetailDto.getDisposition())
            .crvFlag(requestDetailDto.getCrvFlag())
            .regPrice(requestDetailDto.getRegPricePackNoCrv())
            .itemPromoPrices(itemPromoPrices)
            .itemTags(itemTags)
            .itemAttributes(itemAttributes)
            .itemUPCs(itemUPCs)
            .length(requestDetailDto.getLength())
            .height(requestDetailDto.getHeight())
            .width(requestDetailDto.getWidth())
            .build();

    }

    private Brand parsingBrand(String brandName) {
        if (StringUtils.isBlank(brandName)) {
            return null;
        }
        Brand brand = brandService.findByName(brandName);
        if (null == brand) {
            throw new ImsBusinessException(BRAND_NOT_FOUND);
        }

        return brand;
    }

    private Vendor getVendor(String vendorName) {
        if (StringUtils.isBlank(vendorName)) {
            return null;
        }
        Vendor vendor = vendorService.findByVendorName(vendorName);
        if (Objects.isNull(vendor)) {
            throw new ImsBusinessException(VENDOR_NOT_FOUND);
        }
        return vendor;
    }

    private void checkAndUpdateVendor(ItemAdjustmentRequestDetailDto requestDetailDto, UUID itemId) {

        String vendorItemType = requestDetailDto.getVendorItemType();

        if (requestDetailDto.isVendorChanged()) {

            Vendor vendor = getVendor(requestDetailDto.getVendor());
            UUID vendorId = vendor == null ? null : vendor.getId();
            checkAndUpdateVendorItem(vendorId, requestDetailDto.getVendorItemNumber(),
                requestDetailDto.getVendorAisle(), requestDetailDto.getPoVendorItemCost(), itemId, requestDetailDto.getId(),
                requestDetailDto.getVendorItemAvailability(), requestDetailDto.getJitVendorItemCost(),
                vendorItemType);
        }

        UUID primaryVendorId = null;
        UUID backupVendorId = null;
        if (requestDetailDto.isPrimaryVendorItemChanged() || requestDetailDto.isBackupVendorItemChanged()) {
            Item item = itemService.findById(itemId);

            primaryVendorId = StringUtils.isBlank(requestDetailDto.getPrimaryPoVendor())
                ? item.getPrimaryVendorId()
                : Optional.ofNullable(getVendor(requestDetailDto.getPrimaryPoVendor()))
                    .map(Vendor::getId)
                    .orElse(null);

            backupVendorId = StringUtils.isBlank(requestDetailDto.getPrimaryJitVendor())
                ? item.getBackupVendorId()
                : Optional.ofNullable(getVendor(requestDetailDto.getPrimaryJitVendor()))
                    .map(Vendor::getId)
                    .orElse(null);
        }

        if (requestDetailDto.isPrimaryVendorItemChanged() && primaryVendorId != null) {
            String calculateVendorItemType =
                Objects.equals(primaryVendorId, backupVendorId) ? VendorItemType.DIRECT_JIT.getTypeName()
                    : VendorItemType.DIRECT.getTypeName();
            checkAndUpdateVendorItem(primaryVendorId,
                requestDetailDto.getVendorItemNumber(),
                requestDetailDto.getPrimaryVendorItemAisle(),
                requestDetailDto.getPrimaryPoVendorItemCost(),
                itemId,
                requestDetailDto.getId(),
                requestDetailDto.getVendorItemAvailability(),
                null,
                vendorItemType != null ? vendorItemType : calculateVendorItemType);
        }

        if (requestDetailDto.isBackupVendorItemChanged() && backupVendorId != null) {
            String calculateVendorItemType =
                Objects.equals(primaryVendorId, backupVendorId) ? VendorItemType.DIRECT_JIT.getTypeName()
                    : VendorItemType.JIT.getTypeName();
            checkAndUpdateVendorItem(backupVendorId,
                requestDetailDto.getVendorItemNumber(),
                null,
                null,
                itemId,
                requestDetailDto.getId(),
                requestDetailDto.getVendorItemAvailability(),
                requestDetailDto.getPrimaryJitVendorItemCost(),
                vendorItemType != null ? vendorItemType : calculateVendorItemType);
        }

    }

    private void checkAndUpdateVendorItem(UUID vendorId, String vendorItemNumber, String vendorItemAisle,
        BigDecimal cost, UUID itemId, UUID itemAdjustmentRequestDetailId,
        Boolean vendorItemAvailability, BigDecimal backupCost, String vendorItemType) {
        Boolean isCostRefreshed = cost != null && cost.compareTo(BigDecimal.ZERO) != 0;
        Boolean isBackupCostRefreshed = backupCost != null && backupCost.compareTo(BigDecimal.ZERO) != 0;

        VendorItem vendorItem = vendorItemService.findByVendorIDAndItemId(vendorId, itemId);
        if (vendorItem == null) {
            vendorItemApplicationService.create(CreateVendorItemCommand.builder()
                .vendorId(vendorId)
                .itemId(itemId)
                .vendorSkuNumber(vendorItemNumber)
                .aisle(vendorItemAisle)
                .cost(cost)
                .backupCost(backupCost)
                .itemAdjustmentRequestDetailId(itemAdjustmentRequestDetailId)
                .availability(vendorItemAvailability)
                .vendorItemType(vendorItemType)
                .build());
        } else {
            if (StringUtils.isBlank(vendorItemNumber)) {
                vendorItemNumber = vendorItem.getVendorSkuNumber();
            }
            if (StringUtils.isBlank(vendorItemAisle)) {
                vendorItemAisle = vendorItem.getAisle();
            }
            if (cost == null || cost.compareTo(BigDecimal.ZERO) == 0) {
                cost = vendorItem.getPackPlusCrvCost();
            }
            if (backupCost == null || backupCost.compareTo(BigDecimal.ZERO) == 0) {
                backupCost = vendorItem.getBackupPackPlusCrvCost();
            }
            if (vendorItemAvailability == null) {
                vendorItemAvailability = vendorItem.getAvailability();
            }
            if (vendorItemType == null) {
                vendorItemType = vendorItem.getVendorItemType();
            }
            vendorItemApplicationService.update(UpdateVendorItemCommand.builder()
                .vendorItemId(vendorItem.getId())
                .vendorSkuNumber(vendorItemNumber)
                .aisle(vendorItemAisle)
                .cost(cost)
                .backupCost(backupCost)
                .note(vendorItem.getNote())
                .vendorItemName(vendorItem.getVendorItemName())
                .isCostRefreshed(isCostRefreshed)
                .isBackupCostRefreshed(isBackupCostRefreshed)
                .itemAdjustmentRequestDetailId(itemAdjustmentRequestDetailId)
                .availability(vendorItemAvailability)
                .vendorItemType(vendorItemType)
                .build());
        }
    }

    private ItemAdjustmentRequestDetail fetchItemAdjustmentRequestDetailOrThrow(UUID id) {
        return Optional.ofNullable(itemAdjustmentRequestDetailRepository.findById(id))
            .orElseThrow(() -> new ImsBusinessException(ITEM_ADJUSTMENT_NOT_FOUND.getCode()));
    }

    private List<ItemUPCDto> buildItemUPCs(String upcs, String caseUpc, String eachUpc, Item item) {
        if (item == null || CollectionUtils.isEmpty(item.getItemUPCs())) {
            List<ItemUPCDto> itemUPCList = new ArrayList<>();
            if (StringUtils.isNotBlank(upcs)) {
                Arrays.stream(upcs.split(","))
                    .map(String::trim)
                    .filter(StringUtils::isNotBlank)
                    .map(u -> ItemUPCDto.builder().upcNumber(u).itemUpcType(ItemUpcType.VARIANT_BARCODE).build())
                    .forEach(itemUPCList::add);
            }
            if (StringUtils.isNotBlank(caseUpc)) {
                itemUPCList.add(ItemUPCDto.builder().upcNumber(caseUpc).itemUpcType(ItemUpcType.CASE_UPC).build());
            }
            if (StringUtils.isNotBlank(eachUpc)) {
                itemUPCList.add(ItemUPCDto.builder().upcNumber(eachUpc).itemUpcType(ItemUpcType.EACH_UPC).build());
            }
            return itemUPCList;
        }

        List<ItemUPCDto> resultUpcs = item.getItemUPCs().stream()
            .map(itemUPC -> ItemUPCDto.builder()
                .upcNumber(itemUPC.getUpcNumber())
                .itemUpcType(itemUPC.getItemUpcType())
                .build())
            .collect(Collectors.toList());

        if (StringUtils.isNotBlank(eachUpc)) {
            resultUpcs.removeIf(u -> u.getItemUpcType() == ItemUpcType.EACH_UPC);
            resultUpcs.add(ItemUPCDto.builder().upcNumber(eachUpc).itemUpcType(ItemUpcType.EACH_UPC).build());
        }

        if (StringUtils.isNotBlank(caseUpc)) {
            resultUpcs.removeIf(u -> u.getItemUpcType() == ItemUpcType.CASE_UPC);
            resultUpcs.add(ItemUPCDto.builder().upcNumber(caseUpc).itemUpcType(ItemUpcType.CASE_UPC).build());
        }

        if (StringUtils.isNotBlank(upcs)) {
            resultUpcs.removeIf(u -> u.getItemUpcType() == ItemUpcType.VARIANT_BARCODE);
            Arrays.stream(upcs.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .map(u -> ItemUPCDto.builder().upcNumber(u).itemUpcType(ItemUpcType.VARIANT_BARCODE).build())
                .forEach(resultUpcs::add);
        }

        return resultUpcs;
    }

    private List<VendorItemDto> buildVendorItemsForCreate(UUID primaryVendorId, UUID backupVendorId, String vendorItemNumber,
        BigDecimal cost,
        BigDecimal backupCost,
        String vendorItemAisle) {
        List<VendorItemDto> vendorItems = new ArrayList<>();
        if (primaryVendorId != null) {
            vendorItems.add(VendorItemDto.builder()
                .vendorId(primaryVendorId)
                .vendorSkuNumber(vendorItemNumber)
                .cost(cost)
                .backupCost(primaryVendorId.equals(backupVendorId) ? backupCost : null)
                .vendorItemType(primaryVendorId.equals(backupVendorId) ? VendorItemType.DIRECT_JIT.getTypeName()
                    : VendorItemType.DIRECT.getTypeName())
                .aisle(vendorItemAisle)
                .build());
        }
        if (backupVendorId != null && !backupVendorId.equals(primaryVendorId)) {
            vendorItems.add(VendorItemDto.builder()
                .vendorId(backupVendorId)
                .backupCost(backupCost)
                .vendorItemType(VendorItemType.JIT.getTypeName())
                .build());
        }
        return vendorItems;
    }

    private List<ItemTagDto> buildIdTags(String tags) {
        if (StringUtils.isBlank(tags)) {
            return null;
        }
        return Arrays.stream(tags.split(","))
            .filter(StringUtils::isNotBlank)
            .distinct()
            .map(String::trim)
            .map(tag -> ItemTagDto.builder().tagName(tag).build())
            .toList();
    }

    private List<ItemPromoPriceDto> buildItemPromoPrices(UUID itemId, Boolean promoFlag, BigDecimal promoPrice) {
        List<ItemPromoPriceDto> itemPromoPrices = new ArrayList<>();
        ItemPromoPrice itemPromoPrice = itemPromoPriceService.findByItemId(itemId).stream().findFirst().orElse(null);

        if (itemPromoPrice == null && promoFlag == null && promoPrice == null) {
            log.warn("ItemPromoPrice not found as updateItemPromoPrice id: {}", itemId);
            return itemPromoPrices;
        }
        if (itemPromoPrice == null) {
            ItemPromoPriceDto prmo = creteItemPromoPrice(promoFlag, promoPrice);
            itemPromoPrices.add(prmo);
            return itemPromoPrices;
        }

        promoPrice = (promoPrice != null) ? promoPrice
            : Optional.ofNullable(itemPromoPrice).map(ItemPromoPrice::getPromoPrice).orElse(null);
        promoFlag = (promoFlag != null) ? promoFlag : Optional.ofNullable(itemPromoPrice).map(ItemPromoPrice::getPromoFlag)
            .orElse(null);
        Instant promoBeginTime = Optional.ofNullable(itemPromoPrice).map(ItemPromoPrice::getPromoBeginTime).orElse(null);
        Instant promoEndTime = Optional.ofNullable(itemPromoPrice).map(ItemPromoPrice::getPromoEndTime).orElse(null);

        assert itemPromoPrice != null;
        ItemPromoPriceDto prmo = ItemPromoPriceDto.builder()
            .promoFlag(promoFlag)
            .promoPrice(promoPrice)
            .promoBeginTime(promoBeginTime)
            .promoEndTime(promoEndTime).build();
        itemPromoPrices.add(prmo);

        return itemPromoPrices;
    }

    private ItemPromoPriceDto creteItemPromoPrice(Boolean promoFlag, BigDecimal promoPrice) {
        assert promoPrice != null;
        return ItemPromoPriceDto.builder()
            .promoFlag(promoFlag)
            .promoPrice(promoPrice)
            .build();
    }

    public List<ItemAttributeDto> buildItemAttributes(
        List<ItemAttribute> attributeList,
        String department,
        Float itemSize,
        String itemUnitMeasure,
        Double caseWeight,
        String caseWeightUnit,
        Double eachWeight,
        String eachWeightUnit,
        Boolean cooler) {

        List<ItemAttribute> updatedAttributes = new ArrayList<>(attributeList);

        if (itemSize != null && StringUtils.isNotBlank(itemUnitMeasure)) {
            updatedAttributes = updateOrAddAttribute(
                updatedAttributes,
                AttributeConstant.ITEM_SIZE_ATTRIBUTE_ID,
                itemSize.toString(),
                itemUnitMeasure
            );

            if (StringUtils.isNotBlank(department) && department.equals(CategoryConstant.BEVERAGE)) {
                updatedAttributes = updateOrAddAttribute(
                    updatedAttributes,
                    AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID,
                    itemSize.toString(),
                    itemUnitMeasure
                );
            }
        }

        if (caseWeight != null && StringUtils.isNotBlank(caseWeightUnit)) {
            updatedAttributes = updateOrAddAttribute(
                updatedAttributes,
                AttributeConstant.CASE_WEIGHT_ATTRIBUTE_ID,
                caseWeight.toString(),
                caseWeightUnit
            );
        }

        if (eachWeight != null && StringUtils.isNotBlank(eachWeightUnit)) {
            updatedAttributes = updateOrAddAttribute(
                    updatedAttributes,
                    AttributeConstant.EACH_WEIGHT_ATTRIBUTE_ID,
                    eachWeight.toString(),
                    eachWeightUnit
            );
        }

        if (cooler != null) {
            updatedAttributes = updateOrAddAttribute(
                updatedAttributes,
                AttributeConstant.COOLER_ATTRIBUTE_ID,
                cooler.toString(),
                null
            );
        }

        return itemDtoApplicationMapper.mapItemAttributes(updatedAttributes);
    }


    private List<ItemAttribute> updateOrAddAttribute(
        List<ItemAttribute> attributeList,
        UUID attributeId,
        String value,
        String unit) {

        List<ItemAttribute> updatedList = new ArrayList<>(attributeList);

        ItemAttribute attribute = updatedList.stream()
            .filter(attr -> attr.getAttributeId().equals(attributeId))
            .findFirst()
            .orElse(null);

        if (attribute == null) {
            updatedList.add(
                ItemAttribute.builder()
                    .attributeId(attributeId)
                    .value(value)
                    .unit(unit)
                    .attributeType(AttributeType.KEY_ATTRIBUTES)
                    .build()
            );
        } else {
            attribute.setValue(value);
            attribute.setUnit(unit);
        }

        return updatedList;
    }

}
