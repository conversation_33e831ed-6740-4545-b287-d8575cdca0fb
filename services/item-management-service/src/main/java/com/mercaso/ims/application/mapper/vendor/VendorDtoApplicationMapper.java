package com.mercaso.ims.application.mapper.vendor;

import com.mercaso.ims.application.dto.VendorDto;
import com.mercaso.ims.application.mapper.BaseDtoApplicationMapper;
import com.mercaso.ims.domain.vendor.Vendor;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface VendorDtoApplicationMapper extends BaseDtoApplicationMapper<Vendor, VendorDto> {

    @Override
    VendorDto domainToDto(Vendor domain);

}
