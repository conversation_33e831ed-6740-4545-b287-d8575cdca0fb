package com.mercaso.ims.infrastructure.repository.vendor.jpa;

import com.mercaso.ims.infrastructure.repository.vendor.jpa.dataobject.VendorDo;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;

public interface VendorJpaDao extends JpaRepository<VendorDo, UUID> {

    VendorDo findByVendorName(String vendorName);

    VendorDo findByFinaleId(String finaleId);

    List<VendorDo> findAllByIdIn(List<UUID> ids);

    List<VendorDo> findByVendorNameContainsIgnoreCase(String vendorName);

    List<VendorDo> findByExternalPicking(Boolean externalPicking);
}
