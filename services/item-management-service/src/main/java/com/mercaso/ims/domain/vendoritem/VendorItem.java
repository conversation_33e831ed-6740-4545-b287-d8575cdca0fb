package com.mercaso.ims.domain.vendoritem;

import com.mercaso.ims.domain.BaseDomain;
import com.mercaso.ims.domain.vendoritem.enums.VendorItemStatus;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
@Getter
@Setter
@ToString
@SuperBuilder
public class VendorItem extends BaseDomain {

    private final UUID id;

    private UUID vendorId;

    private UUID itemId;

    private String vendorSkuNumber;

    private String vendorItemName;

    private String note;

    private String statusChangeReason;

    private String aisle;

    private BigDecimal lowestCost;

    private BigDecimal highestCost;

    private BigDecimal packPlusCrvCost;

    private BigDecimal packNoCrvCost;

    private BigDecimal individualCost;

    private BigDecimal secondaryPackPlusCrvCost;

    private BigDecimal secondaryPackNoCrvCost;

    private BigDecimal secondaryIndividualCost;

    private VendorItemStatus vendorItemStatus;

    private Instant costFreshnessTime;

    private Boolean availability;

    private BigDecimal backupPackPlusCrvCost;

    private Instant backupCostFreshnessTime;

    private String vendorItemType;

    private Integer leadTimeDays;

    private Integer moq;


    public void updateVendorSkuNumber(String vendorSkuNumber) {
        this.vendorSkuNumber = vendorSkuNumber;
    }

    public void updateDetails(String vendorItemName, String note, String aisle) {
        this.vendorItemName = vendorItemName;
        this.note = note;
        this.aisle = aisle;
    }

    public void updateStatus(VendorItemStatus vendorItemStatus, String statusChangeReason) {
        this.vendorItemStatus = vendorItemStatus;
        this.statusChangeReason = statusChangeReason;
    }

    public void updateAvailability(Boolean availability) {
        if (availability != null) {
            this.availability = availability;
        }
    }

    public void updateCost(BigDecimal packPlusCrvCost, Boolean isCostRefreshed) {
        if (Boolean.TRUE.equals(isCostRefreshed)) {
            this.costFreshnessTime = Instant.now();
        }
        this.packPlusCrvCost = packPlusCrvCost;
    }

    public void updateBackupCost(BigDecimal backupPackPlusCrvCost, Boolean isBackupCostRefreshed) {
        if (Boolean.TRUE.equals(isBackupCostRefreshed)) {
            this.backupCostFreshnessTime = Instant.now();
        }
        this.backupPackPlusCrvCost = backupPackPlusCrvCost;
    }


    public BigDecimal getSecondaryCost() {
        if (this.secondaryPackPlusCrvCost != null) {
            return this.secondaryPackPlusCrvCost;
        } else if (this.secondaryPackNoCrvCost != null) {
            return this.secondaryPackNoCrvCost;
        } else {
            return this.secondaryIndividualCost;
        }
    }

    public BigDecimal getCost() {
        if (this.packPlusCrvCost != null) {
            return this.packPlusCrvCost;
        } else if (this.packNoCrvCost != null) {
            return this.packNoCrvCost;
        } else {
            return this.individualCost;
        }
    }

    public BigDecimal getCostOrBackupCost() {
        if (this.packPlusCrvCost != null) {
            return this.packPlusCrvCost;
        } else {
            return this.backupPackPlusCrvCost;
        }
    }

    public void updateVendorItemType(String vendorItemType) {
        if (StringUtils.isNotBlank(vendorItemType)) {
            this.vendorItemType = vendorItemType;
        }
    }

}
