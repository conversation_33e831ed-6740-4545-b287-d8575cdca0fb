package com.mercaso.ims.application.queryservice.impl;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.ITEM_NOT_FOUND;

import com.mercaso.ims.application.dto.CategoryGroupDto;
import com.mercaso.ims.application.dto.ItemAttributeDto;
import com.mercaso.ims.application.dto.ItemCategoryDto;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.ItemImageDto;
import com.mercaso.ims.application.dto.ItemPackingInfoDto;
import com.mercaso.ims.application.dto.ItemPromoPriceDto;
import com.mercaso.ims.application.dto.ItemRegPriceDto;
import com.mercaso.ims.application.dto.VendorItemDto;
import com.mercaso.ims.application.mapper.brand.BrandDtoApplicationMapper;
import com.mercaso.ims.application.mapper.item.ItemDtoApplicationMapper;
import com.mercaso.ims.application.mapper.itempackinginfo.ItemPickingInfoDtoApplicationMapper;
import com.mercaso.ims.application.mapper.itempromoprice.ItemPromoPriceDtoApplicationMapper;
import com.mercaso.ims.application.mapper.itemregprice.ItemRegPriceDtoApplicationMapper;
import com.mercaso.ims.application.queryservice.ItemQueryApplicationService;
import com.mercaso.ims.application.queryservice.VendorItemQueryApplicationService;
import com.mercaso.ims.application.service.CategoryApplicationService;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.domain.attribute.Attribute;
import com.mercaso.ims.domain.attribute.AttributeConstant;
import com.mercaso.ims.domain.attribute.service.AttributeService;
import com.mercaso.ims.domain.brand.Brand;
import com.mercaso.ims.domain.brand.service.BrandService;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.ItemRepository;
import com.mercaso.ims.domain.item.service.ItemService;
import com.mercaso.ims.domain.itemattribute.ItemAttribute;
import com.mercaso.ims.domain.itempricegroup.ItemPriceGroup;
import com.mercaso.ims.domain.itempricegroup.service.impl.ItemPriceGroupServiceImpl;
import com.mercaso.ims.domain.itempromoprice.ItemPromoPrice;
import com.mercaso.ims.domain.itempromoprice.service.ItemPromoPriceService;
import com.mercaso.ims.domain.itemregprice.ItemRegPrice;
import com.mercaso.ims.domain.itemregprice.service.ItemRegPriceService;
import com.mercaso.ims.domain.itemversion.ItemVersion;
import com.mercaso.ims.domain.itemversion.service.ItemVersionService;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.item.jpa.CustomizedItemJpaDao;
import com.mercaso.ims.infrastructure.util.SerializationUtils;
import java.time.Instant;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class ItemQueryApplicationServiceImpl implements ItemQueryApplicationService {

    private final ItemService itemService;

    private final BrandService brandService;

    private final VendorService vendorService;

    private final AttributeService attributeService;

    private final ItemRegPriceService itemRegPriceService;

    private final ItemPromoPriceService itemPromoPriceService;

    private final DocumentApplicationService documentApplicationService;

    private final ItemDtoApplicationMapper itemMapper;

    private final BrandDtoApplicationMapper brandMapper;


    private final ItemRegPriceDtoApplicationMapper itemRegPriceMapper;

    private final ItemPromoPriceDtoApplicationMapper itemPromoPriceMapper;

    private final ItemPickingInfoDtoApplicationMapper itemPickingInfoDtoMapper;
    private final CustomizedItemJpaDao customizedItemJpaDao;

    private final ItemRepository itemRepository;
    private final ItemPriceGroupServiceImpl itemPriceGroupServiceImpl;

    private final CategoryApplicationService categoryApplicationService;

    private final VendorItemQueryApplicationService vendorItemQueryApplicationService;

    private final ItemVersionService itemVersionService;

    @Override
    public ItemDto findById(UUID id) {
        Item item = itemService.findById(id);
        if (null == item) {
            log.error("Item not found for id: {}", id);
            throw new ImsBusinessException(ITEM_NOT_FOUND);
        }

        ItemDto itemDto = itemMapper.domainToDto(item);
        populateItemRelatedData(itemDto, item);

        return itemDto;
    }

    @Override
    public ItemDto findBySku(String sku) {
        Item item = itemService.findBySku(sku);
        if (null == item) {
            log.warn("Item not found for sku: {}", sku);
            return null;
        }

        ItemDto itemDto = itemMapper.domainToDto(item);
        populateItemRelatedData(itemDto, item);

        return itemDto;
    }

    @Override
    public Page<ItemDto> findPagedItems(Pageable pageable) {
        Page<Item> itemsPage = itemRepository.findAll(pageable);
        return itemsPage.map(itemMapper::domainToDto);
    }

    @Override
    public List<ItemCategoryDto> findItemCategoryByIdIn(List<UUID> ids) {
        List<ItemCategoryDto> itemCategoryDtos = customizedItemJpaDao.findItemCategoryByIdIn(ids);
        itemCategoryDtos.forEach(
            dto -> {
                populateItemAttributeInfo(dto);
                populateVendorItemInfo(dto);
            }
        );
        return itemCategoryDtos;
    }

    @Override
    public List<ItemCategoryDto> findItemCategoryBySkuIn(List<String> skus) {
        if (CollectionUtils.isEmpty(skus)) {
            log.warn("The sku list is empty");
            return List.of();
        }
        List<ItemCategoryDto> itemCategoryDtos = customizedItemJpaDao.findItemCategoryBySkuIn(skus);
        itemCategoryDtos.forEach(
            dto -> {
                populateItemAttributeInfo(dto);
                populateVendorItemInfo(dto);
            }
        );
        return itemCategoryDtos;
    }

    @Override
    public List<ItemPackingInfoDto> findItemsPackingInfoByIdIn(List<UUID> ids) {
        Map<UUID, Vendor> vendorMap = vendorService.findAllExternalPickingVendor(true).stream()
            .collect(Collectors.toMap(Vendor::getId, v -> v));

        List<Item> items = itemRepository.findAllByIdIn(ids);
        return items.stream().map(item -> {
            ItemPackingInfoDto packingInfoDto = itemPickingInfoDtoMapper.domainToDto(item);
            packingInfoDto.setBackupVendorName(
                item.getBackupVendorId() == null ? null : vendorMap.get(item.getBackupVendorId()).getVendorName());
            packingInfoDto.setBottleSize(getBottleSizeStr(item.getItemAttributes()));
            packingInfoDto.setPhotoUrl(
                item.getPhoto() == null ? null : documentApplicationService.getTimelinessUrl(item.getPhoto()));
            List<VendorItemDto> vendorItemDtos = vendorItemQueryApplicationService.findByItemId(item.getId());
            packingInfoDto.setVendorItemDtos(vendorItemDtos);
            return packingInfoDto;
        }).toList();
    }

    @Override
    public Page<ItemDto> findPagedItemsByUpdateAt(Pageable pageable, Instant startTime, Instant endTime) {
        Page<Item> itemsPage = itemRepository.findByUpdatedAtBetween(startTime, endTime, pageable);
        return itemsPage.map(itemMapper::domainToDto);
    }

    @Override
    public List<ItemDto> findByUpcNumber(String upcNumber) {
        return itemRepository.findByUpcNumber(upcNumber).stream().map(itemMapper::domainToDto).distinct().toList();
    }

    @Override
    public List<ItemDto> findByIdIn(List<UUID> ids) {
        return itemRepository.findAllByIdIn(ids).parallelStream().map(itemMapper::domainToDto).toList();
    }

    @Override
    public ItemCategoryDto findItemCategoryByIdAndVersionNumber(UUID itemId, Integer versionNumber) {
        ItemVersion itemVersion = itemVersionService.findByItemIdAndVersion(itemId, versionNumber);

        ItemDto itemDto;
        if (null == itemVersion) {
            itemDto = findById(itemId);
        } else {
            itemDto = SerializationUtils.deserialize(itemVersion.getItemData(), ItemDto.class);
        }
        if (null == itemDto) {
            log.error("Item not found for id: {}", itemId);
            throw new ImsBusinessException(ITEM_NOT_FOUND);
        }
        ItemCategoryDto itemCategoryDto = buildItemCategoryDto(itemDto);
        // Populate additional information
        populateVendorItemInfo(itemCategoryDto);

        return itemCategoryDto;
    }

    @Override
    public List<ItemDto> findByCategoryId(UUID categoryId) {
        return itemRepository.findByCategoryId(categoryId).stream().map(itemMapper::domainToDto).distinct().toList();
    }

    private void populateItemRelatedData(ItemDto itemDto, Item item) {
        //set item image
        populateItemImageUrl(itemDto);

        //set brand
        populateBrandInfo(item.getBrandId(), itemDto);
        //set vendor item
        populateVendorItemInfo(item, itemDto);
        //set item attribute
        populateItemAttributeInfo(itemDto);

        //set item reg price
        populateItemRegPrice(itemDto);
        //set item promo price
        populateItemPromoPrice(itemDto);
        //set category info
        populateCategoryInfo(itemDto);
        //set photoUrl
        populatePhotoUrl(itemDto);
        populateLastVersionNumber(itemDto);
    }

    /**
     * Populate category information for the item based on feature flag
     *
     * @param itemDto the item DTO to populate
     */
    private void populateCategoryInfo(ItemDto itemDto) {
        if (itemDto == null) {
            return;
        }
        if (null == itemDto.getCategoryId()) {
            itemDto.clearCategoryInfo();
            return;
        }

        try {
            // Get category hierarchy with names directly from the database
            CategoryGroupDto categoryGroupDto = categoryApplicationService.getCategoryTreeByLeafCategoryId(itemDto.getCategoryId());

            if (categoryGroupDto != null) {
                itemDto.setCategoryGroup(categoryGroupDto);
            }

        } catch (Exception e) {
            log.error("Error populating category info for item {}: {}", itemDto.getId(), e.getMessage());
        }
    }

    private void populatePhotoUrl(ItemDto itemDto) {
        if (StringUtils.isBlank(itemDto.getPhotoName())) {
            log.warn("[populatePhotoUrl] This item has no photo, Id: {}, SkuNum:{}", itemDto.getId(), itemDto.getSkuNumber());
            return;
        }
        itemDto.setPhotoUrl(documentApplicationService.getImsUrl(itemDto.getPhotoName()));
    }

    private void populateBrandInfo(UUID brandId, ItemDto itemDto) {
        if (null == brandId) {
            log.warn("[populateBrandInfo] This item has no brand, Id: {}, SkuNum:{}", itemDto.getId(), itemDto.getSkuNumber());
            return;
        }

        Brand brand = brandService.findById(brandId);
        if (null == brand) {
            log.warn("[populateBrandInfo] Not found the brand, itemId: {}, SkuNum:{}, brandId:{}",
                itemDto.getId(),
                itemDto.getSkuNumber(),
                brandId);
            return;
        }
        itemDto.setBrand(brandMapper.domainToDto(brand));
    }

    private void populateVendorItemInfo(Item item, ItemDto itemDto) {
        List<VendorItemDto> vendorItemDtos = vendorItemQueryApplicationService.findByItemId(item.getId());

        Map<UUID, VendorItemDto> vendorItemMap = vendorItemDtos.stream()
            .collect(Collectors.toMap(
                VendorItemDto::getVendorId,
                Function.identity(),
                (existing, replacement) -> existing
            ));

        Optional.ofNullable(vendorItemMap.get(item.getPrimaryVendorId()))
            .ifPresentOrElse(
                itemDto::setPrimaryVendorItem,
                () -> logError("Not found the primary vendor item", item)
            );

        Optional.ofNullable(vendorItemMap.get(item.getBackupVendorId()))
            .ifPresentOrElse(
                itemDto::setBackupVendorItem,
                () -> logError("Not found the backup vendor item", item)
            );

        itemDto.setVendorItemDtos(vendorItemDtos);
    }


    private void logError(String message, Item item) {
        log.warn("[populateVendorItemInfo] {} itemId: {}, SkuNum:{}", message, item.getId(), item.getSkuNumber());
    }

    private void logError(String message, String skuNumber) {
        log.warn("[populateVendorItemInfo] {} itemId: {}, SkuNumber:{}", message, skuNumber);
    }


    private void populateItemAttributeInfo(ItemDto itemDto) {
        List<ItemAttributeDto> itemAttributeDtos = itemDto.getItemAttributes();
        if (CollectionUtils.isEmpty(itemAttributeDtos)) {
            return;
        }
        List<UUID> attributeIds = itemAttributeDtos.stream().map(ItemAttributeDto::getAttributeId).distinct().toList();

        Map<UUID, Attribute> attributeMap = Optional.ofNullable(attributeService.findByIds(attributeIds))
            .orElse(Collections.emptyList()).stream().collect(Collectors.toMap(Attribute::getId, attribute -> attribute));

        itemAttributeDtos = itemAttributeDtos.stream().map(itemAttributeDto -> {
            Attribute attribute = attributeMap.getOrDefault(itemAttributeDto.getAttributeId(), Attribute.builder().build());
            itemAttributeDto.setAttributeName(attribute.getName());
            return itemAttributeDto;
        }).toList();

        itemDto.setItemAttributes(itemAttributeDtos);
    }

    private void populateItemImageUrl(ItemDto itemDto) {
        List<ItemImageDto> itemImages = itemDto.getItemImages();
        if (CollectionUtils.isEmpty(itemImages)) {
            log.warn("[populateItemImageUrl] Not found the item image, itemId: {}, SkuNum:{}",
                itemDto.getId(),
                itemDto.getSkuNumber());
            return;
        }
        itemImages.forEach(itemImageDto -> itemImageDto.setUrl(documentApplicationService.getImsUrl(itemImageDto.getFileName())));
    }

    private void populateItemRegPrice(ItemDto itemDto) {
        ItemRegPrice itemRegPrice = itemRegPriceService.findByItemId(itemDto.getId());
        if (null == itemRegPrice) {
            log.warn("[populateItemRegPrice] Not found the item reg price, itemId: {}, SkuNum:{}",
                itemDto.getId(),
                itemDto.getSkuNumber());
            return;
        }
        ItemRegPriceDto priceDto = itemRegPriceMapper.domainToDto(itemRegPrice);
        if (null != priceDto.getItemPriceGroupId()) {
            ItemPriceGroup priceGroup = itemPriceGroupServiceImpl.findById(priceDto.getItemPriceGroupId());
            priceDto.setItemPriceGroupName(priceGroup.getGroupName());
        }
        itemDto.setItemRegPrice(priceDto);
    }


    private void populateItemPromoPrice(ItemDto itemDto) {
        List<ItemPromoPrice> itemPromoPrices = itemPromoPriceService.findByItemId(itemDto.getId());
        if (CollectionUtils.isEmpty(itemPromoPrices)) {
            log.warn("[populateItemPromoPrice] Not found the item promo price, itemId: {}, SkuNum:{}",
                itemDto.getId(),
                itemDto.getSkuNumber());
            return;
        }
        List<ItemPromoPriceDto> itemPromoPriceDtos = itemPromoPrices.stream().map(itemPromoPriceMapper::domainToDto).toList();
        itemDto.setItemPromoPrices(itemPromoPriceDtos);
    }


    private ItemAttribute getItemBottleSizeAttribute(List<ItemAttribute> itemAttributes) {
        return itemAttributes == null ? null : itemAttributes.stream()
            .filter(attr -> attr.getAttributeId().equals(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID))
            .findFirst()
            .orElseGet(() -> itemAttributes.stream()
                .filter(attr -> attr.getAttributeId().equals(AttributeConstant.ITEM_SIZE_ATTRIBUTE_ID))
                .findFirst()
                .orElse(null));
    }


    public String getBottleSizeStr(List<ItemAttribute> itemAttributes) {
        ItemAttribute bottleSizeAttribute = getItemBottleSizeAttribute(itemAttributes);
        if (bottleSizeAttribute != null && bottleSizeAttribute.getValue() != null) {
            String unit = bottleSizeAttribute.getUnit() == null ? "" : bottleSizeAttribute.getUnit();
            return bottleSizeAttribute.getValue() + " " + unit;
        }
        return null;
    }

    private void populateVendorItemInfo(ItemCategoryDto itemDto) {

        List<VendorItemDto> vendorItemDtos = vendorItemQueryApplicationService.findByItemId(itemDto.getId());
        List<Vendor> vendors = vendorService.findAll();
        if (vendors.isEmpty()) {
            logError("Not found the vendors", itemDto.getSkuNumber());
            return;
        }
        Map<UUID, Vendor> vendorMap = vendors.stream().collect(Collectors.toMap(Vendor::getId, Function.identity()));

        itemDto.setPrimaryVendorName(vendorMap.getOrDefault(itemDto.getPrimaryVendorId(), Vendor.builder().build())
            .getVendorName());
        itemDto.setBackupVendorName(vendorMap.getOrDefault(itemDto.getBackupVendorId(), Vendor.builder().build())
            .getVendorName());

        itemDto.setVendorItemDtos(vendorItemDtos);
    }

    private void populateItemAttributeInfo(ItemCategoryDto itemDto) {
        List<ItemAttributeDto> attributeDtos = customizedItemJpaDao.findItemAttributeDtoByItemId(itemDto.getId());
        itemDto.setItemAttributes(attributeDtos);
    }

    private ItemCategoryDto buildItemCategoryDto(ItemDto itemDto) {
        // Convert ItemDto to ItemCategoryDto
        ItemCategoryDto itemCategoryDto = new ItemCategoryDto();

        // Set basic properties
        itemCategoryDto.setId(itemDto.getId());
        itemCategoryDto.setTitle(itemDto.getTitle());
        itemCategoryDto.setSkuNumber(itemDto.getSkuNumber());
        itemCategoryDto.setPack(itemDto.getPackageSize());
        itemCategoryDto.setBrand(itemDto.getBrand() != null ? itemDto.getBrand().getBrandName() : null);
        itemCategoryDto.setBottleSize(itemDto.getBottleSizeStr());

        // Set category information
        itemCategoryDto.setDepartment(itemDto.getDepartment());
        itemCategoryDto.setCategory(itemDto.getCategory());
        itemCategoryDto.setSubCategory(itemDto.getSubCategory());
        itemCategoryDto.setClazz(itemDto.getClazz());

        // Set vendor information
        itemCategoryDto.setPrimaryVendorId(itemDto.getPrimaryVendorId());
        itemCategoryDto.setBackupVendorId(itemDto.getBackupVendorId());

        // Set CRV information if available
        if (itemDto.getItemRegPrice() != null) {
            itemCategoryDto.setCrv(itemDto.getItemRegPrice().getCrv());
        }

        // Set version information
        itemCategoryDto.setVersionNumber(itemDto.getLastVersionNumber());

        // Set item attributes
        itemCategoryDto.setItemAttributes(itemDto.getItemAttributes());

        // Set vendor items
        itemCategoryDto.setVendorItemDtos(itemDto.getVendorItemDtos());

        return itemCategoryDto;
    }

    private void populateLastVersionNumber(ItemDto itemDto) {

        List<ItemVersion> itemVersions = itemVersionService.findByItemId(itemDto.getId());

        if (itemVersions == null || itemVersions.isEmpty()) {
            itemDto.setLastVersionNumber(0);
            return;
        }

        ItemVersion latestVersion = itemVersions.stream()
            .max(Comparator.comparingInt(ItemVersion::getVersionNumber))
            .orElse(null);

        itemDto.setLastVersionNumber(latestVersion == null ? 0 : latestVersion.getVersionNumber());
    }

}
