package com.mercaso.ims.domain.item;

import com.mercaso.ims.domain.BaseDomainRepository;
import com.mercaso.ims.domain.item.enums.ItemUpcType;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface ItemRepository extends BaseDomainRepository<Item, UUID> {

    Item findBySku(String sku);

    Page<Item> findByCreatedAtBetween(Instant begin, Instant end, String photo, Pageable pageable);

    Page<Item> findAll(Pageable pageable);

    List<Item> findAllByIdIn(List<UUID> ids);

    List<Item> findAllByUpcAndUpcType(String upc, ItemUpcType upcType);

    Page<Item> findByUpdatedAtBetween(Instant begin, Instant end, Pageable pageable);

    List<Item> findByUpcNumber(String upcNumber);

    List<Item> findByCategoryId(UUID categoryId);

    List<Item> findByBrandId(UUID brandId);

    List<Item> findActiveItems();

}
