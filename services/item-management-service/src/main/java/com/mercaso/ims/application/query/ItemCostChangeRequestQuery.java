package com.mercaso.ims.application.query;

import com.mercaso.ims.infrastructure.repository.PageQuery;
import java.util.List;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@SuperBuilder
public class ItemCostChangeRequestQuery extends PageQuery {

    private SortType sortType;
    private UUID itemCostCollectionId;
    private String status;
    private List<String> itemStatus;
    private Boolean isPrimaryVendor;
    private Boolean isBackupVendor;
    private List<String> itemSkuNumbers;


    @Getter
    public enum SortType {
        MATCH_TYPE_DESC,
        STATUS_DESC,
        MARGIN_SORT_DESC,
        MARGIN_SORT_ASC,
    }


}
