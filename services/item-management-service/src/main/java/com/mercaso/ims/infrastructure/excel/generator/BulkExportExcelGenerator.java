package com.mercaso.ims.infrastructure.excel.generator;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.mercaso.data.client.dto.FinaleAvailableStockDto;
import com.mercaso.ims.application.dto.ItemSerachDto;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.domain.bulkexportrecords.enums.ExportType;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.enums.VendorItemType;
import com.mercaso.ims.domain.vendoritem.service.VendorItemService;
import com.mercaso.ims.infrastructure.excel.data.ItemReportData;
import com.mercaso.ims.infrastructure.excel.data.SupplierReportData;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;
import java.util.function.Function;

import static com.mercaso.ims.application.service.impl.CategoryApplicationServiceImpl.DEPTH_TO_CLASS;
import static com.mercaso.ims.application.service.impl.CategoryApplicationServiceImpl.DEPTH_TO_GRANDPARENT;
import static com.mercaso.ims.application.service.impl.CategoryApplicationServiceImpl.DEPTH_TO_GREAT_GRANDPARENT;
import static com.mercaso.ims.application.service.impl.CategoryApplicationServiceImpl.DEPTH_TO_PARENT;

@Component
@RequiredArgsConstructor
@Slf4j
public class BulkExportExcelGenerator {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("MM/dd/yyyy")
            .withZone(ZoneId.of("America/Los_Angeles"));

    private final DocumentApplicationService documentApplicationService;

    private final VendorItemService vendorItemService;

    private final Executor taskExecutor;

    public byte[] generateBulkExportReport(List<ItemSerachDto> items,
                                           List<FinaleAvailableStockDto> finaleData,
                                           Map<UUID, Vendor> vendorMap,
                                           ExportType exportType) {

        log.info("[generateBulkExportReport] Generating Excel report for {} items with exportType: {}", items.size(), exportType);

        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            ExcelWriter excelWriter = EasyExcelFactory.write(outputStream).build();

            if (exportType == ExportType.ITEM || exportType == ExportType.ALL) {
                List<ItemReportData> itemData = mapItemsToItemReportData(items, finaleData);
                WriteSheet itemSheet = EasyExcelFactory.writerSheet(0, "Items").head(ItemReportData.class).build();
                excelWriter.write(itemData, itemSheet);
                log.info("Generated Items sheet with {} records", itemData.size());

            }

            if (exportType == ExportType.SUPPLIER || exportType == ExportType.ALL) {
                List<SupplierReportData> supplierData = mapItemsToSupplierReportData(items, vendorMap);
                int sheetIndex = (exportType == ExportType.ALL) ? 1 : 0;
                WriteSheet supplierSheet = EasyExcelFactory.writerSheet(sheetIndex, "Suppliers").head(SupplierReportData.class).build();
                excelWriter.write(supplierData, supplierSheet);
                log.info("Generated Suppliers sheet with {} records", supplierData.size());
            }

            excelWriter.finish();
            byte[] byteArray = outputStream.toByteArray();
            log.info("Bulk export Excel report generated successfully. Size: {} bytes", byteArray.length);
            return byteArray;

        } catch (IOException e) {
            log.error("[generateBulkExportReport] Error generating bulk export Excel report", e);
            throw new ImsBusinessException("Failed to generate the bulk export report", e);
        }
    }

    private List<ItemReportData> mapItemsToItemReportData(List<ItemSerachDto> items,
                                                          List<FinaleAvailableStockDto> finaleData) {

        return items.parallelStream().map(item -> {
            ItemReportData data = new ItemReportData();

            // Basic Information
            data.setStatus(item.getAvailabilityStatus());
            data.setNotes(item.getNote());
            data.setSku(item.getSkuNumber());
            data.setTitle(item.getTitle());
            data.setNewDescription(item.getNewDescription());
            data.setPackageSize(item.getPackageSize());

            // Brand Information
            data.setBrand(item.getBrandName());

            // Tag Information
            data.setTags(item.getItemTags());

            // Company and location information
            data.setCompanyId(item.getCompanyId() != null ? item.getCompanyId().toString() : null);
            data.setLocationId(item.getLocationId() != null ? item.getLocationId().toString() : null);

            data.setCaseHeight(item.getHeight());
            data.setCaseLength(item.getLength());
            data.setCaseWidth(item.getWidth());

            data.setCaseWeight(item.getCaseWeight());
            data.setEachWeight(item.getEachWeight());
            data.setItemSize(item.getItemSize());
            data.setBottleSize(item.getBottleSize());
            data.setCooler(item.getCooler());

            // Category Information
            data.setDepartment(item.getDepartment());
            data.setCategory(item.getCategory());
            data.setSubCategory(item.getSubCategory());
            data.setClassType(item.getClazz());

            // UPC info
            data.setEachUpc(item.getEachUpc());
            data.setCaseUpc(item.getCaseUpc());
            data.setMissingEachUpcReason(item.getMissingEachUpcReason());
            data.setMissingCaseUpcReason(item.getMissingCaseUpcReason());

            // Sales data
            data.setSales1WOS(item.getWos1Week());
            data.setSales4WOS(item.getWos4Weeks());
            data.setGrade(item.getGrade());


            // Inventory data
            String sku = item.getSkuNumber();
            if (sku != null) {

                FinaleAvailableStockDto finaleStock = finaleData.stream()
                        .filter(stock -> sku.equals(stock.getSku()))
                        .findFirst().orElse(null);

                if (finaleStock != null) {
                    data.setSupplierInventory(finaleStock.getShopifyQoh());
                    data.setMercasoQoH(finaleStock.getMfcQoh());
                    data.setReservedUnits(finaleStock.getReservationsQoh());
                }
            }

            // Primary Supplier info
            data.setDirectPrimary(item.getPrimaryVendorName());
            data.setDirectPrimarySku(item.getPrimaryVendorSkuNumber());
            data.setDirectPrimaryCost(item.getPrimaryVendorCost());
            data.setJitPrimary(item.getBackupVendorName());
            data.setJitPrimarySku(item.getBackupVendorSkuNumber());
            data.setJitPrimaryCost(item.getBackupVendorCost());
            data.setJitPrimaryAvailability(item.getVendorItemAvailability());

            //price info
            data.setPriceGroup(item.getItemPriceGroupName());
            data.setRegPrice(item.getRegPrice());
            data.setIndividualPrice(item.getRegIndividualPrice());
            data.setRegPlusCrvPrice(item.getRegPlusCrvPrice());
            data.setCrvPrice(item.getCrv());
            data.setCrvFlag(item.getCrvFlag());

            //promo price
            data.setPromoFlag(item.getPromoFlag());
            data.setPromotionPrice(item.getPromoPrice());
            data.setPromotionIndividualPrice(item.getPromoPriceIndividual());
            data.setPromotionPricePlusCrv(item.getPromoPricePlusCrv());
            if (item.getPromoBeginTime() != null) {
                data.setPromoBeginAt(DATE_FORMATTER.format(item.getPromoBeginTime()));
            }
            if (item.getPromoEndTime() != null) {
                data.setPromoEndAt(DATE_FORMATTER.format(item.getPromoEndTime()));
            }

            // image info
            data.setPhoto(StringUtils.isBlank(item.getPhotoName()) ? null : documentApplicationService.getImsUrl(item.getPhotoName()));

            return data;
        }).toList();
    }

    private List<SupplierReportData> mapItemsToSupplierReportData(List<ItemSerachDto> items, Map<UUID, Vendor> vendorMap) {
        if (items.isEmpty() || vendorMap.isEmpty()) {
            return Collections.emptyList();
        }

        List<UUID> itemIds = items.stream()
                .map(ItemSerachDto::getId)
                .filter(Objects::nonNull)
                .toList();

        if (itemIds.isEmpty()) {
            return Collections.emptyList();
        }

        Map<UUID, ItemSerachDto> itemMap = items.stream()
                .filter(item -> item.getId() != null)
                .collect(Collectors.toMap(ItemSerachDto::getId, Function.identity(), (existing, replacement) -> existing));

        final int BATCH_SIZE = 1000;
        List<CompletableFuture<List<SupplierReportData>>> futures = new ArrayList<>();

        for (int i = 0; i < itemIds.size(); i += BATCH_SIZE) {
            int startIndex = i;
            int endIndex = Math.min(i + BATCH_SIZE, itemIds.size());
            List<UUID> batchItemIds = itemIds.subList(startIndex, endIndex);

            CompletableFuture<List<SupplierReportData>> future = CompletableFuture
                    .supplyAsync(() -> {
                        log.debug("Processing vendor items for batch {}-{} of {} total items",
                                startIndex + 1, endIndex, itemIds.size());

                        List<VendorItem> batchVendorItems = vendorItemService.findByItemIds(batchItemIds);

                        List<SupplierReportData> batchSupplierData = batchVendorItems.stream()
                                .map(vendorItem -> createSupplierReportData(vendorItem, itemMap, vendorMap))
                                .filter(Objects::nonNull)
                                .toList();

                        log.debug("Processed batch {}-{}, generated {} supplier data records",
                                startIndex + 1, endIndex, batchSupplierData.size());

                        return batchSupplierData;
                    }, taskExecutor)
                    .exceptionally(throwable -> {
                        log.error("Error processing vendor items for batch {}-{}", startIndex + 1, endIndex, throwable);
                        return Collections.emptyList();
                    });

            futures.add(future);
        }

        List<SupplierReportData> allSupplierData = futures.stream()
                .map(CompletableFuture::join)
                .flatMap(List::stream)
                .toList();

        log.info("Completed concurrent vendor item processing. Total supplier data records: {}", allSupplierData.size());

        return allSupplierData;
    }

    private SupplierReportData createSupplierReportData(VendorItem vendorItem, Map<UUID, ItemSerachDto> itemMap, Map<UUID, Vendor> vendorMap) {
        ItemSerachDto item = itemMap.get(vendorItem.getItemId());
        if (item == null) {
            return null;
        }

        Vendor vendor = vendorMap.get(vendorItem.getVendorId());
        if (vendor == null) {
            return null;
        }

        SupplierReportData supplierData = new SupplierReportData();
        supplierData.setSku(item.getSkuNumber());
        supplierData.setSupplierName(vendor.getVendorName());
        supplierData.setSupplierSku(vendorItem.getVendorSkuNumber());
        supplierData.setAisle(vendorItem.getAisle());
        supplierData.setSupplierType(vendorItem.getVendorItemType());
        supplierData.setDirectCost(vendorItem.getPackPlusCrvCost());
        supplierData.setJitCost(vendorItem.getBackupPackPlusCrvCost());
        supplierData.setIsPrimary(determineIsPrimary(vendorItem, item));

        if (vendorItem.getCostFreshnessTime() != null) {
            supplierData.setDirectCostFreshnessTime(DATE_FORMATTER.format(vendorItem.getCostFreshnessTime()));
        }

        if (vendorItem.getBackupCostFreshnessTime() != null) {
            supplierData.setJitCostFreshnessTime(DATE_FORMATTER.format(vendorItem.getBackupCostFreshnessTime()));
        }

        supplierData.setAvailability(vendorItem.getAvailability());
        return supplierData;
    }

    private String determineIsPrimary(VendorItem vendorItem, ItemSerachDto item) {
        final UUID vendorId = vendorItem.getVendorId();
        if (Objects.equals(vendorId, item.getPrimaryVendorId()) && Objects.equals(vendorId, item.getBackupVendorId())) {
            return "Direct Primary & JIT Primary";
        }
        if (Objects.equals(vendorId, item.getPrimaryVendorId())) {
            return "Direct Primary";
        }
        if (Objects.equals(vendorId, item.getBackupVendorId())) {
            return "JIT Primary";
        }
        return "";
    }
}