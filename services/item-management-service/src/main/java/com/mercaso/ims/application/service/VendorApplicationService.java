package com.mercaso.ims.application.service;

import com.mercaso.ims.application.command.CreateVendorCommand;
import com.mercaso.ims.application.command.UpdateVendorCommand;
import com.mercaso.ims.application.dto.VendorDto;
import java.util.UUID;

public interface VendorApplicationService {

    VendorDto create(CreateVendorCommand command);

    VendorDto update(UpdateVendorCommand command);

    VendorDto delete(UUID id);

    void migrateFinaleVendor();


}
