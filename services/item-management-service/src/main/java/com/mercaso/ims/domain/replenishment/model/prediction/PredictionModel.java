package com.mercaso.ims.domain.replenishment.model.prediction;

/**
 * 预测模型通用接口
 * 
 * @param <I> 输入类型
 * @param <O> 输出类型
 */
public interface PredictionModel<I, O> {
    
    /**
     * 执行预测
     * 
     * @param input 输入数据
     * @return 预测结果
     */
    O predict(I input);
    
    /**
     * 获取模型置信度
     * 
     * @return 置信度 [0.0, 1.0]
     */
    double getConfidence();
    
    /**
     * 获取模型元数据
     * 
     * @return 模型元数据
     */
    ModelMetadata getMetadata();
    
    /**
     * 更新模型
     * 
     * @param trainingData 训练数据
     */
    void updateModel(TrainingData trainingData);
}
