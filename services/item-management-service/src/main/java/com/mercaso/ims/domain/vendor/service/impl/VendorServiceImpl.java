package com.mercaso.ims.domain.vendor.service.impl;

import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.VendorRepository;
import com.mercaso.ims.domain.vendor.service.VendorService;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class VendorServiceImpl implements VendorService {

    private final VendorRepository vendorRepository;

    @Override
    public Vendor findById(UUID id) {
        if (null == id) {
            return null;
        }
        return vendorRepository.findById(id);
    }

    @Override
    public List<Vendor> findByIds(List<UUID> ids) {
        return vendorRepository.findAllByIdIn(ids);
    }

    @Override
    public List<Vendor> findAll() {
        return vendorRepository.findAll();
    }

    @Override
    public Vendor findByVendorName(String vendorName) {
        return vendorRepository.findByVendorName(vendorName);
    }

    @Override
    public Vendor findByFinaleId(String finaleId) {
        return vendorRepository.findByFinaleId(finaleId);
    }

    @Override
    public Vendor save(Vendor vendor) {
        return vendorRepository.save(vendor);
    }

    @Override
    public Vendor update(Vendor vendor) {
        return vendorRepository.update(vendor);
    }

    @Override
    public Vendor delete(UUID id) {
        return vendorRepository.deleteById(id);
    }

    @Override
    public List<Vendor> findByFuzzyName(String vendorName) {
        return vendorRepository.findByFuzzyName(vendorName);
    }

    @Override
    public List<Vendor> findAllExternalPickingVendor(Boolean externalPicking) {
        return vendorRepository.findByExternalPicking(externalPicking);
    }
}
