package com.mercaso.ims.infrastructure.repository.vendoritem.jpa.dataobject;

import com.mercaso.ims.domain.vendoritem.enums.VendorItemStatus;
import com.mercaso.ims.infrastructure.repository.BaseDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

@Entity
@Table(name = "vendor_item")
@Data
@AllArgsConstructor
@NoArgsConstructor
@SQLDelete(sql = "update vendor_item set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
public class VendorItemDo extends BaseDo {

    @Column(name = "vendor_id")
    private UUID vendorId;

    @Column(name = "item_id")
    private UUID itemId;

    @Column(name = "vendor_sku_number")
    private String vendorSkuNumber;

    @Column(name = "vendor_item_name")
    private String vendorItemName;

    @Column(name = "note")
    private String note;

    @Column(name = "status_change_reason")
    private String statusChangeReason;

    @Column(name = "aisle")
    private String aisle;

    @Column(name = "lowest_cost")
    private BigDecimal lowestCost;

    @Column(name = "highest_cost")
    private BigDecimal highestCost;

    @Column(name = "pack_plus_crv_cost")
    private BigDecimal packPlusCrvCost;

    @Column(name = "pack_no_crv_cost")
    private BigDecimal packNoCrvCost;

    @Column(name = "individual_cost")
    private BigDecimal individualCost;

    @Column(name = "secondary_pack_plus_crv_cost")
    private BigDecimal secondaryPackPlusCrvCost;

    @Column(name = "secondary_pack_no_crv_cost")
    private BigDecimal secondaryPackNoCrvCost;

    @Column(name = "secondary_individual_cost")
    private BigDecimal secondaryIndividualCost;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private VendorItemStatus vendorItemStatus;

    @Column(name = "cost_freshness_time")
    private Instant costFreshnessTime;
    
    @Column(name = "availability")
    private Boolean availability;

    @Column(name = "backup_pack_plus_crv_cost")
    private BigDecimal backupPackPlusCrvCost;

    @Column(name = "backup_cost_freshness_time")
    private Instant backupCostFreshnessTime;

    @Column(name = "vendor_item_type")
    private String vendorItemType;

    @Column(name = "lead_time_days")
    private Integer leadTimeDays;

    @Column(name = "moq")
    private Integer moq;

}
