package com.mercaso.ims.domain.vendor.service;

import com.mercaso.ims.domain.vendor.Vendor;
import java.util.List;
import java.util.UUID;

public interface VendorService {

    Vendor findById(UUID id);

    List<Vendor> findByIds(List<UUID> ids);

    List<Vendor> findAll();

    Vendor findByVendorName(String vendorName);

    Vendor findByFinaleId(String finaleId);

    Vendor save(Vendor vendor);

    Vendor update(Vendor vendor);

    Vendor delete(UUID id);

    List<Vendor> findByFuzzyName(String vendorName);

    List<Vendor> findAllExternalPickingVendor(Boolean externalPicking);

}
