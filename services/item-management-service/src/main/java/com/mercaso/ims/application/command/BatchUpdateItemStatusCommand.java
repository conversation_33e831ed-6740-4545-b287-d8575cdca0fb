package com.mercaso.ims.application.command;

import com.mercaso.ims.application.BaseCommand;
import com.mercaso.ims.domain.item.enums.AvailabilityStatus;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
public class BatchUpdateItemStatusCommand extends BaseCommand {

    private List<UUID> itemIds;

    private AvailabilityStatus status;

}