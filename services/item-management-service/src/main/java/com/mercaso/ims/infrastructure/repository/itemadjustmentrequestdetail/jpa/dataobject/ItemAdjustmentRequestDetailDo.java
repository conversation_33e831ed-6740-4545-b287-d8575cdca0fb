package com.mercaso.ims.infrastructure.repository.itemadjustmentrequestdetail.jpa.dataobject;

import com.alibaba.excel.annotation.ExcelProperty;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentStatus;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentType;
import com.mercaso.ims.infrastructure.repository.BaseDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

@Entity
@Table(name = "item_adjustment_request_detail")
@Data
@AllArgsConstructor
@NoArgsConstructor
@SQLDelete(sql = "update item_adjustment_request_detail set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
@EqualsAndHashCode(callSuper = true)
public class ItemAdjustmentRequestDetailDo extends BaseDo {

    @Column(name = "request_id")
    private UUID requestId;
    @Column(name = "type")
    @Enumerated(EnumType.STRING)
    private ItemAdjustmentType type;
    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private ItemAdjustmentStatus status;
    @Column(name = "sku")
    private String sku;
    @Column(name = "item_status")
    private String itemStatus;
    @Column(name = "primary_vendor_item_aisle")
    private String primaryVendorItemAisle;
    @Column(name = "primary_po_vendor")
    private String primaryPoVendor;
    @Column(name = "primary_jit_vendor")
    private String primaryJitVendor;
    @Column(name = "title")
    private String title;
    @Column(name = "package_size")
    private Integer packageSize;
    @Column(name = "item_size")
    private String itemSize;
    @Column(name = "department")
    private String department;
    @Column(name = "category")
    private String category;
    @Column(name = "sub_category")
    private String subCategory;
    @Column(name = "class_type")
    private String classType;
    @Column(name = "brand")
    private String brand;
    @Column(name = "reg_price_pack_no_crv")
    private BigDecimal regPricePackNoCrv;
    @Column(name = "po_vendor_item_cost")
    private BigDecimal poVendorItemCost;
    @Column(name = "jit_vendor_item_cost")
    private BigDecimal jitVendorItemCost;
    @Column(name = "primary_po_vendor_item_cost")
    private BigDecimal primaryPoVendorItemCost;
    @Column(name = "primary_jit_vendor_item_cost")
    private BigDecimal primaryJitVendorItemCost;
    @Column(name = "upc")
    private String upc;
    @Column(name = "case_upc")
    private String caseUpc;
    @Column(name = "each_upc")
    private String eachUpc;
    @Column(name = "vendor_item_number")
    private String vendorItemNumber;
    @Column(name = "disposition")
    private String disposition;
    @Column(name = "inventory")
    private Integer inventory;
    @Column(name = "failure_reason")
    private String failureReason;

    @ExcelProperty("vendor")
    private String vendor;
    @ExcelProperty("vendor_aisle")
    private String vendorAisle;
    @ExcelProperty("attribute_name")
    private String attributeName;
    @ExcelProperty("attribute_value")
    private String attributeValue;
    @ExcelProperty("company_id")
    private String companyId;
    @ExcelProperty("location_id")
    private String locationId;

    @ExcelProperty("new_description")
    private String newDescription;
    @ExcelProperty("item_unit_measure")
    private String itemUnitMeasure;
    @Column(name = "promo_price_pack_no_crv")
    private BigDecimal promoPricePackNoCrv;
    @Column(name = "crv_flag")
    private Boolean crvFlag;
    @Column(name = "promo_flag")
    private Boolean promoFlag;
    @ExcelProperty("image_url")
    private String imageUrl;
    @ExcelProperty("tags")
    private String tags;

    @Column(name = "item_length")
    private Double length;
    @Column(name = "item_height")
    private Double height;
    @Column(name = "item_width")
    private Double width;
    @Column(name = "item_case_weight")
    private Double caseWeight;
    @Column(name = "item_case_weight_unit")
    private String caseWeightUnit;
    @Column(name = "item_each_weight")
    private Double eachWeight;
    @Column(name = "item_each_weight_unit")
    private String eachWeightUnit;
    @Column(name = "vendor_item_availability")
    private Boolean vendorItemAvailability;
    @Column(name = "vendor_item_type")
    private String vendorItemType;
    @Column(name = "cooler")
    private Boolean cooler;

}