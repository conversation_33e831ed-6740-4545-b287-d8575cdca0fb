package com.mercaso.user.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.user.adaptor.Auth0ManagementApiAdaptor;
import com.mercaso.user.dto.user.RoleDto;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class RoleServiceImplTest {

    @Mock
    private Auth0ManagementApiAdaptor auth0ManagementApiAdaptor;

    @InjectMocks
    private RoleServiceImpl roleService;

    @Test
    void listRoles_ShouldReturnOnlyUserGroupRoles() {
        // Arrange
        RoleDto userGroupRole = RoleDto.builder()
            .id("role_id_1")
            .name("user_group_admin")
            .description("Admin user group")
            .build();

        RoleDto normalRole = RoleDto.builder()
            .id("role_id_2")
            .name("normal_role")
            .description("Normal role")
            .build();

        when(auth0ManagementApiAdaptor.listRoles())
            .thenReturn(List.of(userGroupRole, normalRole));

        // Act
        List<RoleDto> result = roleService.listRoles();

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(auth0ManagementApiAdaptor).listRoles();
    }
} 