package com.mercaso.user.controller.v1;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.auth0.client.mgmt.ManagementAPI;
import com.auth0.client.mgmt.RolesEntity;
import com.auth0.exception.Auth0Exception;
import com.auth0.json.mgmt.roles.Role;
import com.auth0.json.mgmt.roles.RolesPage;
import com.auth0.net.Request;
import com.auth0.net.Response;
import com.mercaso.user.AbstractIT;
import com.mercaso.user.dto.user.RoleDto;
import com.mercaso.user.utils.controller_utils.RoleRestApi;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

class RolesControllerIT extends AbstractIT {

    @Autowired
    private RoleRestApi roleRestApi;

    @MockBean(name = "userManagementApi")
    private ManagementAPI userManagementApi;

    @MockBean
    private RolesEntity rolesEntity;

    @Test
    @DisplayName("Should return only user group roles - Happy Path")
    void listRoles_ShouldReturnOnlyUserGroupRoles() throws Auth0Exception {
        // Mock Auth0 API responses
        when(userManagementApi.roles()).thenReturn(rolesEntity);

        Role userGroupRole = new Role();
        userGroupRole.setName("user_group_admin");
        userGroupRole.setDescription("Admin user group");

        Role normalRole = new Role();
        normalRole.setName("normal_role");
        normalRole.setDescription("Normal role");

        RolesPage rolesPage = new RolesPage(0, 2, 2, 2, Arrays.asList(userGroupRole, normalRole));

        Request<RolesPage> request = mock(Request.class);
        when(rolesEntity.list(any())).thenReturn(request);

        Response<RolesPage> response = mock(Response.class);
        when(request.execute()).thenReturn(response);
        when(response.getStatusCode()).thenReturn(HttpStatus.OK.value());
        when(response.getBody()).thenReturn(rolesPage);

        ResponseEntity<List<RoleDto>> result = roleRestApi.listRoles();

        // Assert
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());

        List<RoleDto> roles = result.getBody();
        assertNotNull(roles);
        assertEquals(1, roles.size());
        assertTrue(roles.stream()
            .allMatch(role -> role.getName().startsWith("user_group_")));
    }
} 