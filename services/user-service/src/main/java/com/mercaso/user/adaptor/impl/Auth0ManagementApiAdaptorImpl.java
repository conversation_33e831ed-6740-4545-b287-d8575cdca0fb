package com.mercaso.user.adaptor.impl;

import com.auth0.client.mgmt.ManagementAPI;
import com.auth0.client.mgmt.filter.ClientFilter;
import com.auth0.client.mgmt.filter.PageFilter;
import com.auth0.client.mgmt.filter.RolesFilter;
import com.auth0.client.mgmt.filter.UserFilter;
import com.auth0.exception.APIException;
import com.auth0.exception.Auth0Exception;
import com.auth0.exception.RateLimitException;
import com.auth0.json.mgmt.Page;
import com.auth0.json.mgmt.users.User;
import com.auth0.net.Response;
import com.mercaso.user.adaptor.Auth0ManagementApiAdaptor;
import com.mercaso.user.config.auth0.Auth0M2MProperties;
import com.mercaso.user.config.auth0.RoleMappingProperties;
import com.mercaso.user.dto.Auth0Client;
import com.mercaso.user.dto.user.RoleDto;
import com.mercaso.user.dto.user.UpdateUserRequest;
import com.mercaso.user.dto.user.UserDto;
import com.mercaso.user.exception.Auth0ClientException;
import com.mercaso.user.exception.UsersException;
import com.mercaso.user.mapper.Auth0RoleMapper;
import com.mercaso.user.mapper.Auth0UserMapper;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class Auth0ManagementApiAdaptorImpl implements Auth0ManagementApiAdaptor {

    private static final String USER_GROUP_PREFIX = "user_group_";
    private static final int PAGE_SIZE = 100;
    private static final String CONNECTION_PARTNER_INSIGHT = "partner-insight";

    private final ManagementAPI managementAPI;
    private final ManagementAPI m2mManagementAPI;
    private final Auth0UserMapper auth0UserMapper;
    private final Auth0RoleMapper auth0RoleMapper;
    private final Auth0M2MProperties auth0M2MProperties;
    private final RoleMappingProperties roleMappingProperties;

    public Auth0ManagementApiAdaptorImpl(
        @Qualifier("userManagementApi") ManagementAPI managementAPI,
        @Qualifier("m2mManagementApi") ManagementAPI m2mManagementAPI,
        Auth0UserMapper auth0UserMapper,
        Auth0RoleMapper auth0RoleMapper,
        Auth0M2MProperties auth0M2MProperties, RoleMappingProperties roleMappingProperties) {
        this.managementAPI = managementAPI;
        this.m2mManagementAPI = m2mManagementAPI;
        this.auth0UserMapper = auth0UserMapper;
        this.auth0RoleMapper = auth0RoleMapper;
        this.auth0M2MProperties = auth0M2MProperties;
        this.roleMappingProperties = roleMappingProperties;
    }

    @Override
    public UserDto getUser(UUID userId) {
        String query = String.format("app_metadata.userInfo.userId:\"%s\"", userId);
        List<UserDto> users = listUsers(query);
        return CollectionUtils.isEmpty(users) ? null : users.getFirst();
    }

    @Override
    public List<UserDto> listUsers(String query) {
        return fetchPaginatedItems(
            (page, pageSize) -> {
                UserFilter filter = new UserFilter()
                    .withTotals(true)
                    .withPage(page, pageSize)
                    .withSearchEngine("v3");

                if (StringUtils.isNoneBlank(query)) {
                    filter.withQuery(query);
                }
                log.info("Invoking mgmt API- list users, query: {}", query);
                return managementAPI.users().list(filter).execute();
            },
            auth0UserMapper::from,
            "users.list",
            40, // limit 40 per second
            3
        ).stream().filter(u -> {
            if (BooleanUtils.isTrue(u.getBlocked())) {
                log.info("User is blocked, email is: {}", u.getEmail());
                return false;
            }

            if (u.getIdentities().stream().allMatch(i -> CONNECTION_PARTNER_INSIGHT.equalsIgnoreCase(i.getConnection()))) {
                log.info("User is a partner insight user, email is: {}", u.getEmail());
                return false;
            }
            return true;
        }).toList();
    }

    @Override
    public List<RoleDto> listUserRoles(String auth0UserId) {
        return fetchPaginatedItems(
            (page, pageSize) -> {
                log.info("Invoking mgmt API- list roles, auth0UserId: {}", auth0UserId);
                return managementAPI.users()
                    .listRoles(auth0UserId, new PageFilter()
                        .withTotals(true)
                        .withPage(page, pageSize))
                    .execute();
            },
            auth0RoleMapper::from,
            "users.listRoles",
            10, // limit 10 per second
            3
        ).stream().filter(r -> r.getName().startsWith(USER_GROUP_PREFIX)).toList();
    }

    @Override
    public List<String> getAuth0UserIdsByRole(String roleId) {
        return fetchUsersForRole(roleId).stream()
            .map(User::getId)
            .toList();
    }

    @Override
    public Map<String, List<RoleDto>> batchListUserRoles(List<String> auth0UserIds) {
        log.debug("Batch listing roles for {} users", auth0UserIds.size());

        if (CollectionUtils.isEmpty(auth0UserIds)) {
            return Collections.emptyMap();
        }

        List<RoleDto> allRoles = listRoles();
        Map<String, List<RoleDto>> userRolesMap = fetchUserRolesWithOptimalStrategy(
            auth0UserIds,
            allRoles
        );

        ensureAllUsersHaveRoles(userRolesMap, auth0UserIds);

        log.debug("Successfully fetched roles for {} users", userRolesMap.size());
        return userRolesMap;
    }

    private Map<String, List<RoleDto>> fetchUserRolesWithOptimalStrategy(
        List<String> auth0UserIds,
        List<RoleDto> allRoles) {

        int userCount = auth0UserIds.size();
        int roleCount = allRoles.size();

        log.debug("Selecting strategy - Users: {}, Roles: {}", userCount, roleCount);

        if (userCount < roleCount) {
            log.debug("Using user-based strategy");
            return buildUserRolesMapByListUserRoles(auth0UserIds);
        }

        log.debug("Using role-based strategy");
        return buildUserRolesMapByFetchUsersForRole(allRoles, auth0UserIds);
    }

    private void ensureAllUsersHaveRoles(
        Map<String, List<RoleDto>> userRolesMap,
        List<String> auth0UserIds
    ) {
        auth0UserIds.forEach(userId ->
            userRolesMap.computeIfAbsent(userId, k -> Collections.emptyList())
        );
    }

    private Map<String, List<RoleDto>> buildUserRolesMapByListUserRoles(List<String> auth0UserIds) {
        log.debug("Building user roles map for {} users", auth0UserIds.size());

        Map<String, List<RoleDto>> result = auth0UserIds.parallelStream()
            .collect(Collectors.toMap(
                userId -> userId,
                userId -> {
                    log.debug("Fetching roles for user: {}", userId);
                    List<RoleDto> roles = listUserRoles(userId);
                    return CollectionUtils.isEmpty(roles) ? Collections.emptyList() : roles;
                },
                (existing, replacement) -> existing,
                HashMap::new
            ));

        log.debug("Completed building user roles map with {} entries", result.size());
        return result;
    }

    @Override
    public List<RoleDto> listRoles() {
        return fetchPaginatedItems(
            (page, pageSize) -> {
                log.info("Invoking mgmt API- list roles.");
                return managementAPI.roles()
                    .list(new RolesFilter()
                        .withTotals(true)
                        .withPage(page, pageSize))
                    .execute();
            },
            auth0RoleMapper::from,
            "roles.list",
            10, // limit 10 per second
            3
        ).stream().filter(r -> r.getName().startsWith(USER_GROUP_PREFIX)).toList();
    }

    @Override
    public void assignRolesToUser(String auth0UserId, List<String> roleIds) {
        try {
            log.info("Invoking mgmt API- assign roles to a user, auth0UserId: {}, roleIds: {}", auth0UserId, roleIds);
            Response<Void> response = managementAPI.users()
                .addRoles(auth0UserId, buildRoleIds(roleIds))
                .execute();

            validateResponseIs204(response);
        } catch (Auth0Exception e) {
            handleAuth0Exception(e);
        }
    }

    @Override
    public List<Auth0Client> getAllM2MClients() {
        log.info("Fetching all Auth0 M2M clients");
        return fetchPaginatedItems(
            (page, pageSize) -> m2mManagementAPI.clients()
                .list(new ClientFilter()
                    .withTotals(true)
                    .withPage(page, pageSize))
                .execute(),
            client -> {
                Map<String, String> clients = auth0M2MProperties.getClients();
                boolean m2mClients = clients.containsKey(client.getClientId());
                if (m2mClients) {
                    return Auth0Client.builder()
                        .clientId(client.getClientId())
                        .clientSecret(client.getClientSecret())
                        .domain(auth0M2MProperties.getCustomDomain())
                        .audience(auth0M2MProperties.getAudience())
                        .name(client.getName())
                        .description(client.getDescription())
                        .build();
                }
                return null;
            },
            "clients.list",
            20, // limit 20 per second
            3
        ).stream()
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    private List<User> fetchUsersForRole(String roleId) {
        return fetchPaginatedItems(
            (page, pageSize) -> {
                log.info("Invoking mgmt API- get a role's users, roleId: {}", roleId);
                return managementAPI.roles()
                    .listUsers(roleId, new PageFilter()
                        .withTotals(true)
                        .withPage(page, pageSize))
                    .execute();
            },
            user -> user,
            "roles.listUsers",
            10, // limit 10 per second
            3
        );
    }

    private Map<String, List<RoleDto>> buildUserRolesMapByFetchUsersForRole(List<RoleDto> allRoles, List<String> auth0UserIds) {
        log.debug("Building user roles map for {} roles", allRoles.size());

        Set<String> userIdSet = new HashSet<>(auth0UserIds);
        Map<String, List<RoleDto>> userRolesMap = new HashMap<>();

        for (RoleDto role : allRoles) {
            List<User> roleUsers = fetchUsersForRole(role.getId());
            roleUsers.stream()
                .map(User::getId)
                .filter(userIdSet::contains)
                .forEach(userId ->
                    userRolesMap.computeIfAbsent(userId, k -> new ArrayList<>())
                        .add(role)
                );
        }

        return userRolesMap;
    }

    private <T extends Page<S>, S, R> List<R> fetchPaginatedItems(
        PaginatedRequest<T, S> request,
        Function<S, R> mapper,
        String apiKey,
        int ratePerSecond,
        int maxRetries) {
        try {
            List<R> items = new ArrayList<>();
            int pageIndex = 0;
            boolean hasMorePages = true;

            while (hasMorePages) {
                final int currentPage = pageIndex;
                Response<T> response = executeWithRateLimit(
                    apiKey,
                    ratePerSecond,
                    maxRetries,
                    () -> request.execute(currentPage, PAGE_SIZE)
                );

                validateResponseIs200(response);

                List<S> currentItems = request.getItems(response);
                if (pageIndex == 0 && CollectionUtils.isEmpty(currentItems)) {
                    return items;
                }

                if (CollectionUtils.isNotEmpty(currentItems)) {
                    items.addAll(currentItems.stream()
                        .filter(Objects::nonNull)
                        .map(mapper)
                        .toList());
                }

                int total = request.getTotal(response);
                hasMorePages = total > 0
                    && (pageIndex + 1) * PAGE_SIZE < total
                    && CollectionUtils.isNotEmpty(currentItems);
                pageIndex++;
            }

            return items;
        } catch (Auth0Exception e) {
            handleAuth0Exception(e);
        }
        return List.of();
    }

    @FunctionalInterface
    private interface PaginatedRequest<T extends Page<S>, S> {

        Response<T> execute(int page, int pageSize) throws Auth0Exception;

        default List<S> getItems(Response<T> response) {
            T page = response.getBody();
            return page != null ? page.getItems() : new ArrayList<>();
        }

        default int getTotal(Response<T> response) {
            T page = response.getBody();
            return page != null ? page.getTotal() : 0;
        }
    }

    @Override
    public UserDto updateUser(String auth0UserId, UpdateUserRequest request) {
        try {
            User updateUser = new User();
            if (StringUtils.isNoneBlank(request.getName())) {
                updateUser.setName(request.getName());
            }

            if (StringUtils.isNoneBlank(request.getPassword())) {
                updateUser.setPassword(request.getPassword().toCharArray());

            }

            if (request.getBlocked() != null) {
                updateUser.setBlocked(request.getBlocked());
            }

            log.info("Invoking mgmt API- update a user, auth0UserId: {}, request: {}", auth0UserId, request);
            Response<User> response = managementAPI.users()
                .update(auth0UserId, updateUser)
                .execute();

            validateResponseIs200(response);
            return auth0UserMapper.from(response.getBody());
        } catch (Auth0Exception e) {
            handleAuth0Exception(e);
        }
        return null;
    }

    @Override
    public UserDto createUser(String email, String password, String name, String connection) {
        try {
            User createUser = new User();
            createUser.setEmail(email);
            createUser.setPassword(password.toCharArray());
            createUser.setName(name);
            createUser.setUsername(email);
            createUser.setConnection(connection);
            createUser.setVerifyEmail(false);

            log.info("Creating Auth0 user with email: {}", email);
            Response<User> response = managementAPI.users()
                .create(createUser)
                .execute();

            validateResponseIs201(response);
            return auth0UserMapper.from(response.getBody());

        } catch (Auth0Exception e) {
            handleAuth0Exception(e);
        }
        return null;
    }

    private void handleAuth0Exception(Auth0Exception e) {
        if (e instanceof APIException apiException) {

            if (e instanceof RateLimitException rateLimitException) {
                throw new Auth0ClientException(String.valueOf(rateLimitException.getStatusCode()),
                    "Failed to request auth0 api client with RateLimitException.",
                    rateLimitException);
            }

            if (apiException.getStatusCode() == HttpStatus.CONFLICT.value()) {
                throw new UsersException(String.valueOf(HttpStatus.CONFLICT.value()),
                    "The user already exists..",
                    apiException);
            }

            throw new Auth0ClientException(String.valueOf(apiException.getStatusCode()),
                "Failed to request auth0 api client with APIException.",
                apiException);
        }

        throw new Auth0ClientException(String.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value()),
            "Failed to request auth0 api client with Auth0Exception",
            e);
    }

    private void validateResponseIs200(Response<?> response) {
        if (response.getStatusCode() != HttpStatus.OK.value()) {
            throw new Auth0ClientException(
                "Auth0 Client Error. Status code: " + response.getStatusCode()
            );
        }
    }

    private void validateResponseIs204(Response<?> response) {
        if (response.getStatusCode() != HttpStatus.NO_CONTENT.value()) {
            throw new Auth0ClientException(
                "Auth0 Client Error. Status code: " + response.getStatusCode()
            );
        }
    }

    private void validateResponseIs201(Response<?> response) {
        if (response.getStatusCode() != HttpStatus.CREATED.value()) {
            throw new Auth0ClientException(
                "Auth0 Client Error. Status code: " + response.getStatusCode()
            );
        }
    }

    private List<String> buildRoleIds(List<String> roleIds) {
        Map<String, List<String>> userGroupToRole = roleMappingProperties.getUserGroupToRole();

        List<String> finalAssignRoleIds = new ArrayList<>();
        roleIds.forEach(roleId -> {
            if (userGroupToRole.containsKey(roleId)) {
                finalAssignRoleIds.addAll(userGroupToRole.get(roleId));
            }
        });

        finalAssignRoleIds.addAll(roleIds);

        return finalAssignRoleIds;
    }

    private <T> Response<T> executeWithRateLimit(String apiKey, int ratePerSecond, int maxRetries,
        ThrowingSupplier<Response<T>> supplier) throws Auth0Exception {
        if (ratePerSecond <= 0) {
            // No rate limiting if rate <= 0
            return supplier.get();
        }

        synchronized (lock) {
            long currentTime = System.currentTimeMillis();
            long minIntervalMs = 1000 / ratePerSecond;

            Long lastRequestTime = lastRequestTimeMap.get(apiKey);
            if (lastRequestTime != null) {
                long elapsedTime = currentTime - lastRequestTime;
                if (elapsedTime < minIntervalMs) {
                    try {
                        long sleepTime = minIntervalMs - elapsedTime;
                        Thread.sleep(sleepTime);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                }
            }

            lastRequestTimeMap.put(apiKey, System.currentTimeMillis());
        }

        // Execute with retry for rate limit exceptions
        return executeWithRetry(supplier, maxRetries);
    }

    private <T> Response<T> executeWithRetry(ThrowingSupplier<Response<T>> supplier, int maxRetries) throws Auth0Exception {
        int retries = 0;
        long retryDelayMs = 1000; // Start with 1 second

        while (true) {
            try {
                return supplier.get();
            } catch (RateLimitException e) {
                if (retries >= maxRetries) {
                    log.error("Maximum retry attempts ({}) exceeded for rate-limited API call", maxRetries);
                    throw e;
                }

                retries++;
                log.warn("Rate limit exceeded, retrying in {} ms (attempt {}/{})",
                    retryDelayMs, retries, maxRetries);

                try {
                    Thread.sleep(retryDelayMs);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                }

                // Exponential backoff
                retryDelayMs *= 2;
            }
        }
    }

    @FunctionalInterface
    private interface ThrowingSupplier<T> {

        T get() throws Auth0Exception;
    }

    private final Map<String, Long> lastRequestTimeMap = new HashMap<>();
    private final Object lock = new Object();
}
