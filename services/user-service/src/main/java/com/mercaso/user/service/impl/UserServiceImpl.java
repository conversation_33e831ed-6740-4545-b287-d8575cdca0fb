package com.mercaso.user.service.impl;

import static com.mercaso.user.exception.ErrorCode.USER_NOT_FOUND;
import static com.mercaso.user.exception.ErrorCode.USER_UNABLE_MODIFICATION;

import com.mercaso.user.adaptor.Auth0ManagementApiAdaptor;
import com.mercaso.user.dto.SearchUserFilter;
import com.mercaso.user.dto.user.CreateUserRequest;
import com.mercaso.user.dto.user.RoleDto;
import com.mercaso.user.dto.user.UpdateUserRequest;
import com.mercaso.user.dto.user.UserDto;
import com.mercaso.user.exception.UsersException;
import com.mercaso.user.service.UserService;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final Auth0ManagementApiAdaptor auth0ManagementApiAdaptor;

    private static final String CONNECTION_MOBILE_CONNECTION = "Mobile-connection";
    private static final String PROVIDER_AUTH0 = "auth0";


    @Override
    public UserDto getUser(UUID userId) {
        UserDto user = auth0ManagementApiAdaptor.getUser(userId);
        if (user == null) {
            return null;
        }
        List<RoleDto> roles = auth0ManagementApiAdaptor.listUserRoles(user.getAuth0UserId());
        user.setRoles(roles);
        return user;
    }

    @Override
    public List<UserDto> listOrSearchUsers(SearchUserFilter filter) {
        log.info("Listing users with filter: {}", filter);

        String query = buildSearchQuery(filter);
        List<UserDto> users = auth0ManagementApiAdaptor.listUsers(query);

        if (users.isEmpty()) {
            return users;
        }

        enrichUsersWithRoles(users);
        return users;
    }

    private String buildSearchQuery(SearchUserFilter filter) {
        if (filter == null) {
            return null;
        }

        StringBuilder queryBuilder = new StringBuilder();

        if (StringUtils.hasText(filter.getRole())) {
            List<String> auth0UserIds = auth0ManagementApiAdaptor.getAuth0UserIdsByRole(filter.getRole());
            if (auth0UserIds.isEmpty()) {
                return null;
            }
            appendUserIdsQuery(queryBuilder, auth0UserIds);
        }

        if (StringUtils.hasText(filter.getName())) {
            appendQueryPart(queryBuilder, "name", filter.getName());
        }

        if (StringUtils.hasText(filter.getEmail())) {
            appendQueryPart(queryBuilder, "email", filter.getEmail());
        }

        return !queryBuilder.isEmpty() ? queryBuilder.toString() : null;
    }

    private void appendUserIdsQuery(StringBuilder queryBuilder, List<String> userIds) {
        queryBuilder.append("user_id:(")
            .append(String.join(" OR ", userIds))
            .append(")");
    }

    private void appendQueryPart(StringBuilder queryBuilder, String field, String value) {
        if (!queryBuilder.isEmpty()) {
            queryBuilder.append(" AND ");
        }
        queryBuilder.append(field).append(":").append(value);
    }

    private void enrichUsersWithRoles(List<UserDto> users) {
        List<String> auth0UserIds = users.stream()
            .map(UserDto::getAuth0UserId)
            .toList();

        Map<String, List<RoleDto>> userRolesMap = auth0ManagementApiAdaptor.batchListUserRoles(auth0UserIds);

        users.forEach(user ->
            user.setRoles(userRolesMap.getOrDefault(user.getAuth0UserId(), Collections.emptyList()))
        );
    }

    @Override
    public UserDto assignRolesToUser(UUID userId, List<String> roleIds) {
        log.info("Assigning roles {} to user {}", roleIds, userId);
        UserDto user = getUser(userId);
        if (user == null) {
            throw new UsersException(USER_NOT_FOUND.code(), "User[%s] not found.".formatted(userId));
        }

        auth0ManagementApiAdaptor.assignRolesToUser(user.getAuth0UserId(), roleIds);
        return getUser(userId);
    }

    @Override
    public UserDto updateUser(UUID userId, UpdateUserRequest request) {
        log.info("Updating user {} with name: {}", userId, request.getName());
        UserDto user = getUser(userId);

        if (user == null) {
            throw new UsersException(USER_NOT_FOUND.code(), "User[%s] not found.".formatted(userId));
        }

        boolean isNotMobileConnection = isNotMobileConnection(user);
        if (isNotMobileConnection) {
            throw new UsersException(USER_UNABLE_MODIFICATION.code(),
                "The user[%s] information does not support modification.".formatted(userId));
        }

        UserDto updatedUser = auth0ManagementApiAdaptor.updateUser(user.getAuth0UserId(), request);
        List<RoleDto> roles = auth0ManagementApiAdaptor.listUserRoles(updatedUser.getAuth0UserId());
        updatedUser.setRoles(roles);

        return updatedUser;
    }

    @Override
    public UserDto createUser(CreateUserRequest request) {
        log.info("Creating new user with email: {}", request.getEmail());

        UserDto user = auth0ManagementApiAdaptor.createUser(
            request.getEmail(),
            request.getPassword(),
            request.getName(),
            CONNECTION_MOBILE_CONNECTION
        );

        if (CollectionUtils.isNotEmpty(request.getRoles())) {
            auth0ManagementApiAdaptor.assignRolesToUser(user.getAuth0UserId(), request.getRoles());
            List<RoleDto> roles = auth0ManagementApiAdaptor.listUserRoles(user.getAuth0UserId());
            user.setRoles(roles);
        }

        log.info("Successfully created user with ID: {}", user.getId());
        return user;
    }

    private boolean isNotMobileConnection(UserDto user) {
        return user.getIdentities()
            .stream()
            .noneMatch(identity -> {
                String provider = identity.getProvider();
                String connection = identity.getConnection();
                String identityUserId = identity.getUserId();

                return PROVIDER_AUTH0.equals(provider)
                    && CONNECTION_MOBILE_CONNECTION.equals(connection)
                    && user.getAuth0UserId().contains(identityUserId);
            });
    }

}
