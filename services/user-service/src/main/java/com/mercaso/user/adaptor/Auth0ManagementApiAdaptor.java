package com.mercaso.user.adaptor;

import com.mercaso.user.dto.Auth0Client;
import com.mercaso.user.dto.user.RoleDto;
import com.mercaso.user.dto.user.UpdateUserRequest;
import com.mercaso.user.dto.user.UserDto;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public interface Auth0ManagementApiAdaptor {

    UserDto getUser(UUID userId);

    List<UserDto> listUsers(String query);

    List<String> getAuth0UserIdsByRole(String roleId);

    List<RoleDto> listUserRoles(String auth0UserId);

    Map<String, List<RoleDto>> batchListUserRoles(List<String> auth0UserIds);

    List<RoleDto> listRoles();

    void assignRolesToUser(String auth0UserId, List<String> roleIds);

    UserDto updateUser(String auth0UserId, UpdateUserRequest request);

    List<Auth0Client> getAllM2MClients();

    UserDto createUser(String email, String password, String name, String connection);
}
