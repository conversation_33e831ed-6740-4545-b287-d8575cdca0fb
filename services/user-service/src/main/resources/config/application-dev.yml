spring:
  config:
    import: vault://
    activate:
      on-profile: dev
springdoc:
  swagger-ui:
    enabled: true
auth0:
  role-mapping:
    user_group_to_role:
      # user_group_ds: admin_ds
      rol_5tSuF0YGZBy2xstI: rol_Db7YjfzWv5j1cfBp
      # user_group_ds_territory: admin_ds
      rol_7fEu1yQRMQanDW69: rol_Db7YjfzWv5j1cfBp
      # user_group_ims: admin_ims, admin_ds
      rol_l4ySkzxBbNBpJffT: rol_P955BxP3YFjoCyxD, rol_Db7YjfzWv5j1cfBp
      # user_group_wms: admin_wms, admin_ds
      rol_6YWr9NUBoKdoYOXj: rol_Bw8kCu0QHr86hwxF, rol_Db7YjfzWv5j1cfBp
      # user_group_master_catalog: admin_master_catalog
      rol_fQB2DeYHdAmRBRXu: rol_LTgXaYx1U0KXSXO7
      # user_group_users_write: admin_user
      rol_wi8OAzPC8wqeHoP6: rol_kHXZcXjMZoHE3KqH
      # user_group_picker: admin_picker
      rol_gNuETNVmlFyK3RPJ: rol_h3g96rILcEmrpFf1
      # user_group_delivery_manager: admin_delivery_manager
      rol_TVXYoVUqizKTkrMz: rol_487cYAQtNysepG30
      # user_group_driver: admin_driver
      rol_6dhY5SMxKaXSwyaO: rol_38thTQ4UsII4UwtW
      # user_group_transfer: admin_transfer
      rol_ngkHHDtQLq8zPqSE: rol_N5wYGnQ1V8BvlX8l
      # user_group_tools_rippling: admin_tools_rippling
      rol_ExyfRPenA1maeN53: rol_3dGMdYOS06DjEnYg
      # user_group_receiver: admin_receiver
      rol_xcHJ6uH8zafY5bi3: rol_xog3EBEUZzHTwPqu


  m2m:
    clients:
      rM7FTLJJYAE4dfVmqGIbnQJ47JXZB5Y0: ${auth0_m2m_airflow_key}
      JJE0LIDKSphApe9LSc54ciG0z2LO13IH: ${auth0_m2m_wms_key}
      97HM7r4Pww2s1XHBdB8TXNazbIAzEO5O: ${auth0_m2m_ims_key}
      miDjI6l7Q2tE1kQeKWcy6XmCQch0ZYnL: ${auth0_m2m_ecommerce_key}
