spring:
  config:
    import: vault://
    activate:
      on-profile: sat

auth0:
  role-mapping:
    user_group_to_role:
      # user_group_ds: admin_ds
      rol_oRwmdi8YLnEhFKiG: rol_UrPSnMpdpqonnOPg
      # user_group_ds_territory: admin_ds
      rol_xhRy9Ax6XFZgv9Se: rol_UrPSnMpdpqonnOPg
      # user_group_ims: admin_ims, admin_ds
      rol_tXVcZG2jIXKD4gsD: rol_SdrJj8IBjrDVq9L6, rol_UrPSnMpdpqonnOPg
      # user_group_wms: admin_wms, admin_ds
      rol_Ccvsgq9XdWqUSEOu: rol_vC0pMrPD4YcBz6kx, rol_UrPSnMpdpqonnOPg
      # user_group_master_catalog: admin_master_catalog
      rol_cS3fgAYPalCF1pWM: rol_lM1ouEGSf6CnjQYQ
      # user_group_users_write: admin_user
      rol_g0s5bCc4LHoAQGxj: rol_ROoW9W4iWG2ugs7n
      # user_group_picker: admin_picker
      rol_G9yqOa31VoG7fxmN: rol_8qabjHD5JOTCXdVi
      # user_group_delivery_manager: admin_delivery_manager
      rol_KTl6erEZjnajxHsP: rol_R6ypmxTVJbgdu5d5
      # user_group_driver: admin_driver
      rol_DczDeGLRfQL3gyxd: rol_cCjawDzBJrZFjVxd
      # user_group_transfer: admin_transfer
      rol_ARqrZqeljwGrwC9Z: rol_BVeSBXQo9VjXxeiv
      # user_group_tools_rippling: admin_tools_rippling
      rol_WXNGseI6f9FDRB4k: rol_ZCBQlaLNwTn5GdbW
      # user_group_receiver: admin_receiver
      rol_nvsxKqzsMahpL0bm: rol_JDFbzHA2wExcMXQL

  m2m:
    clients:
      D553LhMRDpj8AwR4oRMNaXogJGmDRlFd: ${auth0_m2m_airflow_key}
      QGCWzxhQ0fpSFFv713nXLw6wslChRY2m: ${auth0_m2m_wms_key}
      lrybZrpNo6lS44V5wxCVlSX56UY25FTo: ${auth0_m2m_ims_key}
      46IS1XRr5iRlPYyQIjh5s5KRsgquq1lj: ${auth0_m2m_ecommerce_key}
