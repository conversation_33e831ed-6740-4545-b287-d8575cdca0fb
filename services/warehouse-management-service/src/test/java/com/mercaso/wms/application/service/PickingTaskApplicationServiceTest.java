package com.mercaso.wms.application.service;


import static com.mercaso.wms.infrastructure.contant.FeatureFlagKeys.TRANSFER_LOST_FOUND_INVENTORY;
import static com.mercaso.wms.utils.MockDataUtils.buildPickingTask;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.mercaso.businessevents.dispatcher.BusinessEventDispatcher;
import com.mercaso.businessevents.dto.DispatchResponseDto;
import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.security.auth0.utils.SecurityContextUtil;
import com.mercaso.wms.application.command.pickingtask.AssignPickingTaskCommand;
import com.mercaso.wms.application.command.pickingtask.UpdatePickingTaskCommand;
import com.mercaso.wms.application.command.pickingtask.UpdatePickingTaskCommand.UpdatePickingTaskItemDto;
import com.mercaso.wms.application.dto.PickingTaskDto;
import com.mercaso.wms.application.mapper.pickingtask.PickingTaskDtoApplicationMapper;
import com.mercaso.wms.application.queryservice.PickingTaskItemQueryService;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.builder.DataBuilder;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.batch.BatchRepository;
import com.mercaso.wms.domain.batchitem.BatchItemRepository;
import com.mercaso.wms.domain.crossdockitem.enums.CrossDockItemSourceEnum;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.location.LocationRepository;
import com.mercaso.wms.domain.location.enums.LocationType;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItemRepository;
import com.mercaso.wms.infrastructure.cache.LocationCache;
import com.mercaso.wms.infrastructure.event.applicationevent.dispatcher.ApplicationEventDispatcher;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.external.finale.FinaleProductService;
import com.mercaso.wms.infrastructure.external.finale.config.FinaleConfigProperties;
import com.mercaso.wms.infrastructure.external.finale.config.FinaleConfigProperties.SubLocations;
import com.mercaso.wms.infrastructure.external.finale.dto.FinaleEntity;
import com.mercaso.wms.infrastructure.external.finale.dto.FinaleEntityTypeEnum;
import com.mercaso.wms.infrastructure.external.finale.dto.FinaleTransferShipmentResponse;
import com.mercaso.wms.infrastructure.external.ims.ImsAdaptor;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.kafka.common.errors.ResourceNotFoundException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

class PickingTaskApplicationServiceTest {

    private final PickingTaskRepository pickingTaskRepository = mock(PickingTaskRepository.class);

    private final BusinessEventDispatcher businessEventDispatcher = mock(BusinessEventDispatcher.class);

    private final PickingTaskDtoApplicationMapper pickingTaskDtoApplicationMapper = mock(PickingTaskDtoApplicationMapper.class);

    private final ApplicationEventDispatcher applicationEventDispatcher = mock(ApplicationEventDispatcher.class);

    private final BatchItemRepository batchItemRepository = mock(BatchItemRepository.class);

    private final PickingTaskItemRepository pickingTaskItemRepository = mock(PickingTaskItemRepository.class);

    private final PickingTaskItemQueryService pickingTaskItemQueryService = mock(PickingTaskItemQueryService.class);

    private final LocationCache locationCache = mock(LocationCache.class);

    private final LocationRepository locationRepository = mock(LocationRepository.class);

    private final FinaleConfigProperties finaleConfigProperties = mock(FinaleConfigProperties.class);

    private final FinaleProductService finaleProductService = mock(FinaleProductService.class);

    private final ImsAdaptor imsAdaptor = mock(ImsAdaptor.class);

    private final BatchRepository batchRepository = mock(BatchRepository.class);

    private final CrossDockTaskItemService crossDockTaskItemService = mock(CrossDockTaskItemService.class);

    private final FeatureFlagsManager featureFlagsManager = mock(FeatureFlagsManager.class);

    private final PickingTaskApplicationService pickingTaskApplicationService = new PickingTaskApplicationService(
        pickingTaskRepository,
        businessEventDispatcher,
        pickingTaskDtoApplicationMapper,
        applicationEventDispatcher,
        batchItemRepository,
        pickingTaskItemRepository,
        imsAdaptor,
        pickingTaskItemQueryService,
        locationCache,
        locationRepository,
        finaleConfigProperties,
        finaleProductService,
        batchRepository,
        crossDockTaskItemService,
        featureFlagsManager);

    private UUID pickingTaskId;
    private PickingTask pickingTask;
    private PickingTaskDto pickingTaskDto;

    @BeforeEach
    void setUp() {
        pickingTaskId = UUID.randomUUID();
        pickingTask = mock(PickingTask.class);
        pickingTaskDto = mock(PickingTaskDto.class);

        when(pickingTaskDto.getId()).thenReturn(pickingTaskId);
        when(pickingTaskRepository.findById(pickingTaskId)).thenReturn(pickingTask);
        when(pickingTaskDtoApplicationMapper.domainToDto(any())).thenReturn(pickingTaskDto);
    }

    @Test
    void assignPickingTask_Success() {
        // Arrange
        AssignPickingTaskCommand command = AssignPickingTaskCommand.builder().build();
        command.setPickerUserId(UUID.randomUUID());
        command.setPickerUserName("Test User");

        when(pickingTaskRepository.save(any())).thenReturn(pickingTask);

        // Act
        PickingTaskDto result = pickingTaskApplicationService.assignPickingTask(pickingTaskId, command);

        // Assert
        verify(pickingTask).assignTask(command.getPickerUserId(), command.getPickerUserName());
        verify(businessEventDispatcher).dispatch(any());
        assertEquals(pickingTaskDto, result);
    }

    @Test
    void startPicking_WhenUserNotAuthorized_ThrowsException() {
        // Arrange
        UUID pickerUserId = UUID.randomUUID();
        when(pickingTask.getPickerUserId()).thenReturn(pickerUserId);
        try (MockedStatic<SecurityContextUtil> securityContextUtil = mockStatic(SecurityContextUtil.class)) {
            securityContextUtil.when(SecurityContextUtil::getLoginUserId)
                .thenReturn(UUID.randomUUID().toString());

            // Act & Assert
            assertThrows(WmsBusinessException.class,
                () -> pickingTaskApplicationService.startPicking(pickingTaskId));
        }
    }

    @Test
    void completePickingTask_WhenStatusIsPicked_CompletesSuccessfully() {
        // Arrange
        when(pickingTask.getStatus()).thenReturn(PickingTaskStatus.PICKED);
        when(pickingTaskDto.getStatus()).thenReturn(PickingTaskStatus.COMPLETED);
        when(pickingTaskRepository.save(any())).thenReturn(pickingTask);
        when(businessEventDispatcher.dispatch(any())).thenReturn(DispatchResponseDto.builder().build());

        // Act
        PickingTaskDto result = pickingTaskApplicationService.completePickingTask(pickingTaskId);

        // Assert
        verify(pickingTask).completePicking();
        verify(businessEventDispatcher).dispatch(any());
        assertEquals(pickingTaskDto, result);
    }

    @Test
    void cancelPickingTask_Success() {
        // Arrange
        String reason = "Test reason";
        when(pickingTaskRepository.save(any())).thenReturn(pickingTask);

        // Act
        PickingTaskDto result = pickingTaskApplicationService.cancelPickingTask(pickingTaskId, reason);

        // Assert
        verify(pickingTask).cancelTask();
        verify(businessEventDispatcher).dispatch(any());
        assertEquals(pickingTaskDto, result);
    }

    @Test
    void getPickingTaskById_WhenNotFound_ThrowsException() {
        // Arrange
        when(pickingTaskRepository.findById(pickingTaskId)).thenReturn(null);

        // Act & Assert
        assertThrows(ResourceNotFoundException.class,
            () -> pickingTaskApplicationService.getPickingTaskById(pickingTaskId));
    }

    @Test
    void bulkInventoryTransfer_AllTasksCompleted_Success() {
        String finaleShipmentNumber = "shipment123";
        UUID batchId = UUID.randomUUID();
        Batch batch = mock(Batch.class);
        when(batch.getId()).thenReturn(batchId);

        List<Location> locations = DataBuilder.buildLocations(3, LocationType.BIN);
        Location destinationLocation = locations.getFirst();

        Map<UUID, Location> locationMap = locations.stream()
            .collect(Collectors.toMap(Location::getId, Function.identity()));

        List<PickingTask> pickingTasks = buildPickingTask(UUID.randomUUID(),
            2,
            SourceEnum.MDC,
            PickingTaskStatus.COMPLETED,
            PickingTaskType.ORDER);

        PickingTask task1 = pickingTasks.get(0);
        task1.getPickingTaskItems().forEach(taskItem -> {
            Location location = locations.get(1);
            taskItem.setLocationName(location.getName());
            taskItem.setLocationId(location.getId());
            taskItem.setExpectQty(2);
            taskItem.setPickedQty(2);
        });

        PickingTask task2 = pickingTasks.get(1);
        task2.getPickingTaskItems().forEach(taskItem -> {
            Location location = locations.get(2);
            taskItem.setLocationName(location.getName());
            taskItem.setLocationId(location.getId());
            taskItem.setExpectQty(2);
            taskItem.setPickedQty(2);
        });
        task2.setStatus(PickingTaskStatus.PARTIALLY_COMPLETED);

        FinaleTransferShipmentResponse response = buildFinaleTransferShipmentResponse();
        response.setShipmentIdUser(finaleShipmentNumber);
        response.setShipDate(Instant.now().toString());
        response.setReceiveDate(Instant.now().toString());

        when(pickingTaskRepository.findByBatchId(batchId)).thenReturn(List.of(task1, task2));
        when(locationCache.getLocationMap()).thenReturn(locationMap);
        when(locationRepository.findByName(any())).thenReturn(destinationLocation);
        when(finaleConfigProperties.getMfcFacilityUrl()).thenReturn("marcaso/10000");
        when(finaleConfigProperties.getDomain()).thenReturn("marcaso");
        when(finaleProductService.createTransferShipment(any())).thenReturn(response);
        when(finaleProductService.shipTransferShipment(any())).thenReturn(response);
        when(finaleProductService.receiveTransferShipment(any())).thenReturn(response);
        when(batchRepository.findById(any())).thenReturn(batch);
        when(finaleConfigProperties.getSubLocations()).thenReturn(new SubLocations("LOST_FOUND", "PHOTO-STUDIO"));

        pickingTaskApplicationService.bulkInventoryTransfer(batch);

        verify(batch).setFinaleTransferShipmentNumber(finaleShipmentNumber);
        verify(pickingTaskRepository, times(1)).findByBatchId(batchId);
        verify(finaleProductService, times(1)).createTransferShipment(any());
        verify(finaleProductService, times(1)).shipTransferShipment(any());
        verify(finaleProductService, times(1)).receiveTransferShipment(any());
    }

    @Test
    void bulkInventoryTransfer_when_transferShipment_generated() {
        String finaleShipmentNumber = "shipment123";
        UUID batchId = UUID.randomUUID();
        Batch batch = mock(Batch.class);
        when(batch.getId()).thenReturn(batchId);
        FinaleEntity finaleEntity = new FinaleEntity();
        finaleEntity.setEntityType(FinaleEntityTypeEnum.TRANSFER_SHIPMENT);
        finaleEntity.setEntityId(finaleShipmentNumber);
        List<FinaleEntity> finaleEntityList = new ArrayList<>();
        finaleEntityList.add(finaleEntity);
        when(batch.getFinaleEntities()).thenReturn(SerializationUtils.toTree(new ArrayList<>(finaleEntityList)));

        List<Location> locations = DataBuilder.buildLocations(3, LocationType.BIN);
        Location destinationLocation = locations.getFirst();

        Map<UUID, Location> locationMap = locations.stream()
            .collect(Collectors.toMap(Location::getId, Function.identity()));

        List<PickingTask> pickingTasks = buildPickingTask(UUID.randomUUID(),
            2,
            SourceEnum.MDC,
            PickingTaskStatus.COMPLETED,
            PickingTaskType.ORDER);

        PickingTask task1 = pickingTasks.get(0);
        task1.getPickingTaskItems().forEach(taskItem -> {
            Location location = locations.get(1);
            taskItem.setLocationName(location.getName());
            taskItem.setLocationId(location.getId());
            taskItem.setExpectQty(2);
            taskItem.setPickedQty(2);
        });

        PickingTask task2 = pickingTasks.get(1);
        task2.getPickingTaskItems().forEach(taskItem -> {
            Location location = locations.get(2);
            taskItem.setLocationName(location.getName());
            taskItem.setLocationId(location.getId());
            taskItem.setExpectQty(2);
            taskItem.setPickedQty(2);
        });
        task2.setStatus(PickingTaskStatus.PARTIALLY_COMPLETED);

        FinaleTransferShipmentResponse response = buildFinaleTransferShipmentResponse();
        response.setShipmentIdUser(finaleShipmentNumber);
        response.setShipDate(Instant.now().toString());
        response.setReceiveDate(Instant.now().toString());

        when(pickingTaskRepository.findByBatchId(batchId)).thenReturn(List.of(task1, task2));
        when(locationCache.getLocationMap()).thenReturn(locationMap);
        when(locationRepository.findByName(any())).thenReturn(destinationLocation);
        when(finaleConfigProperties.getMfcFacilityUrl()).thenReturn("marcaso/10000");
        when(finaleConfigProperties.getDomain()).thenReturn("marcaso");
        when(finaleProductService.createTransferShipment(any())).thenReturn(response);
        when(finaleProductService.shipTransferShipment(any())).thenReturn(response);
        when(finaleProductService.receiveTransferShipment(any())).thenReturn(response);
        when(finaleConfigProperties.getSubLocations()).thenReturn(new SubLocations("LOST_FOUND", "PHOTO-STUDIO"));

        pickingTaskApplicationService.bulkInventoryTransfer(batch);

        verify(batch).setFinaleTransferShipmentNumber(finaleShipmentNumber);
        verify(pickingTaskRepository, times(1)).findByBatchId(batchId);
        verify(finaleProductService, times(1)).createTransferShipment(any());
        verify(finaleProductService, times(1)).shipTransferShipment(any());
        verify(finaleProductService, times(1)).receiveTransferShipment(any());
        verify(batchRepository, never()).save(any());
    }

    @Test
    void bulkInventoryTransfer_TasksNotCompleted_LogIncompleteCount() {
        // Arrange
        Batch batch = mock(Batch.class);
        UUID batchId = UUID.randomUUID();
        when(batch.getId()).thenReturn(batchId);

        PickingTask task1 = mock(PickingTask.class);
        PickingTask task2 = mock(PickingTask.class);
        when(task1.getStatus()).thenReturn(PickingTaskStatus.PICKING);
        when(task2.getStatus()).thenReturn(PickingTaskStatus.ASSIGNED);

        when(pickingTaskRepository.findByBatchId(batchId)).thenReturn(List.of(task1, task2));

        pickingTaskApplicationService.bulkInventoryTransfer(batch);

        verify(pickingTaskRepository).findByBatchId(batchId);
        verifyNoInteractions(finaleProductService);
    }

    @Test
    void updatePickingTask_ValidCommand_UpdatesTaskSuccessfully() {
        UpdatePickingTaskCommand command = new UpdatePickingTaskCommand();
        UpdatePickingTaskItemDto itemDto = new UpdatePickingTaskItemDto();
        UUID itemId = UUID.randomUUID();
        itemDto.setId(itemId);
        itemDto.setPickedQty(2);
        command.setUpdatePickingTaskItemDtos(List.of(itemDto));
        PickingTaskItem beforeItem = PickingTaskItem.builder().id(itemId).pickedQty(1).build();
        PickingTaskItem afterItem = PickingTaskItem.builder().id(itemId).pickedQty(2).build();
        when(pickingTask.getPickingTaskItems())
            .thenReturn(List.of(beforeItem))
            .thenReturn(List.of(afterItem));
        when(pickingTaskRepository.update(any())).thenReturn(pickingTask);
        when(pickingTask.getStatus()).thenReturn(PickingTaskStatus.PICKED);
        when(pickingTask.getType()).thenReturn(PickingTaskType.BATCH);
        UUID batchId = UUID.randomUUID();
        when(pickingTask.getBatchId()).thenReturn(batchId);

        PickingTaskDto result = pickingTaskApplicationService.updatePickingTask(pickingTaskId, command);
        verify(pickingTask).update(eq(command), any());
        verify(pickingTaskRepository).update(pickingTask);
        Map<UUID, Integer> expectedMap = Map.of(itemId, 2);
        verify(crossDockTaskItemService, times(1)).handleTaskItemQtyChange(eq(expectedMap),
            eq(CrossDockItemSourceEnum.PICKING_TASK),
            eq(batchId));
        assertEquals(pickingTaskDto, result);
    }

    @Test
    void updatePickingTask_withErrorInfo_UpdatesTaskSuccessfully() {
        List<Location> locations = DataBuilder.buildLocations(1, LocationType.BIN);
        PickingTask pickingTask = PickingTask.builder()
            .id(UUID.randomUUID())
            .batchId(UUID.randomUUID())
            .number("123456")
            .pickerUserId(UUID.randomUUID())
            .pickerUserName("test user")
            .status(PickingTaskStatus.PICKING)
            .build();

        PickingTaskItem taskItem = PickingTaskItem.builder()
            .id(UUID.randomUUID())
            .pickingTaskId(pickingTask.getId())
            .errorInfo("Test error info")
            .locationId(locations.getFirst().getId())
            .expectQty(2)
            .pickedQty(0)
            .build();
        pickingTask.setPickingTaskItems(List.of(taskItem));

        UpdatePickingTaskCommand command = new UpdatePickingTaskCommand();
        UpdatePickingTaskItemDto updatePickingTaskItemDto = new UpdatePickingTaskItemDto();
        updatePickingTaskItemDto.setErrorInfo("Test error info");
        updatePickingTaskItemDto.setPickedQty(0);
        updatePickingTaskItemDto.setId(taskItem.getId());
        command.setUpdatePickingTaskItemDtos(List.of(updatePickingTaskItemDto));

        PickingTask mockPickingTask = mock(PickingTask.class);
        when(mockPickingTask.getId()).thenReturn(pickingTask.getId());
        when(mockPickingTask.getStatus()).thenReturn(PickingTaskStatus.FAILED);

        when(pickingTaskRepository.findById(pickingTask.getId())).thenReturn(mockPickingTask);
        when(locationCache.getLocationMap()).thenReturn(Map.of());
        when(pickingTaskRepository.update(mockPickingTask)).thenReturn(mockPickingTask);
        when(pickingTaskDtoApplicationMapper.domainToDto(mockPickingTask)).thenReturn(pickingTaskDto);
        when(pickingTaskDto.getStatus()).thenReturn(PickingTaskStatus.FAILED);

        PickingTaskDto result = pickingTaskApplicationService.updatePickingTask(pickingTask.getId(), command);

        verify(mockPickingTask).update(eq(command), any());
        assertEquals(PickingTaskStatus.FAILED, result.getStatus());
    }

    @Test
    void updatePickingTask_TaskNotFound_ThrowsException() {
        UUID pickingTaskId = UUID.randomUUID();
        UpdatePickingTaskCommand command = mock(UpdatePickingTaskCommand.class);

        when(pickingTaskRepository.findById(pickingTaskId)).thenReturn(null);

        assertThrows(ResourceNotFoundException.class,
            () -> pickingTaskApplicationService.updatePickingTask(pickingTaskId, command));
    }

    @Test
    void updatePickingTask_LocationMapEmpty_UpdatesWithoutError() {
        UUID pickingTaskId = UUID.randomUUID();
        UpdatePickingTaskCommand command = mock(UpdatePickingTaskCommand.class);
        PickingTask pickingTask = mock(PickingTask.class);
        PickingTask updatedPickingTask = mock(PickingTask.class);
        PickingTaskDto pickingTaskDto = mock(PickingTaskDto.class);

        when(pickingTaskRepository.findById(pickingTaskId)).thenReturn(pickingTask);
        when(locationCache.getLocationMap()).thenReturn(Map.of());
        when(pickingTaskRepository.update(pickingTask)).thenReturn(updatedPickingTask);
        when(pickingTaskDtoApplicationMapper.domainToDto(updatedPickingTask)).thenReturn(pickingTaskDto);

        PickingTaskDto result = pickingTaskApplicationService.updatePickingTask(pickingTaskId, command);

        verify(pickingTask).update(command, Map.of());
        assertEquals(pickingTaskDto, result);
    }

    @Test
    void batchUnassignPickingTasks_should_unassign_only_assigned_tasks_and_dispatch_event() {
        UUID id1 = UUID.randomUUID();
        UUID id2 = UUID.randomUUID();
        PickingTask assignedTask = mock(PickingTask.class);
        PickingTask notAssignedTask = mock(PickingTask.class);
        PickingTaskDto assignedDto = mock(PickingTaskDto.class);

        when(assignedTask.getStatus()).thenReturn(PickingTaskStatus.ASSIGNED);
        when(notAssignedTask.getStatus()).thenReturn(PickingTaskStatus.CREATED);
        when(pickingTaskRepository.findByIds(List.of(id1, id2))).thenReturn(List.of(assignedTask, notAssignedTask));
        when(pickingTaskRepository.saveAll(List.of(assignedTask))).thenReturn(List.of(assignedTask));
        when(pickingTaskDtoApplicationMapper.domainToDtos(List.of(assignedTask))).thenReturn(List.of(assignedDto));

        List<PickingTaskDto> result = pickingTaskApplicationService.batchUnassignPickingTasks(List.of(id1, id2));

        verify(assignedTask, times(1)).unassignTask();
        verify(notAssignedTask, never()).unassignTask();
        verify(businessEventDispatcher, times(1)).dispatch(any());
        assertEquals(1, result.size());
        Assertions.assertSame(assignedDto, result.getFirst());
    }

    @Test
    void batchUnassignPickingTasks_should_return_empty_when_no_assigned_tasks() {
        UUID id1 = UUID.randomUUID();
        PickingTask notAssignedTask = mock(PickingTask.class);
        when(notAssignedTask.getStatus()).thenReturn(PickingTaskStatus.CREATED);
        when(pickingTaskRepository.findByIds(List.of(id1))).thenReturn(List.of(notAssignedTask));

        List<PickingTaskDto> result = pickingTaskApplicationService.batchUnassignPickingTasks(List.of(id1));

        verify(notAssignedTask, never()).unassignTask();
        verify(businessEventDispatcher, never()).dispatch(any());
        assertTrue(result.isEmpty());
    }

    @Test
    void batchUnassignPickingTasks_should_throw_when_no_tasks_found() {
        UUID id1 = UUID.randomUUID();
        when(pickingTaskRepository.findByIds(List.of(id1))).thenReturn(Collections.emptyList());

        assertThrows(org.apache.kafka.common.errors.ResourceNotFoundException.class,
            () -> pickingTaskApplicationService.batchUnassignPickingTasks(List.of(id1)));
    }

    @Test
    void transferUnpickedItemsToLostFound_NoPartiallyCompletedTasks_DoesNotCreateTransfer() {
        // Arrange
        UUID batchId = UUID.randomUUID();
        Batch batch = mock(Batch.class);
        when(batch.getId()).thenReturn(batchId);
        when(batch.getTag()).thenReturn("2024-01-15");

        List<Location> locations = DataBuilder.buildLocations(2, LocationType.BIN);
        Map<UUID, Location> locationMap = new HashMap<>();

        // Ensure locations have finaleId
        locations.forEach(location -> {
            location.setFinaleId("finale-" + location.getId().toString().substring(0, 8));
            locationMap.put(location.getId(), location);
        });

        List<PickingTask> completedTasks = buildPickingTask(UUID.randomUUID(),
            1,
            SourceEnum.MDC,
            PickingTaskStatus.COMPLETED,
            PickingTaskType.ORDER);

        // Set locations for picking task items
        completedTasks.forEach(task -> {
            task.getPickingTaskItems().forEach(item -> {
                item.setLocationId(locations.get(0).getId());
                item.setExpectQty(5);
                item.setPickedQty(5); // Fully picked
            });
        });

        FinaleConfigProperties.SubLocations subLocations = mock(FinaleConfigProperties.SubLocations.class);
        when(subLocations.getLostFound()).thenReturn("LOST-FOUND");

        Location lostFoundLocation = Location.builder()
            .id(UUID.randomUUID())
            .name("LOST-FOUND")
            .finaleId("lost-found-id")
            .build();
        Location destinationLocation = locations.get(1);

        when(pickingTaskRepository.findByBatchId(batchId)).thenReturn(completedTasks);
        when(locationCache.getLocationMap()).thenReturn(locationMap);
        when(locationRepository.findByName("SHIP-SB")).thenReturn(destinationLocation);
        when(locationRepository.findByName("LOST-FOUND")).thenReturn(lostFoundLocation);
        when(finaleConfigProperties.getMfcFacilityUrl()).thenReturn("marcaso/10000");
        when(finaleConfigProperties.getDomain()).thenReturn("marcaso");
        when(finaleConfigProperties.getShipSb()).thenReturn("SHIP-SB");
        when(finaleConfigProperties.getSubLocations()).thenReturn(subLocations);

        FinaleTransferShipmentResponse response = buildFinaleTransferShipmentResponse();
        response.setShipmentIdUser("transfer123");
        when(finaleProductService.createTransferShipment(any())).thenReturn(response);
        when(finaleProductService.shipTransferShipment(any())).thenReturn(response);
        when(finaleProductService.receiveTransferShipment(any())).thenReturn(response);

        // Act
        pickingTaskApplicationService.bulkInventoryTransfer(batch);

        // Assert - Should create transfer for completed tasks, but not for lost found since no partial tasks
        verify(finaleProductService, times(1)).createTransferShipment(any());
    }

    @Test
    void transferUnpickedItemsToLostFound_AllItemsFullyPicked_DoesNotCreateTransfer() {
        // Arrange
        UUID batchId = UUID.randomUUID();
        Batch batch = mock(Batch.class);
        when(batch.getId()).thenReturn(batchId);
        when(batch.getTag()).thenReturn("2024-01-15");

        List<Location> locations = DataBuilder.buildLocations(2, LocationType.BIN);
        Map<UUID, Location> locationMap = locations.stream()
            .collect(Collectors.toMap(Location::getId, Function.identity()));

        List<PickingTask> tasks = buildPickingTask(UUID.randomUUID(),
            1,
            SourceEnum.MDC,
            PickingTaskStatus.PARTIALLY_COMPLETED,
            PickingTaskType.ORDER);
        PickingTask partialTask = tasks.get(0);

        // Set all items as fully picked
        partialTask.getPickingTaskItems().forEach(item -> {
            item.setLocationId(locations.get(0).getId());
            item.setExpectQty(5);
            item.setPickedQty(5); // Fully picked
        });

        FinaleConfigProperties.SubLocations subLocations = mock(FinaleConfigProperties.SubLocations.class);
        when(subLocations.getLostFound()).thenReturn("LOST-FOUND");

        Location lostFoundLocation = Location.builder().id(UUID.randomUUID()).name("LOST-FOUND").build();
        Location destinationLocation = Location.builder().id(UUID.randomUUID()).name("SHIP-SB").build();

        setupCommonMocks(batch, List.of(partialTask), locationMap, destinationLocation, lostFoundLocation, subLocations);

        // Act
        pickingTaskApplicationService.bulkInventoryTransfer(batch);

        // Assert - Should only create transfer for completed tasks, not for lost found
        verify(finaleProductService, times(1)).createTransferShipment(any());
    }

    @Test
    void transferUnpickedItemsToLostFound_SingleLocationSingleSku_CreatesOneTransfer() {
        // Arrange
        UUID batchId = UUID.randomUUID();
        Batch batch = mock(Batch.class);
        when(batch.getId()).thenReturn(batchId);
        when(batch.getTag()).thenReturn("2024-01-15");

        List<Location> locations = DataBuilder.buildLocations(3, LocationType.BIN);
        Map<UUID, Location> locationMap = locations.stream()
            .collect(Collectors.toMap(Location::getId, Function.identity()));

        List<PickingTask> tasks = buildPickingTask(UUID.randomUUID(),
            1,
            SourceEnum.MDC,
            PickingTaskStatus.PARTIALLY_COMPLETED,
            PickingTaskType.ORDER);
        PickingTask partialTask = tasks.get(0);

        partialTask.getPickingTaskItems().forEach(item -> {
            item.setLocationId(locations.get(1).getId());
            item.setExpectQty(5);
            item.setPickedQty(2); // 3 unpicked
        });

        FinaleTransferShipmentResponse response = buildFinaleTransferShipmentResponse();
        response.setShipmentIdUser("transfer123");
        response.setShipDate(Instant.now().toString());
        response.setReceiveDate(Instant.now().toString());

        FinaleConfigProperties.SubLocations subLocations = mock(FinaleConfigProperties.SubLocations.class);
        when(subLocations.getLostFound()).thenReturn("LOST-FOUND");

        Location lostFoundLocation = Location.builder()
            .id(UUID.randomUUID())
            .name("LOST-FOUND")
            .finaleId("lost-found-id")
            .build();
        Location destinationLocation = Location.builder().id(UUID.randomUUID()).name("SHIP-SB").build();

        setupCommonMocks(batch, List.of(partialTask), locationMap, destinationLocation, lostFoundLocation, subLocations);
        when(finaleProductService.createTransferShipment(any())).thenReturn(response);

        when(featureFlagsManager.isFeatureOn(TRANSFER_LOST_FOUND_INVENTORY)).thenReturn(true);

        // Act
        pickingTaskApplicationService.bulkInventoryTransfer(batch);

        // Assert
        verify(finaleProductService, times(2)).createTransferShipment(any()); // One for completed, one for lost found
    }

    @Test
    void transferUnpickedItemsToLostFound_SameSkuSameLocation_AccumulatesQuantity() {
        // Arrange
        UUID batchId = UUID.randomUUID();
        Batch batch = mock(Batch.class);
        when(batch.getId()).thenReturn(batchId);
        when(batch.getTag()).thenReturn("2024-01-15");

        List<Location> locations = DataBuilder.buildLocations(3, LocationType.BIN);
        Map<UUID, Location> locationMap = locations.stream()
            .collect(Collectors.toMap(Location::getId, Function.identity()));

        // Create multiple tasks with same SKU and location
        List<PickingTask> tasks = buildPickingTask(UUID.randomUUID(),
            2,
            SourceEnum.MDC,
            PickingTaskStatus.PARTIALLY_COMPLETED,
            PickingTaskType.ORDER);

        tasks.forEach(task -> {
            task.getPickingTaskItems().forEach(item -> {
                item.setLocationId(locations.get(1).getId()); // Same location
                item.setSkuNumber("SKU001"); // Same SKU
                item.setExpectQty(5);
                item.setPickedQty(2); // 3 unpicked each
            });
        });

        FinaleTransferShipmentResponse response = buildFinaleTransferShipmentResponse();
        response.setShipmentIdUser("transfer123");
        response.setShipDate(Instant.now().toString());
        response.setReceiveDate(Instant.now().toString());

        FinaleConfigProperties.SubLocations subLocations = mock(FinaleConfigProperties.SubLocations.class);
        when(subLocations.getLostFound()).thenReturn("LOST-FOUND");

        Location lostFoundLocation = Location.builder()
            .id(UUID.randomUUID())
            .name("LOST-FOUND")
            .finaleId("lost-found-id")
            .build();
        Location destinationLocation = Location.builder().id(UUID.randomUUID()).name("SHIP-SB").build();

        setupCommonMocks(batch, tasks, locationMap, destinationLocation, lostFoundLocation, subLocations);
        when(finaleProductService.createTransferShipment(any())).thenReturn(response);

        when(featureFlagsManager.isFeatureOn(TRANSFER_LOST_FOUND_INVENTORY)).thenReturn(true);

        // Act
        pickingTaskApplicationService.bulkInventoryTransfer(batch);

        // Assert
        verify(finaleProductService, times(2)).createTransferShipment(any());
    }

    @Test
    void transferUnpickedItemsToLostFound_DifferentLocations_CreatesSeparateTransfers() {
        // Arrange
        UUID batchId = UUID.randomUUID();
        Batch batch = mock(Batch.class);
        when(batch.getId()).thenReturn(batchId);
        when(batch.getTag()).thenReturn("2024-01-15");

        List<Location> locations = DataBuilder.buildLocations(4, LocationType.BIN);
        Map<UUID, Location> locationMap = locations.stream()
            .collect(Collectors.toMap(Location::getId, Function.identity()));

        List<PickingTask> tasks = buildPickingTask(UUID.randomUUID(),
            2,
            SourceEnum.MDC,
            PickingTaskStatus.PARTIALLY_COMPLETED,
            PickingTaskType.ORDER);

        // First task in location 1
        tasks.get(0).getPickingTaskItems().forEach(item -> {
            item.setLocationId(locations.get(1).getId());
            item.setExpectQty(5);
            item.setPickedQty(2);
        });

        // Second task in location 2
        tasks.get(1).getPickingTaskItems().forEach(item -> {
            item.setLocationId(locations.get(2).getId());
            item.setExpectQty(4);
            item.setPickedQty(1);
        });

        FinaleTransferShipmentResponse response = buildFinaleTransferShipmentResponse();
        response.setShipmentIdUser("transfer123");
        response.setShipDate(Instant.now().toString());
        response.setReceiveDate(Instant.now().toString());

        FinaleConfigProperties.SubLocations subLocations = mock(FinaleConfigProperties.SubLocations.class);
        when(subLocations.getLostFound()).thenReturn("LOST-FOUND");

        Location lostFoundLocation = Location.builder()
            .id(UUID.randomUUID())
            .name("LOST-FOUND")
            .finaleId("lost-found-id")
            .build();
        Location destinationLocation = Location.builder().id(UUID.randomUUID()).name("SHIP-SB").build();

        setupCommonMocks(batch, tasks, locationMap, destinationLocation, lostFoundLocation, subLocations);
        when(finaleProductService.createTransferShipment(any())).thenReturn(response);

        when(featureFlagsManager.isFeatureOn(TRANSFER_LOST_FOUND_INVENTORY)).thenReturn(true);

        // Act
        pickingTaskApplicationService.bulkInventoryTransfer(batch);

        // Assert - Should create 2 transfers: 1 for completed tasks + 1 for all lost found items
        // Lost found transfer will contain items from different locations in single shipment
        verify(finaleProductService, times(2)).createTransferShipment(any());
    }

    @Test
    void transferUnpickedItemsToLostFound_LocationNotFound_SkipsItemWithWarning() {
        // Arrange
        UUID batchId = UUID.randomUUID();
        Batch batch = mock(Batch.class);
        when(batch.getId()).thenReturn(batchId);
        when(batch.getTag()).thenReturn("2024-01-15");

        List<Location> locations = DataBuilder.buildLocations(2, LocationType.BIN);
        Map<UUID, Location> locationMap = Map.of(locations.get(0).getId(), locations.get(0));

        List<PickingTask> tasks = buildPickingTask(UUID.randomUUID(),
            1,
            SourceEnum.MDC,
            PickingTaskStatus.PARTIALLY_COMPLETED,
            PickingTaskType.ORDER);
        PickingTask partialTask = tasks.get(0);

        partialTask.getPickingTaskItems().forEach(item -> {
            item.setLocationId(UUID.randomUUID()); // Non-existent location
            item.setExpectQty(5);
            item.setPickedQty(2);
        });

        FinaleConfigProperties.SubLocations subLocations = mock(FinaleConfigProperties.SubLocations.class);
        when(subLocations.getLostFound()).thenReturn("LOST-FOUND");

        Location lostFoundLocation = Location.builder().id(UUID.randomUUID()).name("LOST-FOUND").build();
        Location destinationLocation = Location.builder().id(UUID.randomUUID()).name("SHIP-SB").build();

        setupCommonMocks(batch, List.of(partialTask), locationMap, destinationLocation, lostFoundLocation, subLocations);

        // Act
        pickingTaskApplicationService.bulkInventoryTransfer(batch);

        // Assert - Should not create transfer for lost found due to missing location
        verify(finaleProductService, times(0)).createTransferShipment(any()); // No calls since location is missing
    }

    @Test
    void transferUnpickedItemsToLostFound_ProcessFinaleTransfersFails_LogsWarning() {
        // Arrange
        UUID batchId = UUID.randomUUID();
        Batch batch = mock(Batch.class);
        when(batch.getId()).thenReturn(batchId);
        when(batch.getTag()).thenReturn("2024-01-15");

        List<Location> locations = DataBuilder.buildLocations(3, LocationType.BIN);
        Map<UUID, Location> locationMap = locations.stream()
            .collect(Collectors.toMap(Location::getId, Function.identity()));

        List<PickingTask> tasks = buildPickingTask(UUID.randomUUID(),
            1,
            SourceEnum.MDC,
            PickingTaskStatus.PARTIALLY_COMPLETED,
            PickingTaskType.ORDER);
        PickingTask partialTask = tasks.get(0);

        partialTask.getPickingTaskItems().forEach(item -> {
            item.setLocationId(locations.get(1).getId());
            item.setExpectQty(5);
            item.setPickedQty(2);
        });

        FinaleConfigProperties.SubLocations subLocations = mock(FinaleConfigProperties.SubLocations.class);
        when(subLocations.getLostFound()).thenReturn("LOST-FOUND");

        Location lostFoundLocation = Location.builder()
            .id(UUID.randomUUID())
            .name("LOST-FOUND")
            .finaleId("lost-found-id")
            .build();
        Location destinationLocation = Location.builder().id(UUID.randomUUID()).name("SHIP-SB").build();

        setupCommonMocks(batch, List.of(partialTask), locationMap, destinationLocation, lostFoundLocation, subLocations);

        // Mock successful response for completed tasks, null for lost found
        FinaleTransferShipmentResponse successResponse = buildFinaleTransferShipmentResponse();
        successResponse.setShipmentIdUser("success123");
        when(finaleProductService.createTransferShipment(any()))
            .thenReturn(successResponse) // First call for completed tasks
            .thenReturn(null); // Second call for lost found fails

        when(featureFlagsManager.isFeatureOn(TRANSFER_LOST_FOUND_INVENTORY)).thenReturn(true);

        // Act
        pickingTaskApplicationService.bulkInventoryTransfer(batch);

        // Assert
        verify(finaleProductService, times(2)).createTransferShipment(any());
    }

    @Test
    void transferUnpickedItemsToLostFound_ProcessFinaleTransfersReturnsEmptyShipmentId_LogsWarning() {
        // Arrange
        UUID batchId = UUID.randomUUID();
        Batch batch = mock(Batch.class);
        when(batch.getId()).thenReturn(batchId);
        when(batch.getTag()).thenReturn("2024-01-15");

        List<Location> locations = DataBuilder.buildLocations(3, LocationType.BIN);
        Map<UUID, Location> locationMap = locations.stream()
            .collect(Collectors.toMap(Location::getId, Function.identity()));

        List<PickingTask> tasks = buildPickingTask(UUID.randomUUID(),
            1,
            SourceEnum.MDC,
            PickingTaskStatus.PARTIALLY_COMPLETED,
            PickingTaskType.ORDER);
        PickingTask partialTask = tasks.get(0);

        partialTask.getPickingTaskItems().forEach(item -> {
            item.setLocationId(locations.get(1).getId());
            item.setExpectQty(5);
            item.setPickedQty(2);
        });

        FinaleConfigProperties.SubLocations subLocations = mock(FinaleConfigProperties.SubLocations.class);
        when(subLocations.getLostFound()).thenReturn("LOST-FOUND");

        Location lostFoundLocation = Location.builder()
            .id(UUID.randomUUID())
            .name("LOST-FOUND")
            .finaleId("lost-found-id")
            .build();
        Location destinationLocation = Location.builder().id(UUID.randomUUID()).name("SHIP-SB").build();

        setupCommonMocks(batch, List.of(partialTask), locationMap, destinationLocation, lostFoundLocation, subLocations);

        // Mock successful response for completed tasks, empty shipmentIdUser for lost found
        FinaleTransferShipmentResponse successResponse = buildFinaleTransferShipmentResponse();
        successResponse.setShipmentIdUser("success123");

        FinaleTransferShipmentResponse emptyResponse = buildFinaleTransferShipmentResponse();
        emptyResponse.setShipmentIdUser(""); // Empty shipment ID

        when(finaleProductService.createTransferShipment(any()))
            .thenReturn(successResponse) // First call for completed tasks
            .thenReturn(emptyResponse); // Second call for lost found returns empty ID

        when(featureFlagsManager.isFeatureOn(TRANSFER_LOST_FOUND_INVENTORY)).thenReturn(true);

        // Act
        pickingTaskApplicationService.bulkInventoryTransfer(batch);

        // Assert
        verify(finaleProductService, times(2)).createTransferShipment(any());
    }

    @Test
    void transferUnpickedItemsToLostFound_MultipleSkusMultipleLocations_CreatesCorrectTransfers() {
        // Arrange
        UUID batchId = UUID.randomUUID();
        Batch batch = mock(Batch.class);
        when(batch.getId()).thenReturn(batchId);
        when(batch.getTag()).thenReturn("2024-01-15");

        List<Location> locations = DataBuilder.buildLocations(7, LocationType.BIN);
        Map<UUID, Location> locationMap = locations.stream()
            .collect(Collectors.toMap(Location::getId, Function.identity()));

        List<PickingTask> tasks = buildPickingTask(UUID.randomUUID(),
            10,
            SourceEnum.MDC,
            PickingTaskStatus.PARTIALLY_COMPLETED,
            PickingTaskType.ORDER);

        // Location 1: SKU001 (3 remaining), SKU002 (3 remaining)
        tasks.get(0).getPickingTaskItems().forEach(item -> {
            item.setLocationId(locations.get(1).getId());
            item.setSkuNumber("SKU001");
            item.setExpectQty(5);
            item.setPickedQty(2);
        });

        tasks.get(1).getPickingTaskItems().forEach(item -> {
            item.setLocationId(locations.get(1).getId());
            item.setSkuNumber("SKU002");
            item.setExpectQty(4);
            item.setPickedQty(1);
        });

        // Location 2: SKU001 (3 remaining个), SKU003 (2 remaining)
        tasks.get(2).getPickingTaskItems().forEach(item -> {
            item.setLocationId(locations.get(2).getId());
            item.setSkuNumber("SKU001");
            item.setExpectQty(3);
            item.setPickedQty(0);
        });

        tasks.get(3).getPickingTaskItems().forEach(item -> {
            item.setLocationId(locations.get(2).getId());
            item.setSkuNumber("SKU003");
            item.setExpectQty(6);
            item.setPickedQty(4);
        });

        // Location 3: SKU003 (1 remaining), SKU004 (4 remaining)
        tasks.get(4).getPickingTaskItems().forEach(item -> {
            item.setLocationId(locations.get(3).getId());
            item.setSkuNumber("SKU003");
            item.setExpectQty(7);
            item.setPickedQty(6);
        });

        tasks.get(5).getPickingTaskItems().forEach(item -> {
            item.setLocationId(locations.get(3).getId());
            item.setSkuNumber("SKU004");
            item.setExpectQty(8);
            item.setPickedQty(4);
        });

        // Location 4: SKU004 (2 remaining), SKU005 (5 remaining)
        tasks.get(6).getPickingTaskItems().forEach(item -> {
            item.setLocationId(locations.get(4).getId());
            item.setSkuNumber("SKU004");
            item.setExpectQty(5);
            item.setPickedQty(3);
        });

        tasks.get(7).getPickingTaskItems().forEach(item -> {
            item.setLocationId(locations.get(4).getId());
            item.setSkuNumber("SKU005");
            item.setExpectQty(10);
            item.setPickedQty(5);
        });

        // Location 5: SKU005 (1 remaining), SKU001 (2 remaining)
        tasks.get(8).getPickingTaskItems().forEach(item -> {
            item.setLocationId(locations.get(5).getId());
            item.setSkuNumber("SKU005");
            item.setExpectQty(3);
            item.setPickedQty(2);
        });

        tasks.get(9).getPickingTaskItems().forEach(item -> {
            item.setLocationId(locations.get(5).getId());
            item.setSkuNumber("SKU001");
            item.setExpectQty(6);
            item.setPickedQty(4);
        });

        FinaleTransferShipmentResponse response = buildFinaleTransferShipmentResponse();
        response.setShipmentIdUser("transfer123");
        response.setShipDate(Instant.now().toString());
        response.setReceiveDate(Instant.now().toString());

        FinaleConfigProperties.SubLocations subLocations = mock(FinaleConfigProperties.SubLocations.class);
        when(subLocations.getLostFound()).thenReturn("LOST-FOUND");

        Location lostFoundLocation = Location.builder()
            .id(UUID.randomUUID())
            .name("LOST-FOUND")
            .finaleId("lost-found-id")
            .build();
        Location destinationLocation = Location.builder().id(UUID.randomUUID()).name("SHIP-SB").build();

        setupCommonMocks(batch, tasks, locationMap, destinationLocation, lostFoundLocation, subLocations);
        when(finaleProductService.createTransferShipment(any())).thenReturn(response);

        when(featureFlagsManager.isFeatureOn(TRANSFER_LOST_FOUND_INVENTORY)).thenReturn(true);

        // Act
        pickingTaskApplicationService.bulkInventoryTransfer(batch);

        // Assert - Should create 2 transfers: 1 for completed tasks + 1 for all lost found items
        // Lost found transfer will contain all items from different locations in single shipment
        // ShipmentItem.facilityUrl already contains original location info
        verify(finaleProductService, times(2)).createTransferShipment(any());
    }

    @Test
    void transferUnpickedItemsToLostFound_MixedPickingStatus_OnlyProcessesPartiallyCompleted() {
        // Arrange
        UUID batchId = UUID.randomUUID();
        Batch batch = mock(Batch.class);
        when(batch.getId()).thenReturn(batchId);
        when(batch.getTag()).thenReturn("2024-01-15");

        List<Location> locations = DataBuilder.buildLocations(4, LocationType.BIN);
        Map<UUID, Location> locationMap = locations.stream()
            .collect(Collectors.toMap(Location::getId, Function.identity()));

        // Create tasks with different statuses
        List<PickingTask> allTasks = new ArrayList<>();

        // Completed task
        List<PickingTask> completedTasks = buildPickingTask(UUID.randomUUID(),
            1,
            SourceEnum.MDC,
            PickingTaskStatus.COMPLETED,
            PickingTaskType.ORDER);
        completedTasks.get(0).getPickingTaskItems().forEach(item -> {
            item.setLocationId(locations.get(1).getId());
            item.setExpectQty(5);
            item.setPickedQty(5);
        });
        allTasks.addAll(completedTasks);

        // Partially completed task with unpicked items
        List<PickingTask> partialTasks = buildPickingTask(UUID.randomUUID(),
            1,
            SourceEnum.MDC,
            PickingTaskStatus.PARTIALLY_COMPLETED,
            PickingTaskType.ORDER);
        partialTasks.get(0).getPickingTaskItems().forEach(item -> {
            item.setLocationId(locations.get(2).getId());
            item.setExpectQty(5);
            item.setPickedQty(2);
        });
        allTasks.addAll(partialTasks);

        // Another completed task (should be processed for completed tasks)
        List<PickingTask> anotherCompletedTasks = buildPickingTask(UUID.randomUUID(),
            1,
            SourceEnum.MDC,
            PickingTaskStatus.COMPLETED,
            PickingTaskType.ORDER);
        anotherCompletedTasks.get(0).getPickingTaskItems().forEach(item -> {
            item.setLocationId(locations.get(3).getId());
            item.setExpectQty(3);
            item.setPickedQty(3);
        });
        allTasks.addAll(anotherCompletedTasks);

        FinaleTransferShipmentResponse response = buildFinaleTransferShipmentResponse();
        response.setShipmentIdUser("transfer123");
        response.setShipDate(Instant.now().toString());
        response.setReceiveDate(Instant.now().toString());

        FinaleConfigProperties.SubLocations subLocations = mock(FinaleConfigProperties.SubLocations.class);
        when(subLocations.getLostFound()).thenReturn("LOST-FOUND");

        Location lostFoundLocation = Location.builder()
            .id(UUID.randomUUID())
            .name("LOST-FOUND")
            .finaleId("lost-found-id")
            .build();
        Location destinationLocation = Location.builder().id(UUID.randomUUID()).name("SHIP-SB").build();

        setupCommonMocks(batch, allTasks, locationMap, destinationLocation, lostFoundLocation, subLocations);
        when(finaleProductService.createTransferShipment(any())).thenReturn(response);

        when(featureFlagsManager.isFeatureOn(TRANSFER_LOST_FOUND_INVENTORY)).thenReturn(true);

        // Act
        pickingTaskApplicationService.bulkInventoryTransfer(batch);

        // Assert - Should create 2 transfers: 1 for completed tasks + 1 for partially completed lost found
        verify(finaleProductService, times(2)).createTransferShipment(any());
    }

    private void setupCommonMocks(Batch batch, List<PickingTask> tasks, Map<UUID, Location> locationMap,
        Location destinationLocation, Location lostFoundLocation,
        FinaleConfigProperties.SubLocations subLocations) {
        UUID batchId = batch.getId();

        // Ensure all locations have finaleId to avoid NPE
        locationMap.values().forEach(location -> {
            if (location.getFinaleId() == null) {
                location.setFinaleId("finale-" + location.getId().toString().substring(0, 8));
            }
        });

        when(pickingTaskRepository.findByBatchId(batchId)).thenReturn(tasks);
        when(locationCache.getLocationMap()).thenReturn(locationMap);
        when(locationRepository.findByName("SHIP-SB")).thenReturn(destinationLocation);
        when(locationRepository.findByName("LOST-FOUND")).thenReturn(lostFoundLocation);
        when(finaleConfigProperties.getMfcFacilityUrl()).thenReturn("marcaso/10000");
        when(finaleConfigProperties.getDomain()).thenReturn("marcaso");
        when(finaleConfigProperties.getShipSb()).thenReturn("SHIP-SB");
        when(finaleConfigProperties.getSubLocations()).thenReturn(subLocations);

        // Setup successful responses for createTransferShipment calls
        FinaleTransferShipmentResponse defaultResponse = buildFinaleTransferShipmentResponse();
        defaultResponse.setShipmentIdUser("default123");
        defaultResponse.setShipDate(Instant.now().toString());
        defaultResponse.setReceiveDate(Instant.now().toString());

        when(finaleProductService.createTransferShipment(any())).thenReturn(defaultResponse);
        when(finaleProductService.shipTransferShipment(any())).thenReturn(defaultResponse);
        when(finaleProductService.receiveTransferShipment(any())).thenReturn(defaultResponse);
    }

    private FinaleTransferShipmentResponse buildFinaleTransferShipmentResponse() {
        return FinaleTransferShipmentResponse.builder()
            .shipmentId("218277").build();
    }

}