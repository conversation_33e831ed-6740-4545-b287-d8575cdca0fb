package com.mercaso.wms.utils;


import com.fasterxml.jackson.core.type.TypeReference;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.dto.shippingorder.ShippingOrderDto;
import com.mercaso.wms.application.query.ShippingOrderQuery;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import java.util.UUID;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class ShippingOrderResourceApi extends IntegrationTestRestUtil {

    public static final String GET_SHIPPING_ORDER_BY_ID_URL = "/query/shipping-orders/%s";
    private static final String SEARCH_SHIPPING_ORDERS_URL = "/search/shipping-orders";

    public ShippingOrderResourceApi(Environment environment) {
        super(environment);
    }

    public ShippingOrderDto getShippingOrder(UUID id) {
        return getEntity(String.format(GET_SHIPPING_ORDER_BY_ID_URL, id), ShippingOrderDto.class).getBody();
    }

    public Result<ShippingOrderDto> searchShippingOrders(
        String orderNumber,
        String deliveryDate,
        String[] statuses,
        ShippingOrderQuery.SortType sort) throws Exception {
        StringBuilder url = new StringBuilder(SEARCH_SHIPPING_ORDERS_URL + "?page=1&pageSize=20");
        if (orderNumber != null) {
            url.append("&orderNumber=").append(orderNumber);
        }
        if (deliveryDate != null) {
            url.append("&deliveryDate=").append(deliveryDate);
        }
        if (statuses != null) {
            url.append("&statuses=").append(String.join(",", statuses));
        }
        if (sort != null) {
            url.append("&sort=").append(sort);
        }
        String body = getEntity(url.toString(), String.class).getBody();
        return SerializationUtils.readValue(body, new TypeReference<Result<ShippingOrderDto>>() {
        });
    }

}
