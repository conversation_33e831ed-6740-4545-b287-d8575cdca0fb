package com.mercaso.wms.interfaces.search;

import static com.mercaso.wms.utils.MockDataUtils.buildShopifyOrderDto;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.dto.shippingorder.ShippingOrderDto;
import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto;
import com.mercaso.wms.utils.ShippingOrderResourceApi;
import com.mercaso.wms.utils.ShopifyWebhookResourceApi;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class SearchShippingOrderResourceIT extends AbstractIT {

    @Autowired
    ShippingOrderResourceApi shippingOrderResourceApi;

    @Autowired
    ShopifyWebhookResourceApi shopifyWebhookResourceApi;

    @Test
    void searchShippingOrders_withParameters_returnsResult() throws Exception {

        shippingOrderRepository.deleteAll();

        ShopifyOrderDto shopifyOrderDto1 = buildShopifyOrderDto();
        ShopifyOrderDto shopifyOrderDto2 = buildShopifyOrderDto();
        shopifyWebhookResourceApi.webhook(shopifyOrderDto1);
        shopifyWebhookResourceApi.webhook(shopifyOrderDto2);

        Result<ShippingOrderDto> shippingOrderDtoResult = shippingOrderResourceApi.searchShippingOrders(
            null,
            shopifyOrderDto1.getTags().split(",")[0],
            null,
            null);

        assertEquals(2, shippingOrderDtoResult.getTotalCount());
        assertEquals(2, shippingOrderDtoResult.getData().size());
    }

}