package com.mercaso.wms.infrastructure.repository.pickingtask.jpa;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.dto.PickedItemsDto;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class PickingTaskJdbcTemplateIT extends AbstractIT {

    @Autowired
    private PickingTaskJdbcTemplate pickingTaskJdbcTemplate;
    @Autowired
    private PickingTaskRepository pickingTaskRepository;

    @Test
    void when_fetch_picked_items_then_returnPickedItems() {
        Batch batch = Batch.builder()
            .tag(RandomStringUtils.randomAlphabetic(5))
            .build();
        Batch savedBatch = batchRepository.save(batch);
        PickingTaskItem pickingTaskItemDo = PickingTaskItem.builder()
            .batchItemId(UUID.randomUUID())
            .itemId(UUID.randomUUID())
            .skuNumber("skuNumber")
            .title("title")
            .locationName("locationName")
            .errorInfo("errorInfo")
            .pickedQty(5)
            .expectQty(10)
            .pickingSequence(1)
            .prep(RandomStringUtils.randomAlphabetic(5))
            .build();
        PickingTask pickingTask = PickingTask.builder()
            .batchId(UUID.randomUUID())
            .status(PickingTaskStatus.FAILED)
            .batchId(savedBatch.getId())
            .source(SourceEnum.MFC)
            .pickingTaskItems(List.of(pickingTaskItemDo))
            .build();

        // when
        PickingTask saved = pickingTaskRepository.save(pickingTask);

        List<PickedItemsDto> pickedItemsDtos = pickingTaskJdbcTemplate.fetchPickedItemsForDeliveryDate(savedBatch.getTag());

        assertEquals(1, pickedItemsDtos.size());
        assertEquals(saved.getSource().name(), pickedItemsDtos.getFirst().getFinalFrom());
    }

}