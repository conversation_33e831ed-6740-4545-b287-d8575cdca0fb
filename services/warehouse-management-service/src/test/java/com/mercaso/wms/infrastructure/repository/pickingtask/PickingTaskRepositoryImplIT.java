package com.mercaso.wms.infrastructure.repository.pickingtask;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class PickingTaskRepositoryImplIT extends AbstractIT {

    @Autowired
    private PickingTaskRepositoryImpl pickingTaskRepositoryImpl;

    @Test
    void when_save_picking_task_then_success() {
        // given
        PickingTaskItem pickingTaskItemDo = PickingTaskItem.builder()
            .batchItemId(UUID.randomUUID())
            .itemId(UUID.randomUUID())
            .skuNumber("skuNumber")
            .title("title")
            .locationName("locationName")
            .pickingSequence(1)
            .prep(RandomStringUtils.randomAlphabetic(5))
            .build();
        PickingTask pickingTask = PickingTask.builder()
            .batchId(UUID.randomUUID())
            .status(PickingTaskStatus.CREATED)
            .source(SourceEnum.MFC)
            .pickingTaskItems(List.of(pickingTaskItemDo))
            .build();

        // when
        PickingTask saved = pickingTaskRepositoryImpl.save(pickingTask);

        // then
        assertNotNull(saved);
        assertEquals(pickingTask.getBatchId(), saved.getBatchId());
        assertEquals(pickingTask.getStatus(), saved.getStatus());
    }

    @Test
    void when_find_by_id_then_success() {
        // given
        PickingTaskItem pickingTaskItemDo = PickingTaskItem.builder()
            .batchItemId(UUID.randomUUID())
            .itemId(UUID.randomUUID())
            .skuNumber("skuNumber")
            .title("title")
            .locationName("locationName")
            .pickingSequence(1)
            .prep(RandomStringUtils.randomAlphabetic(5))
            .build();
        PickingTask pickingTask = PickingTask.builder()
            .batchId(UUID.randomUUID())
            .status(PickingTaskStatus.CREATED)
            .source(SourceEnum.MFC)
            .pickingTaskItems(List.of(pickingTaskItemDo))
            .build();
        PickingTask saved = pickingTaskRepositoryImpl.save(pickingTask);

        // when
        PickingTask found = pickingTaskRepositoryImpl.findById(saved.getId());

        // then
        assertNotNull(found);
        assertEquals(saved.getId(), found.getId());
    }

    @Test
    void when_update_picking_task_then_success() {
        // given
        PickingTaskItem pickingTaskItemDo = PickingTaskItem.builder()
            .batchItemId(UUID.randomUUID())
            .itemId(UUID.randomUUID())
            .skuNumber("skuNumber")
            .title("title")
            .locationName("locationName")
            .pickingSequence(1)
            .prep(RandomStringUtils.randomAlphabetic(5))
            .build();
        PickingTask pickingTask = PickingTask.builder()
            .batchId(UUID.randomUUID())
            .status(PickingTaskStatus.CREATED)
            .source(SourceEnum.MFC)
            .pickingTaskItems(List.of(pickingTaskItemDo))
            .build();
        PickingTask saved = pickingTaskRepositoryImpl.save(pickingTask);

        // when
        PickingTask retrieve = pickingTaskRepositoryImpl.findById(saved.getId());
        retrieve.setStatus(PickingTaskStatus.COMPLETED);
        PickingTask updated = pickingTaskRepositoryImpl.update(retrieve);

        // then
        assertNotNull(updated);
        assertEquals(saved.getId(), updated.getId());
        assertEquals(PickingTaskStatus.COMPLETED, updated.getStatus());
    }

    @Test
    void when_find_by_batchId_and_type_then_success() {
        // given
        PickingTaskItem pickingTaskItemDo = PickingTaskItem.builder()
            .batchItemId(UUID.randomUUID())
            .itemId(UUID.randomUUID())
            .skuNumber("skuNumber")
            .title("title")
            .locationName("locationName")
            .orderNumber(RandomStringUtils.randomAlphabetic(10))
            .pickingSequence(1)
            .prep(RandomStringUtils.randomAlphabetic(5))
            .build();
        PickingTask pickingTask = PickingTask.builder()
            .batchId(UUID.randomUUID())
            .status(PickingTaskStatus.CREATED)
            .source(SourceEnum.MFC)
            .type(PickingTaskType.ORDER)
            .pickingTaskItems(List.of(pickingTaskItemDo))
            .build();
        PickingTask saved = pickingTaskRepositoryImpl.save(pickingTask);

        // when
        List<PickingTask> pickingTasks = pickingTaskRepositoryImpl.findByBatchIdAndTypeAndSource(saved.getBatchId(),
            PickingTaskType.ORDER, SourceEnum.MFC);

        // then
        assertNotNull(pickingTasks);
        assertTrue(pickingTasks.stream().map(PickingTask::getId).anyMatch(id -> id.equals(saved.getId())));
    }

    @Test
    void when_find_order_numbers_then_success() {
        // given
        PickingTaskItem pickingTaskItemDo = PickingTaskItem.builder()
            .batchItemId(UUID.randomUUID())
            .itemId(UUID.randomUUID())
            .skuNumber("skuNumber")
            .title("title")
            .locationName("locationName")
            .orderNumber(RandomStringUtils.randomAlphabetic(10))
            .pickingSequence(1)
            .prep(RandomStringUtils.randomAlphabetic(5))
            .build();
        PickingTask pickingTask = PickingTask.builder()
            .batchId(UUID.randomUUID())
            .status(PickingTaskStatus.CREATED)
            .source(SourceEnum.MFC)
            .type(PickingTaskType.ORDER)
            .pickingTaskItems(List.of(pickingTaskItemDo))
            .build();
        PickingTask saved = pickingTaskRepositoryImpl.save(pickingTask);

        // when
        List<PickingTask> pickingTasks = pickingTaskRepositoryImpl.findByOrderNumbers(List.of(pickingTaskItemDo.getOrderNumber()));

        // then
        assertNotNull(pickingTasks);
        assertTrue(pickingTasks.stream().map(PickingTask::getId).anyMatch(id -> id.equals(saved.getId())));
    }
}