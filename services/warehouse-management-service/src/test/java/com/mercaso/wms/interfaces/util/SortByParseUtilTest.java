package com.mercaso.wms.interfaces.util;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import org.junit.jupiter.api.Test;
import org.springframework.data.domain.Sort;

class SortByParseUtilTest {

    @Test
    void getSortField_returnsNull_whenSortFieldIsEmpty() {
        assertNull(SortByParseUtil.getSortField(""));
    }

    @Test
    void getSortField_returnsNull_whenSortFieldIsNull() {
        assertNull(SortByParseUtil.getSortField(null));
    }

    @Test
    void getSortField_returnsNull_whenSortFieldHasNoUnderscore() {
        assertNull(SortByParseUtil.getSortField("name"));
    }

    @Test
    void getSortField_returnsNull_whenFieldPartIsEmpty() {
        assertNull(SortByParseUtil.getSortField("_asc"));
    }

    @Test
    void getSortField_returnsNull_whenOrderPartIsEmpty() {
        assertNull(SortByParseUtil.getSortField("name_"));
    }

    @Test
    void getSortField_returnsSortAsc_whenOrderIsAsc() {
        Sort sort = SortByParseUtil.getSortField("CREATED_AT_ASC");
        assertNotNull(sort);
        assertEquals(sort.getOrderFor("createdAt"), Sort.Order.asc("createdAt"));
    }

    @Test
    void getSortField_returnsSortDesc_whenOrderIsDesc() {
        Sort sort = SortByParseUtil.getSortField("NAME_DESC");
        assertNotNull(sort);
        assertEquals(Sort.Order.desc("name"), sort.getOrderFor("name"));
    }

    @Test
    void getSortField_returnsNull_whenOrderIsInvalid() {
        assertNull(SortByParseUtil.getSortField("NAME_INVALID"));
    }
}