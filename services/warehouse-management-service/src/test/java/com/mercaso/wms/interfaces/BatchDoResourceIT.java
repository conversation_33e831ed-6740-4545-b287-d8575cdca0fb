package com.mercaso.wms.interfaces;

import static com.mercaso.wms.utils.DataServiceUtil.mockGetFinaleProducts;
import static com.mercaso.wms.utils.DataServiceUtil.mockGetShopifyOrders;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.alibaba.excel.util.DateUtils;
import com.mercaso.data.client.dto.FinaleAvailableStockDto;
import com.mercaso.data.client.dto.ShopifyOrderDto;
import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.batch.dto.CreateBatchDto;
import com.mercaso.wms.batch.dto.response.Response;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.location.LocationRepository;
import com.mercaso.wms.domain.location.enums.LocationType;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.domain.warehouse.WarehouseRepository;
import com.mercaso.wms.domain.warehouse.enums.WarehouseStatus;
import com.mercaso.wms.utils.BatchResourceApi;
import java.io.FileInputStream;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class BatchDoResourceIT extends AbstractIT {

    @Autowired
    private BatchResourceApi batchResourceApi;
    @Autowired
    private LocationRepository locationRepository;
    @Autowired
    private WarehouseRepository warehouseRepository;

    public static final String LOOK_UP = "1-Mercaso Pick Sheet Template_V7.8_lookup.xlsx";

    private static final String FILE_NAME =
        "Mercaso Pick Sheet" + DateUtils.format(new Date(), DateUtils.DATE_FORMAT_14) + ".xlsx";

    @Test
    void when_crate_batch_should_success() throws Exception {
        // given
        List<ShopifyOrderDto> shopifyOrderDtos = mockGetShopifyOrders();
        List<FinaleAvailableStockDto> finaleAvailableStockDtos = new ArrayList<>();
        shopifyOrderDtos.stream()
            .flatMap(shopifyOrderDto -> shopifyOrderDto.getLineItems().stream())
            .forEach(shopifyOrderLineItemDto -> finaleAvailableStockDtos.add(mockGetFinaleProducts(shopifyOrderLineItemDto.getSku())));

        Warehouse warehouse = warehouseRepository.save(Warehouse.builder()
            .name(RandomStringUtils.randomAlphabetic(5))
            .status(WarehouseStatus.ACTIVE)
            .build());
        locationRepository.save(Location.builder()
            .warehouse(warehouse)
            .type(LocationType.BIN)
            .name(finaleAvailableStockDtos.getFirst().getStockItemsOnHand().getFirst().getSubLocation().getName())
            .build());

        when(shopifyAdaptor.getShopifyOrders(any())).thenReturn(shopifyOrderDtos);
        try (FileInputStream fis = new FileInputStream(LOOK_UP)) {
            when(documentOperations.downloadDocument(any())).thenReturn(fis.readAllBytes());
        }
        when(documentOperations.uploadDocument(any())).thenReturn(new DocumentResponse("http://localhost:8080/file", FILE_NAME));

        CreateBatchDto createBatchDto = new CreateBatchDto();
        createBatchDto.setFileNames(List.of(LOOK_UP));
        createBatchDto.setTaggedWith(LocalDate.now());

        @SuppressWarnings("rawtypes")
        Response batch = batchResourceApi.createBatch(createBatchDto);

        assertNotNull(batch);
    }
}