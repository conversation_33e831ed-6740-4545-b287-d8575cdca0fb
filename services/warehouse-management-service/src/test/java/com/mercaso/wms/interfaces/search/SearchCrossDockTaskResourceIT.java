package com.mercaso.wms.interfaces.search;

import static com.mercaso.wms.utils.MockDataUtils.buildCrossDockTasks;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.dto.view.SearchCrossDockTaskView;
import com.mercaso.wms.domain.crossdock.CrossDockTask;
import com.mercaso.wms.domain.crossdock.CrossDockTaskRepository;
import com.mercaso.wms.utils.CrossDockTaskResourceApi;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class SearchCrossDockTaskResourceIT extends AbstractIT {

    @Autowired
    private CrossDockTaskResourceApi crossDockTaskResourceApi;

    @Autowired
    private CrossDockTaskRepository crossDockTaskRepository;

    @BeforeEach
    void setUp() {
        crossDockTaskRepository.deleteAll();
    }

    @Test
    void searchCrossDockTasks_withValidParameters_returnsResult() throws Exception {
        List<CrossDockTask> crossDockTasks = buildCrossDockTasks(5);
        crossDockTaskRepository.saveAll(crossDockTasks);

        Result<SearchCrossDockTaskView> crossDockTaskViewResult = crossDockTaskResourceApi.searchCrossDockTasks(
            null,
            null,
            null
        );

        assertNotNull(crossDockTaskViewResult);
        assertNotNull(crossDockTaskViewResult.getData());
        assertNotNull(crossDockTaskViewResult.getTotalCount());
        assertEquals(5, crossDockTaskViewResult.getData().size());
        assertEquals(5, crossDockTaskViewResult.getTotalCount());
    }

}