package com.mercaso.wms.application.service;

import static com.mercaso.wms.utils.MockDataUtils.buildPickingTask;
import static com.mercaso.wms.utils.MockDataUtils.buildShippingOrder;
import static com.mercaso.wms.utils.MockDataUtils.buildShippingOrdersWithId;
import static com.mercaso.wms.utils.MockDataUtils.buildShopifyOrderDto;
import static com.mercaso.wms.utils.MockDataUtils.buildWarehouse;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.businessevents.dispatcher.BusinessEventDispatcher;
import com.mercaso.wms.application.dto.scanrecord.OutboundScanRecordDto;
import com.mercaso.wms.application.dto.shippingorder.ShippingOrderDto;
import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto;
import com.mercaso.wms.application.mapper.shippingorder.ShippingOrderDtoApplicationMapper;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import com.mercaso.wms.domain.receivingtask.ReceivingTask;
import com.mercaso.wms.domain.receivingtask.ReceivingTaskRepository;
import com.mercaso.wms.domain.receivingtaskitem.ReceivingTaskItem;
import com.mercaso.wms.domain.shippingorder.ShippingOrder;
import com.mercaso.wms.domain.shippingorder.ShippingOrderRepository;
import com.mercaso.wms.domain.shippingorder.ShippingOrderService;
import com.mercaso.wms.domain.shippingorder.enums.ShippingOrderStatus;
import com.mercaso.wms.domain.warehouse.WarehouseRepository;
import com.mercaso.wms.infrastructure.config.PgAdvisoryLock;
import com.mercaso.wms.infrastructure.external.ims.ImsAdaptor;
import com.mercaso.wms.infrastructure.slackalert.FraudOrderAlert;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;

class ShippingOrderApplicationServiceTest {

    private final ShippingOrderRepository shippingOrderRepository = mock(ShippingOrderRepository.class);

    private final WarehouseRepository warehouseRepository = mock(WarehouseRepository.class);

    private final PickingTaskRepository pickingTaskRepository = mock(PickingTaskRepository.class);

    private final ImsAdaptor imsAdaptor = mock(ImsAdaptor.class);

    private final BusinessEventDispatcher businessEventDispatcher = mock(BusinessEventDispatcher.class);

    private final ShippingOrderDtoApplicationMapper shippingOrderDtoApplicationMapper = mock(ShippingOrderDtoApplicationMapper.class);

    private final PgAdvisoryLock pgAdvisoryLock = mock(PgAdvisoryLock.class);

    private final ReceivingTaskRepository receivingTaskRepository = mock(ReceivingTaskRepository.class);

    private final ShippingOrderService shippingOrderService = mock(ShippingOrderService.class);

    private final FraudOrderAlert fraudOrderAlert = mock(FraudOrderAlert.class);

    private final ShippingOrderApplicationService shippingOrderApplicationService = new ShippingOrderApplicationService(
        shippingOrderRepository,
        pickingTaskRepository,
        warehouseRepository,
        imsAdaptor,
        businessEventDispatcher,
        shippingOrderDtoApplicationMapper,
        pgAdvisoryLock, receivingTaskRepository,
        shippingOrderService,
        fraudOrderAlert);

    @Test
    void when_createOrUpdate_given_shopifyOrderDto_then_return_shippingOrderDto() {
        // given
        ShopifyOrderDto shopifyOrderDto = buildShopifyOrderDto();
        ShippingOrder shippingOrder = buildShippingOrder(shopifyOrderDto);
        // when
        when(warehouseRepository.findByType(any())).thenReturn(List.of(buildWarehouse(UUID.randomUUID())));
        when(shippingOrderRepository.findByOrderNumberAndShopifyOrderId(any(), anyString())).thenReturn(null);
        when(shippingOrderRepository.save(any())).thenReturn(shippingOrder);
        ShippingOrderDto shippingOrderDto = new ShippingOrderDto();
        shippingOrderDto.setStatus(ShippingOrderStatus.OPEN);
        when(shippingOrderDtoApplicationMapper.domainToDto(any())).thenReturn(shippingOrderDto);

        shippingOrderApplicationService.createOrUpdate(shopifyOrderDto);

        verify(shippingOrderService, times(1)).saveBusinessEvent(any(), anyBoolean());
    }

    @Test
    void when_picking_task_not_found_then_return() {
        // Arrange
        UUID pickingTaskId = UUID.randomUUID();
        when(pickingTaskRepository.findById(pickingTaskId)).thenReturn(null);

        // Act
        shippingOrderApplicationService.updateOrderStatusByPickingTaskId(pickingTaskId);

        // Assert
        verify(shippingOrderRepository, never()).save(any());
        verify(shippingOrderRepository, never()).saveAll(any());
    }

    @Test
    void when_order_level_picking_task_then_update_single_order() {
        // Arrange
        UUID pickingTaskId = UUID.randomUUID();
        UUID orderItemId = UUID.randomUUID();

        PickingTask pickingTask = mock(PickingTask.class);
        PickingTaskItem pickingTaskItem = mock(PickingTaskItem.class);
        ShippingOrder shippingOrder = buildShippingOrdersWithId(1, ShippingOrderStatus.IN_PROGRESS).getFirst();
        ShippingOrderDto shippingOrderDto = ShippingOrderDto.builder().status(ShippingOrderStatus.PICKED).build();

        when(pickingTaskRepository.findById(pickingTaskId)).thenReturn(pickingTask);
        when(pickingTask.getType()).thenReturn(PickingTaskType.ORDER);
        when(pickingTask.getPickingTaskItems()).thenReturn(List.of(pickingTaskItem));
        when(pickingTaskItem.getShippingOrderItemId()).thenReturn(orderItemId);
        when(shippingOrderRepository.findByOrderIds(any())).thenReturn(List.of(shippingOrder));
        when(shippingOrderRepository.findById(any())).thenReturn(shippingOrder);
        when(shippingOrderRepository.update(any())).thenReturn(shippingOrder);
        when(shippingOrderDtoApplicationMapper.domainToDto(shippingOrder)).thenReturn(shippingOrderDto);

        // Act
        shippingOrderApplicationService.updateOrderStatusByPickingTaskId(pickingTaskId);

        // Assert
        verify(shippingOrderService, times(1)).updateSingleOrderWithRetry(any(), anyList(), any());
    }

    @Test
    void when_batch_level_picking_task_then_update_multiple_orders() {
        // Arrange
        UUID pickingTaskId = UUID.randomUUID();

        PickingTask pickingTask = buildPickingTask(UUID.randomUUID(), PickingTaskType.BATCH);
        ShippingOrder shippingOrder = buildShippingOrdersWithId(1, ShippingOrderStatus.IN_PROGRESS).getFirst();
        pickingTask.getPickingTaskItems().forEach(item -> item.setShippingOrderId(shippingOrder.getId()));
        ShippingOrderDto shippingOrderDto = ShippingOrderDto.builder().status(ShippingOrderStatus.PICKED).build();

        when(pickingTaskRepository.findById(pickingTaskId)).thenReturn(pickingTask);
        when(shippingOrderRepository.findByOrderIds(any())).thenReturn(List.of(shippingOrder));
        when(shippingOrderRepository.findById(any())).thenReturn(shippingOrder);
        when(shippingOrderDtoApplicationMapper.domainToDto(shippingOrder)).thenReturn(shippingOrderDto);
        when(shippingOrderRepository.update(any())).thenReturn(shippingOrder);

        // Act
        shippingOrderApplicationService.updateOrderStatusByPickingTaskId(pickingTaskId);

        // Assert
        verify(shippingOrderService, times(1)).updateSingleOrderWithRetry(any(), anyList(), any());
    }

    @Test
    void when_order_level_picking_task_order_not_found_then_return() {
        // Arrange
        UUID pickingTaskId = UUID.randomUUID();

        PickingTask pickingTask = mock(PickingTask.class);
        PickingTaskItem pickingTaskItem = mock(PickingTaskItem.class);

        when(pickingTaskRepository.findById(pickingTaskId)).thenReturn(pickingTask);
        when(pickingTask.getType()).thenReturn(PickingTaskType.ORDER);
        when(pickingTask.getPickingTaskItems()).thenReturn(List.of(pickingTaskItem));
        when(shippingOrderRepository.findByOrderIds(any())).thenReturn(List.of());

        // Act
        shippingOrderApplicationService.updateOrderStatusByPickingTaskId(pickingTaskId);

        // Assert
        verify(shippingOrderRepository, never()).save(any());
        verify(businessEventDispatcher, never()).dispatch(any());
    }

    @Test
    void update_WhenShippingOrderNotFound_ShouldLogErrorAndReturn() {
        // Given
        OutboundScanRecordDto dto = new OutboundScanRecordDto();
        dto.setShippingOrderId(UUID.randomUUID());
        when(shippingOrderRepository.findById(any())).thenReturn(null);

        // When
        shippingOrderApplicationService.update(dto);

        // Then
        verify(shippingOrderRepository, times(0)).save(any());
    }

    @Test
    void update_WhenShippingOrderFound_ShouldUpdateAndSaveEvent() {
        // Given
        OutboundScanRecordDto dto = new OutboundScanRecordDto();
        ShippingOrder shippingOrder = mock(ShippingOrder.class);
        ShippingOrderDto shippingOrderDto = new ShippingOrderDto();

        when(shippingOrderRepository.findById(any())).thenReturn(shippingOrder);
        when(shippingOrderRepository.update(any())).thenReturn(shippingOrder);
        when(shippingOrderDtoApplicationMapper.domainToDto(any())).thenReturn(shippingOrderDto);

        // When
        shippingOrderApplicationService.update(dto);

        // Then
        verify(shippingOrder).picked(dto);
        verify(shippingOrderRepository).update(shippingOrder);
        verify(businessEventDispatcher, times(0)).dispatch(any());
    }

    @Test
    void updateOrderStatusByReceivingTaskId_shouldSuccessfullyUpdateOrders() {
        // Arrange
        UUID receivingTaskId = UUID.randomUUID();
        UUID orderId1 = UUID.randomUUID();
        UUID orderId2 = UUID.randomUUID();

        // Create receiving task with items
        ReceivingTask receivingTask = mock(ReceivingTask.class);
        List<ReceivingTaskItem> receivingTaskItems = Arrays.asList(
            createReceivingTaskItem(orderId1),
            createReceivingTaskItem(orderId1),
            createReceivingTaskItem(orderId2)
        );
        when(receivingTask.getReceivingTaskItems()).thenReturn(receivingTaskItems);

        // Create shipping orders
        ShippingOrder order1 = mock(ShippingOrder.class);
        when(order1.getId()).thenReturn(orderId1);
        when(order1.getOrderNumber()).thenReturn("ORDER-1");
        ShippingOrder order2 = mock(ShippingOrder.class);
        when(order2.getId()).thenReturn(orderId2);
        when(order2.getOrderNumber()).thenReturn("ORDER-2");
        List<ShippingOrder> orders = Arrays.asList(order1, order2);

        // Mock repository responses
        when(receivingTaskRepository.findById(receivingTaskId)).thenReturn(receivingTask);
        when(shippingOrderRepository.findByOrderIds(any())).thenReturn(orders);
        when(shippingOrderRepository.update(any())).thenAnswer(i -> i.getArgument(0));

        // Mock DTO mapping
        ShippingOrderDto orderDto = new ShippingOrderDto();
        when(shippingOrderDtoApplicationMapper.domainToDto(any())).thenReturn(orderDto);

        // Act
        shippingOrderApplicationService.updateOrderStatusByReceivingTaskId(receivingTaskId);

        // Assert
        verify(receivingTaskRepository).findById(receivingTaskId);
        verify(shippingOrderRepository).findByOrderIds(any());
        verify(order1).received(argThat(items -> items.size() == 2)); // Should receive 2 items for order1
        verify(order2).received(argThat(items -> items.size() == 1)); // Should receive 1 item for order2
        verify(shippingOrderRepository, times(2)).update(any());
        verify(shippingOrderDtoApplicationMapper, times(2)).domainToDto(any());
    }

    @Test
    void updateOrderStatusByReceivingTaskId_whenReceivingTaskNotFound_shouldReturnEarly() {
        // Arrange
        UUID receivingTaskId = UUID.randomUUID();
        when(receivingTaskRepository.findById(receivingTaskId)).thenReturn(null);

        // Act
        shippingOrderApplicationService.updateOrderStatusByReceivingTaskId(receivingTaskId);

        // Assert
        verify(receivingTaskRepository).findById(receivingTaskId);
        verify(shippingOrderRepository, never()).findByOrderIds(any());
        verify(shippingOrderRepository, never()).update(any());
        verify(businessEventDispatcher, never()).dispatch(any());
    }

    @Test
    void updateOrderStatusByReceivingTaskId_whenNoItemsInReceivingTask_shouldReturnEarly() {
        // Arrange
        UUID receivingTaskId = UUID.randomUUID();
        ReceivingTask receivingTask = mock(ReceivingTask.class);
        when(receivingTask.getReceivingTaskItems()).thenReturn(Collections.emptyList());
        when(receivingTaskRepository.findById(receivingTaskId)).thenReturn(receivingTask);

        // Act
        shippingOrderApplicationService.updateOrderStatusByReceivingTaskId(receivingTaskId);

        // Assert
        verify(receivingTaskRepository).findById(receivingTaskId);
        verify(shippingOrderRepository, never()).findByOrderIds(any());
        verify(shippingOrderRepository, never()).update(any());
        verify(businessEventDispatcher, never()).dispatch(any());
    }

    @Test
    void updateOrderStatusByReceivingTaskId_whenNoOrdersFound_shouldReturnEarly() {
        // Arrange
        UUID receivingTaskId = UUID.randomUUID();
        ReceivingTask receivingTask = mock(ReceivingTask.class);
        List<ReceivingTaskItem> receivingTaskItems = Collections.singletonList(
            createReceivingTaskItem(UUID.randomUUID())
        );
        when(receivingTask.getReceivingTaskItems()).thenReturn(receivingTaskItems);
        when(receivingTaskRepository.findById(receivingTaskId)).thenReturn(receivingTask);
        when(shippingOrderRepository.findByOrderIds(any())).thenReturn(Collections.emptyList());

        // Act
        shippingOrderApplicationService.updateOrderStatusByReceivingTaskId(receivingTaskId);

        // Assert
        verify(receivingTaskRepository).findById(receivingTaskId);
        verify(shippingOrderRepository).findByOrderIds(any());
        verify(shippingOrderRepository, never()).update(any());
        verify(businessEventDispatcher, never()).dispatch(any());
    }

    private ReceivingTaskItem createReceivingTaskItem(UUID orderId) {
        ReceivingTaskItem item = mock(ReceivingTaskItem.class);
        when(item.getShippingOrderId()).thenReturn(orderId);
        return item;
    }

}