package com.mercaso.wms.interfaces;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.batch.constants.BatchConstants;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.batch.BatchRepository;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.crossdockitem.CrossDockTaskItem;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import com.mercaso.wms.infrastructure.repository.crossdockitem.CrossDockTaskItemRepositoryImpl;
import com.mercaso.wms.infrastructure.repository.pickingtask.PickingTaskRepositoryImpl;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockHttpServletResponse;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import static com.mercaso.wms.utils.MockDataUtils.buildBatch;
import static com.mercaso.wms.utils.MockDataUtils.buildBatchItems;
import static com.mercaso.wms.utils.MockDataUtils.buildPickingTask;
import static org.awaitility.Awaitility.await;

class FailedPickingTaskItemResourceIT extends AbstractIT {

    @Autowired
    private PickingTaskRepositoryImpl pickingTaskRepositoryImpl;

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private FailedPickingTaskItemResource failedPickingTaskItemResource;

    @Autowired
    private CrossDockTaskItemRepositoryImpl crossDockTaskItemRepositoryImpl;

    @Test
    void testFailedItemExport() throws IOException {
        pickingTaskRepositoryImpl.deleteAll();

        String deliveryDate = "2023-10-01";

        Batch batch = buildBatch(UUID.randomUUID());
        batch.setTag(deliveryDate);
        batch = batchRepository.save(batch);

        List<BatchItem> batchItems = buildBatchItems(UUID.randomUUID(), 2);
        Batch finalBatch = batch;
        batchItems.forEach(item -> {
            item.setBatchId(finalBatch.getId());
            item.setDepartment(BatchConstants.TOBACCO_DEPARTMENT);
        });
        batchItems = batchItemRepository.saveAll(batchItems);

        List<PickingTaskItem> pickingTaskItems = new ArrayList<>();
        for (int i = 0; i < batchItems.size(); i++) {
            pickingTaskItems.add(PickingTaskItem.builder()
                .batchItemId(batchItems.get(i).getId())
                .itemId(batchItems.get(i).getItemId())
                .skuNumber(String.valueOf(i + 1))
                .title("title")
                .locationName("locationName")
                .pickingSequence(1)
                .prep(RandomStringUtils.randomAlphabetic(5))
                .errorInfo("errorInfo")
                .build());
        }

        PickingTask pickingTask = PickingTask.builder()
            .batchId(batch.getId())
            .status(PickingTaskStatus.FAILED)
            .source(SourceEnum.MFC)
            .pickingTaskItems(pickingTaskItems)
            .build();

        pickingTaskRepositoryImpl.save(pickingTask);

        MockHttpServletResponse response = new MockHttpServletResponse();
        failedPickingTaskItemResource.failedItemExport(deliveryDate, response);

        Assertions.assertEquals("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8",
            response.getContentType());
        Assertions.assertEquals("attachment;filename=2023-10-01-failed-picking-task-items-export.xlsx",
            response.getHeader("Content-disposition"));

        String filePath = "failed-picking-task-items-export-" + RandomStringUtils.random(6) + ".xlsx";
        try (FileOutputStream fileOutputStream = new FileOutputStream(filePath)) {
            fileOutputStream.write(response.getContentAsByteArray());
        }

        try (FileInputStream fileInputStream = new FileInputStream(filePath);
            org.apache.poi.xssf.usermodel.XSSFWorkbook workbook = new org.apache.poi.xssf.usermodel.XSSFWorkbook(fileInputStream)) {
            Sheet sheet = workbook.getSheetAt(0);
            List<PickingTaskItem> exportedItems = new ArrayList<>();

            for (Row row : sheet) {
                if (row.getRowNum() == 0) {
                    continue;
                }
                exportedItems.add(PickingTaskItem.builder()
                    .skuNumber(row.getCell(0).getStringCellValue())
                    .title(row.getCell(1).getStringCellValue())
                    .locationName(row.getCell(2).getStringCellValue())
                    .prep(row.getCell(3).getStringCellValue())
                    .errorInfo(row.getCell(4).getStringCellValue())
                    .build());
            }

            Assertions.assertEquals(pickingTask.getPickingTaskItems().size(), exportedItems.size());
            pickingTask.getPickingTaskItems().forEach(item ->
                Assertions.assertTrue(exportedItems.stream()
                    .anyMatch(exportedItem -> exportedItem.getSkuNumber().equals(item.getSkuNumber()))));
        }
    }

    @Test
    void testDeletePickingTaskItems() {
        pickingTaskRepositoryImpl.deleteAll();

        Batch batch = buildBatch(UUID.randomUUID());
        batch = batchRepository.save(batch);

        List<BatchItem> batchItems = buildBatchItems(UUID.randomUUID(), 2);
        Batch finalBatch = batch;
        batchItems.forEach(item -> {
            item.setBatchId(finalBatch.getId());
            item.setDepartment(BatchConstants.TOBACCO_DEPARTMENT);
        });
        batchItems = batchItemRepository.saveAll(batchItems);

        List<PickingTaskItem> pickingTaskItems = new ArrayList<>();
        for (int i = 0; i < batchItems.size(); i++) {
            pickingTaskItems.add(PickingTaskItem.builder()
                .batchItemId(batchItems.get(i).getId())
                .itemId(batchItems.get(i).getItemId())
                .skuNumber(String.valueOf(i + 1))
                .title("title")
                .locationName("locationName")
                .pickingSequence(1)
                .prep(RandomStringUtils.randomAlphabetic(5))
                .errorInfo("errorInfo")
                .build());
        }

        PickingTask pickingTask = PickingTask.builder()
            .batchId(batch.getId())
            .status(PickingTaskStatus.FAILED)
            .source(SourceEnum.MFC)
            .pickingTaskItems(pickingTaskItems)
            .build();

        pickingTask = pickingTaskRepositoryImpl.save(pickingTask);

        List<UUID> idsToDelete = new ArrayList<>();
        idsToDelete.add(pickingTask.getPickingTaskItems().getFirst().getId());

        failedPickingTaskItemResource.deletePickingTaskItems(idsToDelete);

        Assertions.assertEquals(1, pickingTaskRepositoryImpl.findById(pickingTask.getId()).getPickingTaskItems().size());
    }

    @Test
    void testDeletePickingTaskItemsAlsoDeletesCrossDockTaskItems() {
        pickingTaskRepositoryImpl.deleteAll();
        crossDockTaskItemRepositoryImpl.deleteAll();

        Batch batch = buildBatch(UUID.randomUUID());
        batch = batchRepository.save(batch);

        List<PickingTask> pickingTasks = buildPickingTask(batch.getId(), 2, SourceEnum.MFC, PickingTaskStatus.FAILED, PickingTaskType.BATCH);
        PickingTask pickingTask = pickingTasks.getFirst();
        pickingTask.getPickingTaskItems().forEach(item -> {
            item.setErrorInfo("errorInfo");
            item.setPrep(org.apache.commons.lang3.RandomStringUtils.randomAlphabetic(5));
        });
        pickingTask = pickingTaskRepositoryImpl.save(pickingTask);

        List<CrossDockTaskItem> crossDockTaskItems = new ArrayList<>();
        for (PickingTaskItem pickingTaskItem : pickingTask.getPickingTaskItems()) {
            crossDockTaskItems.add(CrossDockTaskItem.builder()
                .taskItemId(pickingTaskItem.getId())
                .pickedQty(1)
                .crossDockedQty(1)
                .batchId(batch.getId())
                .itemId(pickingTaskItem.getItemId())
                .skuNumber(pickingTaskItem.getSkuNumber())
                .shippingOrderId(UUID.randomUUID())
                .shippingOrderItemId(UUID.randomUUID())
                .title(pickingTaskItem.getTitle())
                .source(com.mercaso.wms.domain.crossdockitem.enums.CrossDockItemSourceEnum.PICKING_TASK)
                .build());
        }
        crossDockTaskItemRepositoryImpl.saveAll(crossDockTaskItems);

        List<UUID> idsToDelete = new ArrayList<>();
        idsToDelete.add(pickingTask.getPickingTaskItems().getFirst().getId());
        failedPickingTaskItemResource.deletePickingTaskItems(idsToDelete);

        await().atMost(3, TimeUnit.SECONDS)
            .until(() -> crossDockTaskItemRepositoryImpl.findByTaskItemId(idsToDelete.getFirst()).isEmpty());

        UUID remainId = pickingTask.getPickingTaskItems().get(1).getId();
        Assertions.assertFalse(crossDockTaskItemRepositoryImpl.findByTaskItemId(remainId).isEmpty());
    }

}