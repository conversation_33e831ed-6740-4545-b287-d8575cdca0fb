package com.mercaso.wms.interfaces.search;


import static com.mercaso.wms.utils.MockDataUtils.buildBatchItems;
import static com.mercaso.wms.utils.MockDataUtils.buildPickingTask;
import static com.mercaso.wms.utils.MockDataUtils.buildShopifyOrderDto;
import static com.mercaso.wms.utils.MockDataUtils.createBatch;
import static java.util.concurrent.TimeUnit.SECONDS;
import static org.awaitility.Awaitility.await;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.dto.PickingTaskDto;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto;
import com.mercaso.wms.application.query.SortType;
import com.mercaso.wms.batch.config.PickingTaskAssignmentProperties;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import com.mercaso.wms.infrastructure.event.applicationevent.listener.PickingTaskApplicationEventListener;
import com.mercaso.wms.infrastructure.repository.batch.jpa.BatchJpaDao;
import com.mercaso.wms.infrastructure.utils.DateUtils;
import com.mercaso.wms.utils.PickingTaskResourceApi;
import com.mercaso.wms.utils.ShopifyWebhookResourceApi;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class SearchPickingTaskResourceIT extends AbstractIT {

    @Autowired
    PickingTaskResourceApi pickingTaskResourceApi;

    @Autowired
    PickingTaskRepository pickingTaskRepository;

    @Autowired
    ShopifyWebhookResourceApi shopifyWebhookResourceApi;

    @Autowired
    private PickingTaskApplicationEventListener listener;

    @Autowired
    BatchJpaDao batchJpaDao;

    @BeforeEach
    void setUp() {
        PickingTaskAssignmentProperties config = new PickingTaskAssignmentProperties();
        config.setAutoAssignment(false);
        when(pickingTaskAssignmentConfig.getPickingTaskAssignmentConfig()).thenReturn(config);
    }

    @Test
    void searchPickingTasks_withValidParameters_returnsResult() throws Exception {

        pickingTaskRepository.deleteAll();

        Batch saved = batchRepository.save(createBatch());

        List<PickingTask> pickingTasks = buildPickingTask(saved.getId(),
            5,
            SourceEnum.MFC,
            PickingTaskStatus.CREATED,
            PickingTaskType.ORDER);
        List<PickingTask> costcoPickingTasks = buildPickingTask(saved.getId(),
            5,
            SourceEnum.COSTCO,
            PickingTaskStatus.CREATED, PickingTaskType.ORDER);
        pickingTasks.addAll(costcoPickingTasks);

        List<PickingTask> result = pickingTaskRepository.saveAll(pickingTasks);

        Result<PickingTaskDto> pickingTaskDtoResult = pickingTaskResourceApi.searchPickingTasks(
            null,
            null,
            null,
            SourceEnum.MFC,
            new PickingTaskStatus[]{PickingTaskStatus.CREATED},
            DateUtils.getNextDeliveryDate(),
            null,
            null,
            null,
            null, 
            List.of(SortType.CREATED_AT_DESC)
        );

        assertNotNull(pickingTaskDtoResult);
        assertNotNull(pickingTaskDtoResult.getData());
        assertNotNull(pickingTaskDtoResult.getTotalCount());
        assertEquals(5, pickingTaskDtoResult.getData().size());
        assertEquals(5, pickingTaskDtoResult.getTotalCount());

        pickingTaskDtoResult.getData().forEach(pickingTaskDto -> result.forEach(pickingTask -> {
            if (pickingTask.getId().equals(pickingTaskDto.getId())) {
                assertEquals(pickingTask.getId(), pickingTaskDto.getId());
                assertEquals(pickingTask.getSource().name(), pickingTaskDto.getSource());
                assertEquals(pickingTask.getStatus(), pickingTaskDto.getStatus());
                assertEquals(pickingTask.getPickingTaskItems().size(), pickingTaskDto.getPickingTaskItems().size());
            }
        }));
    }

    @Test
    void searchPickingTasks_with_numbers_returnsResult() throws Exception {

        pickingTaskRepository.deleteAll();

        Batch saved = batchRepository.save(createBatch());

        List<PickingTask> pickingTasks = buildPickingTask(saved.getId(),
            5,
            SourceEnum.MFC,
            PickingTaskStatus.CREATED,
            PickingTaskType.ORDER);

        List<PickingTask> result = pickingTaskRepository.saveAll(pickingTasks);

        List<PickingTask> pickingTaskResult = pickingTaskRepository.findByIds(result.stream().map(PickingTask::getId).toList());

        List<String> numbers = pickingTaskResult.stream().map(PickingTask::getNumber).toList().subList(0, 3);

        Result<PickingTaskDto> pickingTaskDtoResult = pickingTaskResourceApi.searchPickingTasks(
            null,
            numbers,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            List.of(SortType.CREATED_AT_DESC)
        );

        assertNotNull(pickingTaskDtoResult);
        assertNotNull(pickingTaskDtoResult.getData());
        assertNotNull(pickingTaskDtoResult.getTotalCount());
        assertEquals(3, pickingTaskDtoResult.getData().size());
        assertEquals(3, pickingTaskDtoResult.getTotalCount());
    }

    @Test
    void when_search_picking_tasks_by_delivery_date_then_returnsResult() throws Exception {
        batchJpaDao.deleteAll();
        pickingTaskRepository.deleteAll();

        Batch saved = batchRepository.save(createBatch());
        List<BatchItem> batchItems = buildBatchItems(saved.getId(), 10);
        batchItemRepository.saveAll(batchItems);

        listener.handleBatchCreatedEvent(saved.getId());
        waitForPickingTask(saved.getId(), 10);

        Result<PickingTaskDto> pickingTaskDtoResult = pickingTaskResourceApi.searchPickingTasks(
            null,
            null,
            null,
            null,
            null,
            LocalDate.parse(saved.getTag()),
            null,
            null,
            null,
            null,
            List.of(SortType.CREATED_AT_DESC)
        );

        assertNotNull(pickingTaskDtoResult);
        assertNotNull(pickingTaskDtoResult.getData());
        assertNotNull(pickingTaskDtoResult.getTotalCount());
        assertEquals(10, pickingTaskDtoResult.getData().size());
    }

    private void waitForPickingTask(UUID batchId, int size) {
        await().atMost(10, SECONDS)
            .until(() -> {
                List<PickingTask> pickingTasks = pickingTaskRepository.findByBatchId(batchId);
                return pickingTasks.size() == size;
            });
    }

    @Test
    void when_search_picking_tasks_by_batch_type_then_returnsResult() throws Exception {
        batchJpaDao.deleteAll();
        pickingTaskRepository.deleteAll();

        Batch saved = batchRepository.save(createBatch());
        List<BatchItem> batchItems = buildBatchItems(saved.getId(), 10);
        batchItemRepository.saveAll(batchItems);

        listener.handleBatchCreatedEvent(saved.getId());
        waitForPickingTask(saved.getId(), 10);

        Result<PickingTaskDto> pickingTaskDtoResult = pickingTaskResourceApi.searchPickingTasks(
            null,
            null,
            null,
            null,
            null,
            null,
            PickingTaskType.BATCH,
            null,
            null,
            null,
            List.of(SortType.CREATED_AT_DESC)
        );

        assertNotNull(pickingTaskDtoResult);
        assertNotNull(pickingTaskDtoResult.getData());
        assertNotNull(pickingTaskDtoResult.getTotalCount());
        assertEquals(0, pickingTaskDtoResult.getData().size());
    }

    @Test
    void when_search_picking_tasks_by_order_type_then_returnsResult() throws Exception {
        batchJpaDao.deleteAll();
        pickingTaskRepository.deleteAll();
        String orderNumber = RandomStringUtils.randomAlphabetic(10);

        ShopifyOrderDto shopifyOrderDto = buildShopifyOrderDto();
        shopifyOrderDto.setName(orderNumber);
        shopifyWebhookResourceApi.webhook(shopifyOrderDto);

        Batch saved = batchRepository.save(createBatch());
        List<BatchItem> batchItems = buildBatchItems(saved.getId(), 1);
        batchItems.forEach(batchItem -> batchItem.setOrderNumber(orderNumber));
        batchItemRepository.saveAll(batchItems);

        listener.handleBatchCreatedEvent(saved.getId());

        Result<PickingTaskDto> pickingTaskDtoResult = pickingTaskResourceApi.searchPickingTasks(
            null,
            null,
            null,
            null,
            null,
            null,
            PickingTaskType.ORDER,
            null,
            null,
            null,
            List.of(SortType.CREATED_AT_DESC)
        );

        assertNotNull(pickingTaskDtoResult);
        assertNotNull(pickingTaskDtoResult.getData());
        assertNotNull(pickingTaskDtoResult.getTotalCount());
        assertEquals(1, pickingTaskDtoResult.getData().size());
        assertEquals(100, pickingTaskDtoResult.getData().getFirst().getOrderQty());
    }

    @Test
    void searchPickingTasks_with_departments_returnsResult() throws Exception {

        pickingTaskRepository.deleteAll();

        Batch saved = batchRepository.save(createBatch());
        List<PickingTask> pickingTasks = buildPickingTask(saved.getId(),
            5,
            SourceEnum.MFC,
            PickingTaskStatus.CREATED,
            PickingTaskType.ORDER);
        PickingTask pickingTask = pickingTasks.getFirst();
        pickingTask.getPickingTaskItems().forEach(pickingTaskItem -> pickingTaskItem.setDepartment("DEPT1"));

        pickingTaskRepository.saveAll(pickingTasks);

        Result<PickingTaskDto> pickingTaskDtoResult = pickingTaskResourceApi.searchPickingTasks(
            null,
            null,
            null,
            SourceEnum.MFC,
            new PickingTaskStatus[]{PickingTaskStatus.CREATED},
            null,
            null,
            List.of("DEPT1"),
            null,
            null,
            List.of(SortType.CREATED_AT_DESC)
        );

        assertNotNull(pickingTaskDtoResult);
        assertNotNull(pickingTaskDtoResult.getData());
        assertNotNull(pickingTaskDtoResult.getTotalCount());
        assertEquals(1, pickingTaskDtoResult.getData().size());
        assertEquals("DEPT1", pickingTaskDtoResult.getData().getFirst().getPickingTaskItems().getFirst().getDepartment());
    }

    @Test
    void searchPickingTasks_with_categories_returnsResult() throws Exception {

        pickingTaskRepository.deleteAll();
        Batch saved = batchRepository.save(createBatch());

        List<PickingTask> pickingTasks = buildPickingTask(saved.getId(),
            5,
            SourceEnum.MFC,
            PickingTaskStatus.CREATED,
            PickingTaskType.ORDER);
        PickingTask pickingTask = pickingTasks.getFirst();
        pickingTask.getPickingTaskItems().forEach(pickingTaskItem -> pickingTaskItem.setCategory("category1"));

        pickingTaskRepository.saveAll(pickingTasks);

        Result<PickingTaskDto> pickingTaskDtoResult = pickingTaskResourceApi.searchPickingTasks(
            null,
            null,
            null,
            SourceEnum.MFC,
            new PickingTaskStatus[]{PickingTaskStatus.CREATED},
            DateUtils.getNextDeliveryDate(),
            null,
            null,
            List.of("category1"),
            null,
            List.of(SortType.CREATED_AT_DESC)
        );

        assertNotNull(pickingTaskDtoResult);
        assertNotNull(pickingTaskDtoResult.getData());
        assertNotNull(pickingTaskDtoResult.getTotalCount());
        assertEquals(1, pickingTaskDtoResult.getData().size());
        assertEquals("category1", pickingTaskDtoResult.getData().getFirst().getPickingTaskItems().getFirst().getCategory());
    }

    @Test
    void searchPickingTasks_with_sort_returnsResult() throws Exception {

        pickingTaskRepository.deleteAll();
        Batch saved = batchRepository.save(createBatch());

        List<PickingTask> pickingTasks = buildPickingTask(saved.getId(),
            5,
            SourceEnum.MFC,
            PickingTaskStatus.CREATED,
            PickingTaskType.ORDER);
        for (int i = 0; i < pickingTasks.size(); i++) {
            PickingTask pickingTask = pickingTasks.get(i);
            List<PickingTaskItem> pickingTaskItems = pickingTask.getPickingTaskItems();
            for (PickingTaskItem pickingTaskItem : pickingTaskItems) {
                pickingTaskItem.setAisleNumber("Aisle_" + i);
            }
        }

        pickingTaskRepository.saveAll(pickingTasks);
        Result<PickingTaskDto> pickingTaskDtoResult = pickingTaskResourceApi.searchPickingTasks(
            null,
            null,
            null,
            SourceEnum.MFC,
            new PickingTaskStatus[]{PickingTaskStatus.CREATED},
            null,
            null,
            null,
            null,
            null,
            List.of(SortType.AISLE_NUMBER_DESC)
        );

        assertNotNull(pickingTaskDtoResult);
        assertNotNull(pickingTaskDtoResult.getData());
        assertNotNull(pickingTaskDtoResult.getTotalCount());
        assertEquals(5, pickingTaskDtoResult.getData().size());
        assertEquals("Aisle_4", pickingTaskDtoResult.getData().getFirst().getPickingTaskItems().getFirst().getAisleNumber());
    }
    
    @Test
    void searchPickingTasks_with_picker_sort_returnsResult() throws Exception {
    
        pickingTaskRepository.deleteAll();
        Batch saved = batchRepository.save(createBatch());

        List<PickingTask> pickingTasks = buildPickingTask(saved.getId(),
            5,
            SourceEnum.MFC,
            PickingTaskStatus.CREATED,
            PickingTaskType.ORDER);
        
        for (int i = 0; i < pickingTasks.size(); i++) {
            PickingTask pickingTask = pickingTasks.get(i);
            pickingTask.setPickerUserName("Picker_" + i);
        }
    
        pickingTaskRepository.saveAll(pickingTasks);
        
        Result<PickingTaskDto> pickingTaskDtoResult = pickingTaskResourceApi.searchPickingTasks(
            null,
            null,
            null,
            SourceEnum.MFC,
            new PickingTaskStatus[]{PickingTaskStatus.CREATED},
            null,
            null,
            null,
            null,
            null,
            List.of(SortType.PICKER_USER_NAME_DESC)
        );
    
        assertNotNull(pickingTaskDtoResult);
        assertNotNull(pickingTaskDtoResult.getData());
        assertNotNull(pickingTaskDtoResult.getTotalCount());
        assertEquals(5, pickingTaskDtoResult.getData().size());
        assertEquals("Picker_4", pickingTaskDtoResult.getData().getFirst().getPickerUserName());
    }
    

    @Test
    void searchPickingTasksV2_withValidParameters_returnsResult() throws Exception {

        pickingTaskRepository.deleteAll();
        Batch saved = batchRepository.save(createBatch());

        List<PickingTask> pickingTasks = buildPickingTask(saved.getId(),
            5,
            SourceEnum.MFC,
            PickingTaskStatus.CREATED,
            PickingTaskType.ORDER);
        List<PickingTask> costcoPickingTasks = buildPickingTask(saved.getId(),
            5,
            SourceEnum.COSTCO,
            PickingTaskStatus.CREATED, PickingTaskType.ORDER);
        pickingTasks.addAll(costcoPickingTasks);

        List<PickingTask> result = pickingTaskRepository.saveAll(pickingTasks);

        Result<PickingTaskDto> pickingTaskDtoResult = pickingTaskResourceApi.searchPickingTasksV2(
            saved.getNumber(),
            null,
            null,
            SourceEnum.MFC,
            new PickingTaskStatus[]{PickingTaskStatus.CREATED},
            null,
            null,
            null,
            null,
            List.of(SortType.BREAKDOWN_NAME_DESC)
        );

        assertNotNull(pickingTaskDtoResult);
        assertNotNull(pickingTaskDtoResult.getData());
        assertNotNull(pickingTaskDtoResult.getTotalCount());
        assertEquals(5, pickingTaskDtoResult.getData().size());
        assertEquals(5, pickingTaskDtoResult.getTotalCount());

        pickingTaskDtoResult.getData().forEach(pickingTaskDto -> result.forEach(pickingTask -> {
            if (pickingTask.getId().equals(pickingTaskDto.getId())) {
                assertEquals(pickingTask.getId(), pickingTaskDto.getId());
                assertEquals(pickingTask.getSource().name(), pickingTaskDto.getSource());
                assertEquals(pickingTask.getStatus(), pickingTaskDto.getStatus());
            }
        }));
    }

    @Test
    void searchPickingTasks_with_skuNumbers_returnsResult() throws Exception {

        pickingTaskRepository.deleteAll();
        Batch saved = batchRepository.save(createBatch());

        List<PickingTask> pickingTasks = buildPickingTask(saved.getId(),
            5,
            SourceEnum.MFC,
            PickingTaskStatus.CREATED,
            PickingTaskType.ORDER);
        PickingTask pickingTask = pickingTasks.getFirst();
    
        List<String> skuNumbers = List.of("DW30960-6", "CO30960-6");
        pickingTask.getPickingTaskItems().forEach(pickingTaskItem -> pickingTaskItem.setSkuNumber(skuNumbers.getFirst()));
    
        pickingTaskRepository.saveAll(pickingTasks);
    
        Result<PickingTaskDto> pickingTaskDtoResult = pickingTaskResourceApi.searchPickingTasks(
            null,
            null,
            null,
            SourceEnum.MFC,
            new PickingTaskStatus[]{PickingTaskStatus.CREATED},
            null,
            null,
            null,
            null,
            skuNumbers, 
            List.of(SortType.CREATED_AT_DESC)
        );
    
        assertNotNull(pickingTaskDtoResult);
        assertNotNull(pickingTaskDtoResult.getData());
        assertNotNull(pickingTaskDtoResult.getTotalCount());
        assertEquals(1, pickingTaskDtoResult.getData().size());
        assertEquals(skuNumbers.getFirst(),
            pickingTaskDtoResult.getData().getFirst().getPickingTaskItems().getFirst().getSkuNumber());
    }
    
}