package com.mercaso.wms.interfaces.search;

import static com.mercaso.wms.utils.MockDataUtils.buildCrossTaskItems;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.dto.view.SearchCrossDockTaskItemView;
import com.mercaso.wms.domain.crossdockitem.CrossDockTaskItem;
import com.mercaso.wms.domain.crossdockitem.CrossDockTaskItemRepository;
import com.mercaso.wms.utils.CrossDockTaskResourceApi;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class SearchCrossDockItemResourceIT extends AbstractIT {

    @Autowired
    private CrossDockTaskResourceApi crossDockTaskResourceApi;

    @Autowired
    private CrossDockTaskItemRepository crossDockTaskItemRepository;

    @BeforeEach
    void setUp() {
        crossDockTaskItemRepository.deleteAll();
    }

    @Test
    void searchCrossDockTaskItems_withValidParameters_returnsResult() throws Exception {
        List<CrossDockTaskItem> items = buildCrossTaskItems(5);
        UUID sameTaskItemId = items.get(0).getTaskItemId();
        items.get(1).setTaskItemId(sameTaskItemId);
        items.forEach(crossDockTaskItemRepository::save);

        Result<SearchCrossDockTaskItemView> result = crossDockTaskResourceApi.searchCrossDockTaskItems(
            null, null, null, null, null
        );

        assertNotNull(result);
        assertNotNull(result.getData());
        assertNotNull(result.getTotalCount());
        long uniqueTaskItemCount = items.stream().map(CrossDockTaskItem::getTaskItemId).distinct().count();
        assertEquals(uniqueTaskItemCount, result.getData().size());
        assertEquals(uniqueTaskItemCount, result.getTotalCount());
    }

    @Test
    void searchCrossDockTaskItems_filterByStatus_crossed() throws Exception {
        List<CrossDockTaskItem> items = buildCrossTaskItems(3);
        items.get(0).setCrossDockedQty(1);
        items.get(1).setCrossDockedQty(0);
        items.get(2).setCrossDockedQty(null);
        items.forEach(crossDockTaskItemRepository::save);

        Result<SearchCrossDockTaskItemView> result = crossDockTaskResourceApi.searchCrossDockTaskItems(
            null, null, null, null, "CROSSED"
        );
        assertNotNull(result);
        assertNotNull(result.getData());
        assertTrue(result.getData().stream().allMatch(i -> i.getCrossDockedQty() != null && i.getCrossDockedQty() > 0));
    }

    @Test
    void searchCrossDockTaskItems_filterByStatus_notCrossed() throws Exception {
        List<CrossDockTaskItem> items = buildCrossTaskItems(3);
        items.get(0).setCrossDockedQty(0);
        items.get(1).setCrossDockedQty(null);
        items.get(2).setCrossDockedQty(1);
        items.forEach(crossDockTaskItemRepository::save);

        Result<SearchCrossDockTaskItemView> result = crossDockTaskResourceApi.searchCrossDockTaskItems(
            null, null, null, null, "NOT_CROSSED"
        );
        assertNotNull(result);
        assertNotNull(result.getData());
        assertTrue(result.getData().stream().allMatch(i -> i.getCrossDockedQty() == null || i.getCrossDockedQty() == 0));
    }

    @Test
    void searchCrossDockTaskItems_aggregationWithStatusFilter() throws Exception {
        List<CrossDockTaskItem> items = buildCrossTaskItems(3);
        UUID sameTaskItemId = items.getFirst().getTaskItemId();
        items.forEach(i -> i.setTaskItemId(sameTaskItemId));
        items.get(0).setCrossDockedQty(0);
        items.get(1).setCrossDockedQty(1);
        items.get(1).setSequence(List.of("2/3"));
        items.get(2).setCrossDockedQty(1);
        items.get(2).setSequence(List.of("3/3"));
        items.forEach(crossDockTaskItemRepository::save);

        Result<SearchCrossDockTaskItemView> crossedResult = crossDockTaskResourceApi.searchCrossDockTaskItems(
            null, null, null, null, "CROSSED"
        );
        assertNotNull(crossedResult);
        assertNotNull(crossedResult.getData());
        assertTrue(crossedResult.getData().stream().anyMatch(i -> i.getTaskItemId().equals(sameTaskItemId)));

        Result<SearchCrossDockTaskItemView> notCrossedResult = crossDockTaskResourceApi.searchCrossDockTaskItems(
            null, null, null, null, "NOT_CROSSED"
        );
        assertNotNull(notCrossedResult);
        assertNotNull(notCrossedResult.getData());
        assertTrue(notCrossedResult.getData().stream().noneMatch(i -> i.getTaskItemId().equals(sameTaskItemId)));

        List<CrossDockTaskItem> zeroItems = buildCrossTaskItems(2);
        UUID zeroTaskItemId = zeroItems.getFirst().getTaskItemId();
        zeroItems.forEach(i -> i.setTaskItemId(zeroTaskItemId));
        zeroItems.forEach(i -> i.setCrossDockedQty(0));
        zeroItems.forEach(crossDockTaskItemRepository::save);

        Result<SearchCrossDockTaskItemView> notCrossedResult2 = crossDockTaskResourceApi.searchCrossDockTaskItems(
            null, null, null, null, "NOT_CROSSED"
        );
        assertNotNull(notCrossedResult2);
        assertNotNull(notCrossedResult2.getData());
        assertTrue(notCrossedResult2.getData().stream().anyMatch(i -> i.getTaskItemId().equals(zeroTaskItemId)));
    }
}
