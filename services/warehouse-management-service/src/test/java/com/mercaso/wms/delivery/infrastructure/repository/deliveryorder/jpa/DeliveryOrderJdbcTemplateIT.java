package com.mercaso.wms.delivery.infrastructure.repository.deliveryorder.jpa;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.fail;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.delivery.application.dto.view.SearchPaymentSummaryView;
import com.mercaso.wms.delivery.application.query.PaymentSummaryQuery;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.DeliveryOrderStatus;
import com.mercaso.wms.delivery.infrastructure.repository.account.jpa.AccountJpaDao;
import com.mercaso.wms.delivery.infrastructure.repository.account.jpa.dataobject.AccountDo;
import com.mercaso.wms.delivery.infrastructure.repository.deliveryorder.jpa.dataobject.DeliveryOrderDo;
import com.mercaso.wms.delivery.infrastructure.repository.deliveryorder.jpa.dataobject.DeliveryOrderItemDo;
import com.mercaso.wms.delivery.infrastructure.repository.deliverytask.jpa.DeliveryTaskJpaDao;
import com.mercaso.wms.delivery.infrastructure.repository.deliverytask.jpa.dataobject.DeliveryTaskDo;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

class DeliveryOrderJdbcTemplateIT extends AbstractIT {

    @Autowired
    private DeliveryOrderJdbcTemplate deliveryOrderJdbcTemplate;

    @Autowired
    private DeliveryOrderJpaDao deliveryOrderJpaDao;

    @Autowired
    private DeliveryTaskJpaDao deliveryTaskJpaDao;

    @Autowired
    private AccountJpaDao accountJpaDao;

    private UUID driverId;
    private UUID deliveryTaskId;
    private UUID deliveryOrderId;
    private String deliveryDate;
    private String orderNumber;

    @BeforeEach
    void setUp() {
        deliveryOrderJpaDao.deleteAll();
        deliveryTaskJpaDao.deleteAll();
        accountJpaDao.deleteAll();

        // Create test data
        driverId = UUID.randomUUID();
        String driverName = "Test Driver";

        // Create account
        AccountDo accountDo = new AccountDo();
        accountDo.setId(UUID.randomUUID());
        accountDo.setUserId(driverId);
        accountDo.setUserName(driverName);
        accountDo.setWarehouseId(UUID.randomUUID());
        accountDo.setEmail("<EMAIL>");
        // Set audit fields
        Instant now = Instant.now();
        accountDo.setCreatedAt(now);
        accountDo.setUpdatedAt(now);
        accountJpaDao.save(accountDo);

        // Create delivery task
        DeliveryTaskDo deliveryTaskDo = new DeliveryTaskDo();
        deliveryTaskId = UUID.randomUUID();
        deliveryTaskDo.setId(deliveryTaskId);
        deliveryTaskDo.setNumber("DT" + RandomStringUtils.randomNumeric(6));
        deliveryTaskDo.setDriverUserId(driverId);
        deliveryTaskDo.setDriverUserName(driverName);
        deliveryTaskDo.setTruckNumber("TRUCK-001");
        deliveryDate = LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
        deliveryTaskDo.setDeliveryDate(deliveryDate);
        // Set audit fields
        deliveryTaskDo.setCreatedAt(now);
        deliveryTaskDo.setUpdatedAt(now);
        deliveryTaskJpaDao.save(deliveryTaskDo);

        // Create delivery order
        DeliveryOrderDo deliveryOrderDo = new DeliveryOrderDo();
        deliveryOrderId = UUID.randomUUID();
        deliveryOrderDo.setId(deliveryOrderId);
        orderNumber = "ORDER-" + RandomStringUtils.randomNumeric(6);
        deliveryOrderDo.setOrderNumber(orderNumber);
        deliveryOrderDo.setDeliveryTaskId(deliveryTaskId);
        deliveryOrderDo.setStatus(DeliveryOrderStatus.CREATED);
        deliveryOrderDo.setDeliveryDate(deliveryDate);
        deliveryOrderDo.setPaymentStatus("PAID");
        deliveryOrderDo.setTotalPrice(BigDecimal.valueOf(100.00));
        deliveryOrderDo.setOriginalTotalPrice(BigDecimal.valueOf(120.00));
        deliveryOrderDo.setWarehouseId(UUID.randomUUID());
        // Set audit fields
        deliveryOrderDo.setCreatedAt(now);
        deliveryOrderDo.setUpdatedAt(now);

        // Create order items
        DeliveryOrderItemDo orderItemDo1 = new DeliveryOrderItemDo();
        orderItemDo1.setId(UUID.randomUUID());
        orderItemDo1.setDeliveryOrder(deliveryOrderDo);
        orderItemDo1.setQty(BigDecimal.valueOf(5));
        orderItemDo1.setDeliveredQty(BigDecimal.valueOf(5));
        orderItemDo1.setLine(1);
        orderItemDo1.setTitle("Test Item 1");
        orderItemDo1.setReasonCode("{\"RETURNS\":1,\"DAMAGED\":1}"); // Contains RETURNS and DAMAGED for testing returns flag
        // Set audit fields
        orderItemDo1.setCreatedAt(now);
        orderItemDo1.setUpdatedAt(now);

        DeliveryOrderItemDo orderItemDo2 = new DeliveryOrderItemDo();
        orderItemDo2.setId(UUID.randomUUID());
        orderItemDo2.setDeliveryOrder(deliveryOrderDo);
        orderItemDo2.setQty(BigDecimal.valueOf(10));
        orderItemDo2.setDeliveredQty(BigDecimal.valueOf(10));
        orderItemDo2.setLine(2);
        orderItemDo2.setTitle("Test Item 2");
        orderItemDo2.setReasonCode("{\"MISSING\":2}"); // Contains MISSING for testing shortShips flag
        // Set audit fields
        orderItemDo2.setCreatedAt(now);
        orderItemDo2.setUpdatedAt(now);

        deliveryOrderDo.setDeliveryOrderItems(List.of(orderItemDo1, orderItemDo2));
        deliveryOrderJpaDao.save(deliveryOrderDo);
    }

    @Test
    void when_search_payment_summary_then_return_payment_summary_view() {
        // Build query
        PaymentSummaryQuery query = PaymentSummaryQuery.builder()
            .deliveryDate(deliveryDate)
            .paymentStatus(Collections.singletonList("PAID"))
            .issueOrder(true)
            .page(1)
            .pageSize(10)
            .build();

        // Query actual order ID from database
        List<DeliveryOrderDo> allOrders = deliveryOrderJpaDao.findAll();
        if (allOrders.isEmpty()) {
            fail("Test data preparation failed, no order records in database");
        }

        // Get the first order from test data
        DeliveryOrderDo actualOrder = allOrders.get(0);
        UUID actualOrderId = actualOrder.getId();
        System.out.println("Actual order ID in database: " + actualOrderId);
        System.out.println("Expected order ID in test: " + deliveryOrderId);

        // Execute query
        Page<SearchPaymentSummaryView> result = deliveryOrderJdbcTemplate.searchPaymentSummary(query, PageRequest.of(0, 10));

        // Assert results
        assertNotNull(result);
        assertEquals(1, result.getContent().size(), "Should return one record");

        SearchPaymentSummaryView view = result.getContent().get(0);

        // Assert with actual order ID from database
        assertEquals(actualOrderId, view.getOrderId(), "Order ID should match with database");

        // Other assertions
        assertEquals(actualOrder.getOrderNumber(), view.getOrderNumber());
        assertEquals(deliveryDate, view.getDeliveryDate());

        assertEquals(0, BigDecimal.valueOf(120.00).compareTo(view.getOriginalOrderPrice()));
        assertEquals(0, BigDecimal.valueOf(100.00).compareTo(view.getFinalInvoicePaidPrice()));

        assertEquals("Y", view.getReturns(), "Should detect returns");
        assertEquals("Y", view.getShortShips(), "Should detect short ships");
    }

    @Test
    void when_search_payment_summary_with_wrong_delivery_date_then_return_empty() {
        // Build query with non-existent delivery date
        PaymentSummaryQuery query = PaymentSummaryQuery.builder()
            .deliveryDate("2099-01-01")
            .page(1)
            .pageSize(10)
            .build();

        // Execute query
        Page<SearchPaymentSummaryView> result = deliveryOrderJdbcTemplate.searchPaymentSummary(query, PageRequest.of(0, 10));

        // Assert results
        assertNotNull(result);
        assertEquals(0, result.getContent().size());
    }

    @Test
    void when_search_payment_summary_with_driver_name_sort_then_return_sorted_results() {
        // Setup additional test data with different driver names
        createAdditionalTestDataWithDifferentDrivers();

        // Build query with driver name sorting (ascending)
        PaymentSummaryQuery query = PaymentSummaryQuery.builder()
            .deliveryDate(deliveryDate)
            .page(1)
            .pageSize(10)
            .build();

        // Execute query with driver name ascending sort
        PageRequest pageRequest = PageRequest.of(0, 10, org.springframework.data.domain.Sort.by(
            org.springframework.data.domain.Sort.Direction.ASC, "user_name"));
        Page<SearchPaymentSummaryView> result = deliveryOrderJdbcTemplate.searchPaymentSummary(query, pageRequest);

        // Assert results
        assertNotNull(result);
        assertEquals(2, result.getContent().size(), "Should return two records");
    }

    private void createAdditionalTestDataWithDifferentDrivers() {
        UUID secondDriverId = UUID.randomUUID();
        String secondDriverName = "Alpha Driver"; // This should come first in ascending order

        // Create second account
        AccountDo secondAccountDo = new AccountDo();
        secondAccountDo.setId(UUID.randomUUID());
        secondAccountDo.setUserId(secondDriverId);
        secondAccountDo.setUserName(secondDriverName);
        secondAccountDo.setWarehouseId(UUID.randomUUID());
        secondAccountDo.setEmail("<EMAIL>");
        Instant now = Instant.now();
        secondAccountDo.setCreatedAt(now);
        secondAccountDo.setUpdatedAt(now);
        accountJpaDao.save(secondAccountDo);

        // Create second delivery task
        DeliveryTaskDo secondDeliveryTaskDo = new DeliveryTaskDo();
        UUID secondDeliveryTaskId = UUID.randomUUID();
        secondDeliveryTaskDo.setId(secondDeliveryTaskId);
        secondDeliveryTaskDo.setNumber("DT" + RandomStringUtils.randomNumeric(6));
        secondDeliveryTaskDo.setDriverUserId(secondDriverId);
        secondDeliveryTaskDo.setDriverUserName(secondDriverName);
        secondDeliveryTaskDo.setTruckNumber("TRUCK-002");
        secondDeliveryTaskDo.setDeliveryDate(deliveryDate);
        secondDeliveryTaskDo.setCreatedAt(now);
        secondDeliveryTaskDo.setUpdatedAt(now);
        deliveryTaskJpaDao.save(secondDeliveryTaskDo);

        // Create second delivery order
        DeliveryOrderDo secondDeliveryOrderDo = new DeliveryOrderDo();
        UUID secondDeliveryOrderId = UUID.randomUUID();
        secondDeliveryOrderDo.setId(secondDeliveryOrderId);
        String secondOrderNumber = "ORDER-" + RandomStringUtils.randomNumeric(6);
        secondDeliveryOrderDo.setOrderNumber(secondOrderNumber);
        secondDeliveryOrderDo.setDeliveryTaskId(secondDeliveryTaskId);
        secondDeliveryOrderDo.setStatus(DeliveryOrderStatus.CREATED);
        secondDeliveryOrderDo.setDeliveryDate(deliveryDate);
        secondDeliveryOrderDo.setPaymentStatus("PAID");
        secondDeliveryOrderDo.setTotalPrice(BigDecimal.valueOf(150.00));
        secondDeliveryOrderDo.setOriginalTotalPrice(BigDecimal.valueOf(180.00));
        secondDeliveryOrderDo.setWarehouseId(UUID.randomUUID());
        secondDeliveryOrderDo.setCreatedAt(now);
        secondDeliveryOrderDo.setUpdatedAt(now);

        // Create order items for second order
        DeliveryOrderItemDo secondOrderItemDo = new DeliveryOrderItemDo();
        secondOrderItemDo.setId(UUID.randomUUID());
        secondOrderItemDo.setDeliveryOrder(secondDeliveryOrderDo);
        secondOrderItemDo.setQty(BigDecimal.valueOf(3));
        secondOrderItemDo.setDeliveredQty(BigDecimal.valueOf(3));
        secondOrderItemDo.setLine(1);
        secondOrderItemDo.setTitle("Second Test Item");
        secondOrderItemDo.setReasonCode("{}");
        secondOrderItemDo.setCreatedAt(now);
        secondOrderItemDo.setUpdatedAt(now);

        secondDeliveryOrderDo.setDeliveryOrderItems(List.of(secondOrderItemDo));
        deliveryOrderJpaDao.save(secondDeliveryOrderDo);
    }
} 