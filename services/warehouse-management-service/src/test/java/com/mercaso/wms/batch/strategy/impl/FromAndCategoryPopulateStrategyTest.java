package com.mercaso.wms.batch.strategy.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mercaso.wms.batch.constants.BatchConstants;
import com.mercaso.wms.batch.dto.ExcelBatchDto;
import com.mercaso.wms.batch.dto.LookupDto;
import com.mercaso.wms.batch.dto.PopulateCondition;
import com.mercaso.wms.batch.dto.StockDto;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.batch.enums.UploadDocNameEnum;
import com.mercaso.wms.builder.DataBuilder;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.location.enums.LocationType;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;

class FromAndCategoryPopulateStrategyTest {

    private final FromAndCategoryPopulateStrategy strategy = new FromAndCategoryPopulateStrategy();


    @Test
    void populateBatchTemplate_successful() {
        PopulateCondition populateCondition = DataBuilder.buildPopulateCondition(5);

        List<ExcelBatchDto> result = strategy.populateBatchTemplate(populateCondition);

        assertNotNull(result);
        assertEquals(5, result.size());
        assertEquals("item0", result.getFirst().getItemNumber());
        assertEquals("order0", result.getFirst().getOrderNumber());
        assertEquals(1, result.getFirst().getQuantity());

    }

    @Test
    void populateBatchTemplate_withEmptyBatchDtoList_returnsEmptyList() {
        PopulateCondition populateCondition = DataBuilder.buildPopulateCondition(0);

        List<ExcelBatchDto> result = strategy.populateBatchTemplate(populateCondition);

        assertTrue(result.isEmpty());
    }

    @Test
    void populateBatchTemplate_withQuantitiesGreaterThanAvailableStock_returnsModifiedList() {
        ExcelBatchDto excelBatchDto = DataBuilder.buildBatchDto("item1", "order1", 15);
        List<StockDto> stockDtos = DataBuilder.buildStockDtoList(1);
        PopulateCondition populateCondition = PopulateCondition.builder()
            .excelBatchDtoList(List.of(excelBatchDto))
            .lookUpData(new EnumMap<>(UploadDocNameEnum.class))
            .mfcStocks(stockDtos)
            .locations(new ArrayList<>())
            .build();

        List<ExcelBatchDto> result = strategy.populateBatchTemplate(populateCondition);

        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
    }

    @Test
    void populateBatchTemplate_withQuantitiesEqualToAvailableStock_returnsPopulatedList() {
        ExcelBatchDto excelBatchDto = DataBuilder.buildBatchDto("item1", "order1", 10);
        List<StockDto> stockDtos = DataBuilder.buildStockDtoList(1);
        PopulateCondition populateCondition = PopulateCondition.builder()
            .excelBatchDtoList(List.of(excelBatchDto))
            .lookUpData(new EnumMap<>(UploadDocNameEnum.class))
            .mfcStocks(stockDtos)
            .locations(new ArrayList<>())
            .build();

        List<ExcelBatchDto> result = strategy.populateBatchTemplate(populateCondition);

        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
    }

    @Test
    void populateBatchTemplate_splitting_out_new_batch_when_MFC_stock_running_low() {
        ExcelBatchDto excelBatchDto = DataBuilder.buildBatchDto("item1", "order1", 10);
        List<StockDto> stockDtos = DataBuilder.buildStockDtoList(1);
        StockDto stockDto = stockDtos.getFirst();
        stockDto.setTotalQty(5);
        LookupDto lookupDto = DataBuilder.buildLookupData("item1", "order1", UploadDocNameEnum.COSTCO);
        Location location = DataBuilder.buildLocation("location1", LocationType.BIN);

        List<ExcelBatchDto> excelBatchDtoList = new ArrayList<>();
        excelBatchDtoList.add(excelBatchDto);
        List<LookupDto> lookupDtoList = new ArrayList<>();
        lookupDtoList.add(lookupDto);
        Map<UploadDocNameEnum, List<LookupDto>> lookUpData = new EnumMap<>(UploadDocNameEnum.class);
        lookUpData.put(UploadDocNameEnum.COSTCO, lookupDtoList);
        List<Location> locations = new ArrayList<>();
        locations.add(location);

        PopulateCondition populateCondition = PopulateCondition.builder()
            .excelBatchDtoList(excelBatchDtoList)
            .lookUpData(lookUpData)
            .mfcStocks(stockDtos)
            .locations(locations)
            .build();

        List<ExcelBatchDto> result = strategy.populateBatchTemplate(populateCondition);

        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        assertEquals(10, result.getFirst().getQuantity());

    }

    @Test
    void populateBatchTemplate_splitting_out_new_batch_when_pulling_bin_stock_running_low() {
        ExcelBatchDto excelBatchDto = DataBuilder.buildBatchDto("item1", "order1", 10);
        StockDto stockDto = DataBuilder.buildStockDto("item1", "location1", 3, LocationType.BIN);
        StockDto stockDto1 = DataBuilder.buildStockDto("item1", "location2", 2, LocationType.STOCK);
        StockDto stockDto2 = DataBuilder.buildStockDto("item2", "location1", 5, LocationType.BIN);

        LookupDto lookupDto = DataBuilder.buildLookupData("item1", "order1", UploadDocNameEnum.COSTCO);

        List<ExcelBatchDto> excelBatchDtoList = new ArrayList<>();
        excelBatchDtoList.add(excelBatchDto);
        List<LookupDto> lookupDtoList = new ArrayList<>();
        lookupDtoList.add(lookupDto);
        Map<UploadDocNameEnum, List<LookupDto>> lookUpData = new EnumMap<>(UploadDocNameEnum.class);
        lookUpData.put(UploadDocNameEnum.COSTCO, lookupDtoList);
        List<Location> locations = new ArrayList<>();
        locations.add(DataBuilder.buildLocation("location1", LocationType.BIN));
        locations.add(DataBuilder.buildLocation("location2", LocationType.STOCK));

        PopulateCondition populateCondition = PopulateCondition.builder()
            .excelBatchDtoList(excelBatchDtoList)
            .lookUpData(lookUpData)
            .locations(locations)
            .mfcStocks(Arrays.asList(stockDto, stockDto1, stockDto2))
            .build();

        List<ExcelBatchDto> result = strategy.populateBatchTemplate(populateCondition);

        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        assertEquals(10, result.getFirst().getQuantity());
    }

    @Test
    void when_populate_batch_template_splitting_out_new_batch_when_use_special_bin() {
        ExcelBatchDto excelBatchDto = DataBuilder.buildBatchDto("item1", "order1", 3);
        StockDto stockDto = DataBuilder.buildStockDto("item1", ".COOLER", 3, LocationType.SPECIAL_BIN);
        StockDto stockDto1 = DataBuilder.buildStockDto("item1", "location2", 2, LocationType.STOCK);

        LookupDto lookupDto = DataBuilder.buildLookupData("item1", "order1", UploadDocNameEnum.COSTCO);

        List<ExcelBatchDto> excelBatchDtoList = new ArrayList<>();
        excelBatchDtoList.add(excelBatchDto);
        List<LookupDto> lookupDtoList = new ArrayList<>();
        lookupDtoList.add(lookupDto);
        Map<UploadDocNameEnum, List<LookupDto>> lookUpData = new EnumMap<>(UploadDocNameEnum.class);
        lookUpData.put(UploadDocNameEnum.COSTCO, lookupDtoList);

        PopulateCondition populateCondition = PopulateCondition.builder()
            .excelBatchDtoList(excelBatchDtoList)
            .lookUpData(lookUpData)
            .mfcStocks(List.of(stockDto, stockDto1))
            .build();

        List<ExcelBatchDto> result = strategy.populateBatchTemplate(populateCondition);

        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        assertEquals(3, result.getFirst().getQuantity());
        assertEquals(".COOLER", result.getFirst().getFrom());
    }

    @Test
    void when_populate_batch_template_splitting_out_new_batch_when_use_room_bin() {
        ExcelBatchDto excelBatchDto = DataBuilder.buildBatchDto("item1", "order1", 3);
        StockDto stockDto = DataBuilder.buildStockDto("item1", "ROOM", 3, LocationType.ROOM);
        StockDto stockDto1 = DataBuilder.buildStockDto("item1", "location2", 2, LocationType.STOCK);

        LookupDto lookupDto = DataBuilder.buildLookupData("item1", "order1", UploadDocNameEnum.COSTCO);

        List<ExcelBatchDto> excelBatchDtoList = new ArrayList<>();
        excelBatchDtoList.add(excelBatchDto);
        List<LookupDto> lookupDtoList = new ArrayList<>();
        lookupDtoList.add(lookupDto);
        Map<UploadDocNameEnum, List<LookupDto>> lookUpData = new EnumMap<>(UploadDocNameEnum.class);
        lookUpData.put(UploadDocNameEnum.COSTCO, lookupDtoList);

        PopulateCondition populateCondition = PopulateCondition.builder()
            .excelBatchDtoList(excelBatchDtoList)
            .lookUpData(lookUpData)
            .mfcStocks(List.of(stockDto, stockDto1))
            .build();

        List<ExcelBatchDto> result = strategy.populateBatchTemplate(populateCondition);

        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        assertEquals(3, result.getFirst().getQuantity());
        assertEquals("ROOM", result.getFirst().getFrom());
    }

    @Test
    void when_populate_batch_template_splitting_out_new_batch_when_use_rd_bin() {
        ExcelBatchDto excelBatchDto = DataBuilder.buildBatchDto("item1", "order1", 3);
        excelBatchDto.setFrom(null);
        ExcelBatchDto excelBatchDto1 = DataBuilder.buildBatchDto("item1", "order2", 10);
        excelBatchDto1.setFrom(null);
        ExcelBatchDto excelBatchDto2 = DataBuilder.buildBatchDto("item1", "order3", 10);
        excelBatchDto2.setFrom(null);
        StockDto stockDto = DataBuilder.buildStockDto("item1", "ROOM", 3, LocationType.ROOM);
        StockDto stockDto1 = DataBuilder.buildStockDto("item1", "01-01-A-1", 2, LocationType.STOCK);
        StockDto stockDto2 = DataBuilder.buildStockDto("item1", ".RD", 19, LocationType.RD);

        LookupDto lookupDto = DataBuilder.buildLookupData("item1", "order1", UploadDocNameEnum.COSTCO);

        List<ExcelBatchDto> excelBatchDtoList = new ArrayList<>();
        excelBatchDtoList.add(excelBatchDto);
        excelBatchDtoList.add(excelBatchDto1);
        excelBatchDtoList.add(excelBatchDto2);
        List<LookupDto> lookupDtoList = new ArrayList<>();
        lookupDtoList.add(lookupDto);
        Map<UploadDocNameEnum, List<LookupDto>> lookUpData = new EnumMap<>(UploadDocNameEnum.class);
        lookUpData.put(UploadDocNameEnum.COSTCO, lookupDtoList);

        PopulateCondition populateCondition = PopulateCondition.builder()
            .excelBatchDtoList(excelBatchDtoList)
            .lookUpData(lookUpData)
            .mfcStocks(List.of(stockDto, stockDto1, stockDto2))
            .build();

        List<ExcelBatchDto> result = strategy.populateBatchTemplate(populateCondition);

        assertFalse(result.isEmpty());
        assertEquals(3, result.size());
        assertEquals(3, result.getFirst().getQuantity());
        assertEquals("ROOM", result.getFirst().getFrom());

        assertEquals(10, result.get(1).getQuantity());
        assertEquals(".RD", result.get(1).getFrom());

        assertEquals(10, result.getLast().getQuantity());
        assertEquals(lookupDto.getAisle(), result.getLast().getFrom());
    }

    @Test
    void when_populate_batch_template_splitting_out_new_batch_when_use_bin() {
        ExcelBatchDto excelBatchDto = DataBuilder.buildBatchDto("item1", "order1", 3);
        excelBatchDto.setFrom(null);
        ExcelBatchDto excelBatchDto1 = DataBuilder.buildBatchDto("item1", "order2", 10);
        excelBatchDto1.setFrom(null);
        ExcelBatchDto excelBatchDto2 = DataBuilder.buildBatchDto("item1", "order3", 10);
        excelBatchDto2.setFrom(null);
        StockDto stockDto = DataBuilder.buildStockDto("item1", "ROOM", 3, LocationType.ROOM);
        StockDto stockDto1 = DataBuilder.buildStockDto("item1", "01-01-A-1", 20, LocationType.STOCK);

        LookupDto lookupDto = DataBuilder.buildLookupData("item1", "order1", UploadDocNameEnum.COSTCO);

        List<ExcelBatchDto> excelBatchDtoList = new ArrayList<>();
        excelBatchDtoList.add(excelBatchDto);
        excelBatchDtoList.add(excelBatchDto1);
        excelBatchDtoList.add(excelBatchDto2);
        List<LookupDto> lookupDtoList = new ArrayList<>();
        lookupDtoList.add(lookupDto);
        Map<UploadDocNameEnum, List<LookupDto>> lookUpData = new EnumMap<>(UploadDocNameEnum.class);
        lookUpData.put(UploadDocNameEnum.COSTCO, lookupDtoList);

        PopulateCondition populateCondition = PopulateCondition.builder()
            .excelBatchDtoList(excelBatchDtoList)
            .lookUpData(lookUpData)
            .mfcStocks(List.of(stockDto, stockDto1))
            .build();

        List<ExcelBatchDto> result = strategy.populateBatchTemplate(populateCondition);

        assertFalse(result.isEmpty());
        assertEquals(3, result.size());
        assertEquals(3, result.getFirst().getQuantity());
        assertEquals("ROOM", result.getFirst().getFrom());

        assertEquals(10, result.get(1).getQuantity());
        assertEquals("01-01-A-1", result.get(1).getFrom());

        assertEquals(10, result.getLast().getQuantity());
        assertEquals("01-01-A-1", result.getLast().getFrom());
    }

    @Test
    void when_populate_batch_template_splitting_out_new_batch_when_use_rd() {
        ExcelBatchDto excelBatchDto = DataBuilder.buildBatchDto("item1", "order1", 8);
        excelBatchDto.setFrom(null);
        StockDto stockDto = DataBuilder.buildStockDto("item1", ".RD", 50, LocationType.BIN);
        StockDto stockDto1 = DataBuilder.buildStockDto("item1", ".RD-04-C-3", 35, LocationType.STOCK);
        StockDto stockDt2 = DataBuilder.buildStockDto("item2", "01-01-A-1", 1, LocationType.BIN);

        LookupDto lookupDto = DataBuilder.buildLookupData("item1", "order1", UploadDocNameEnum.COSTCO);
        List<LookupDto> lookupDtoList = new ArrayList<>();
        lookupDtoList.add(lookupDto);

        List<ExcelBatchDto> excelBatchDtoList = new ArrayList<>();
        excelBatchDtoList.add(excelBatchDto);
        Map<UploadDocNameEnum, List<LookupDto>> lookUpData = new EnumMap<>(UploadDocNameEnum.class);
        lookUpData.put(UploadDocNameEnum.COSTCO, lookupDtoList);

        PopulateCondition populateCondition = PopulateCondition.builder()
            .excelBatchDtoList(excelBatchDtoList)
            .lookUpData(lookUpData)
            .mfcStocks(List.of(stockDto, stockDto1, stockDt2))
            .build();

        List<ExcelBatchDto> result = strategy.populateBatchTemplate(populateCondition);

        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        assertEquals(8, result.getFirst().getQuantity());
        assertEquals(".RD", result.getFirst().getFrom());
    }

    @Test
    void when_populate_batch_template_splitting_out_new_batch_when_use_pulling_bin_sufficient_and_have_two_stock() {
        ExcelBatchDto excelBatchDto = DataBuilder.buildBatchDto("item1", "order1", 8);
        excelBatchDto.setFrom(null);
        StockDto stockDto1 = DataBuilder.buildStockDto("item1", "01-04-A-3", 35, LocationType.STOCK);
        StockDto stockDt2 = DataBuilder.buildStockDto("item1", "01-01-A-1", 7, LocationType.BIN);

        LookupDto lookupDto = DataBuilder.buildLookupData("item1", "order1", UploadDocNameEnum.COSTCO);
        List<LookupDto> lookupDtoList = new ArrayList<>();
        lookupDtoList.add(lookupDto);

        List<ExcelBatchDto> excelBatchDtoList = new ArrayList<>();
        excelBatchDtoList.add(excelBatchDto);
        Map<UploadDocNameEnum, List<LookupDto>> lookUpData = new EnumMap<>(UploadDocNameEnum.class);
        lookUpData.put(UploadDocNameEnum.COSTCO, lookupDtoList);

        PopulateCondition populateCondition = PopulateCondition.builder()
            .excelBatchDtoList(excelBatchDtoList)
            .lookUpData(lookUpData)
            .mfcStocks(List.of(stockDto1, stockDt2))
            .build();

        List<ExcelBatchDto> result = strategy.populateBatchTemplate(populateCondition);

        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        assertEquals(8, result.getFirst().getQuantity());
        assertEquals(stockDt2.getSubLocation(), result.getFirst().getFrom());
    }

    @Test
    void when_populate_batch_template_splitting_out_new_batch_when_use_pulling_bin_sufficient_and_have_one_stock() {
        ExcelBatchDto excelBatchDto = DataBuilder.buildBatchDto("item1", "order1", 8);
        excelBatchDto.setFrom(null);
        StockDto stockDto1 = DataBuilder.buildStockDto("item1", "01-04-C-3", 35, LocationType.STOCK);
        StockDto stockDt2 = DataBuilder.buildStockDto("item1", "01-01-A-1", 7, LocationType.BIN);

        LookupDto lookupDto = DataBuilder.buildLookupData("item1", "order1", UploadDocNameEnum.COSTCO);
        List<LookupDto> lookupDtoList = new ArrayList<>();
        lookupDtoList.add(lookupDto);

        List<ExcelBatchDto> excelBatchDtoList = new ArrayList<>();
        excelBatchDtoList.add(excelBatchDto);
        Map<UploadDocNameEnum, List<LookupDto>> lookUpData = new EnumMap<>(UploadDocNameEnum.class);
        lookUpData.put(UploadDocNameEnum.COSTCO, lookupDtoList);

        PopulateCondition populateCondition = PopulateCondition.builder()
            .excelBatchDtoList(excelBatchDtoList)
            .lookUpData(lookUpData)
            .mfcStocks(List.of(stockDto1, stockDt2))
            .build();

        List<ExcelBatchDto> result = strategy.populateBatchTemplate(populateCondition);

        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        assertEquals(8, result.getFirst().getQuantity());
        assertEquals(stockDt2.getSubLocation(), result.getFirst().getFrom());
    }

    @Test
    void when_populate_batch_template_splitting_out_new_batch_when_use_upper_bin_and_have_one_stock_sufficient() {
        ExcelBatchDto excelBatchDto = DataBuilder.buildBatchDto("item1", "order1", 8);
        excelBatchDto.setFrom(null);
        StockDto stockDto1 = DataBuilder.buildStockDto("item1", "01-04-C-3", 35, LocationType.STOCK);
        StockDto stockDt2 = DataBuilder.buildStockDto("item1", "01-01-B-1", 7, LocationType.STOCK);

        LookupDto lookupDto = DataBuilder.buildLookupData("item1", "order1", UploadDocNameEnum.COSTCO);
        List<LookupDto> lookupDtoList = new ArrayList<>();
        lookupDtoList.add(lookupDto);

        List<ExcelBatchDto> excelBatchDtoList = new ArrayList<>();
        excelBatchDtoList.add(excelBatchDto);
        Map<UploadDocNameEnum, List<LookupDto>> lookUpData = new EnumMap<>(UploadDocNameEnum.class);
        lookUpData.put(UploadDocNameEnum.COSTCO, lookupDtoList);

        PopulateCondition populateCondition = PopulateCondition.builder()
            .excelBatchDtoList(excelBatchDtoList)
            .lookUpData(lookUpData)
            .mfcStocks(List.of(stockDto1, stockDt2))
            .build();

        List<ExcelBatchDto> result = strategy.populateBatchTemplate(populateCondition);

        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        assertEquals(8, result.getFirst().getQuantity());
        assertEquals(stockDto1.getSubLocation(), result.getFirst().getFrom());
    }

    @Test
    void when_populate_batch_template_splitting_out_new_batch_when_use_upper_bin_and_no_one_stock_sufficient_use_default() {
        ExcelBatchDto excelBatchDto = DataBuilder.buildBatchDto("item1", "order1", 8);
        excelBatchDto.setFrom(null);
        StockDto stockDto1 = DataBuilder.buildStockDto("item1", "01-04-C-3", 5, LocationType.STOCK);
        StockDto stockDt2 = DataBuilder.buildStockDto("item1", "01-01-B-1", 7, LocationType.STOCK);

        LookupDto lookupDto = DataBuilder.buildLookupData("item1", "order1", UploadDocNameEnum.COSTCO);
        List<LookupDto> lookupDtoList = new ArrayList<>();
        lookupDtoList.add(lookupDto);

        List<ExcelBatchDto> excelBatchDtoList = new ArrayList<>();
        excelBatchDtoList.add(excelBatchDto);
        Map<UploadDocNameEnum, List<LookupDto>> lookUpData = new EnumMap<>(UploadDocNameEnum.class);
        lookUpData.put(UploadDocNameEnum.COSTCO, lookupDtoList);

        PopulateCondition populateCondition = PopulateCondition.builder()
            .excelBatchDtoList(excelBatchDtoList)
            .lookUpData(lookUpData)
            .mfcStocks(List.of(stockDto1, stockDt2))
            .build();

        List<ExcelBatchDto> result = strategy.populateBatchTemplate(populateCondition);

        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        assertEquals(8, result.getFirst().getQuantity());
        assertEquals(stockDt2.getSubLocation(), result.getFirst().getFrom());
    }

    @Test
    void when_populate_batch_template_and_use_three_pl_prep_tobacco() {
        ExcelBatchDto excelBatchDto = DataBuilder.buildBatchDto("item1", "order1", 8);
        excelBatchDto.setFrom(null);
        excelBatchDto.setDepartment(BatchConstants.TOBACCO_DEPARTMENT);
        StockDto stockDto = DataBuilder.buildStockDto("item2", ".RD", 50, LocationType.BIN);
        LookupDto lookupDto = DataBuilder.buildLookupData("item1", "order1", UploadDocNameEnum.DOWNEY);
        lookupDto.setDepartment(BatchConstants.TOBACCO_DEPARTMENT);
        List<LookupDto> lookupDtoList = new ArrayList<>();
        lookupDtoList.add(lookupDto);

        List<ExcelBatchDto> excelBatchDtoList = new ArrayList<>();
        excelBatchDtoList.add(excelBatchDto);
        Map<UploadDocNameEnum, List<LookupDto>> lookUpData = new EnumMap<>(UploadDocNameEnum.class);
        lookUpData.put(UploadDocNameEnum.DOWNEY, lookupDtoList);
        lookUpData.put(UploadDocNameEnum.MASTER,
            List.of(DataBuilder.buildLookupData("item1", "order1", UploadDocNameEnum.MASTER)));

        PopulateCondition populateCondition = PopulateCondition.builder()
            .excelBatchDtoList(excelBatchDtoList)
            .lookUpData(lookUpData)
            .mfcStocks(List.of(stockDto))
            .build();

        List<ExcelBatchDto> result = strategy.populateBatchTemplate(populateCondition);

        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        assertEquals(8, result.getFirst().getQuantity());
        assertEquals(BatchConstants.TOBACCO, result.getFirst().getPrep());
    }

    @Test
    void when_populate_batch_template_and_use_three_pl_prep_snacks() {
        ExcelBatchDto excelBatchDto = DataBuilder.buildBatchDto("item1", "order1", 8);
        excelBatchDto.setFrom(null);
        excelBatchDto.setDepartment(BatchConstants.CANDY_AND_SNACKS);
        StockDto stockDto = DataBuilder.buildStockDto("item2", ".RD", 50, LocationType.BIN);
        LookupDto lookupDto = DataBuilder.buildLookupData("item1", "order1", UploadDocNameEnum.DOWNEY);
        lookupDto.setDepartment(BatchConstants.CANDY_AND_SNACKS);
        List<LookupDto> lookupDtoList = new ArrayList<>();
        lookupDtoList.add(lookupDto);

        List<ExcelBatchDto> excelBatchDtoList = new ArrayList<>();
        excelBatchDtoList.add(excelBatchDto);
        Map<UploadDocNameEnum, List<LookupDto>> lookUpData = new EnumMap<>(UploadDocNameEnum.class);
        lookUpData.put(UploadDocNameEnum.DOWNEY, lookupDtoList);
        lookUpData.put(UploadDocNameEnum.MASTER,
            List.of(DataBuilder.buildLookupData("item1", "order1", UploadDocNameEnum.MASTER)));

        PopulateCondition populateCondition = PopulateCondition.builder()
            .excelBatchDtoList(excelBatchDtoList)
            .lookUpData(lookUpData)
            .mfcStocks(List.of(stockDto))
            .build();

        List<ExcelBatchDto> result = strategy.populateBatchTemplate(populateCondition);

        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        assertEquals(8, result.getFirst().getQuantity());
        assertEquals(BatchConstants.SNACKS, result.getFirst().getPrep());
    }

    @Test
    void when_populate_batch_template_and_use_costco_prep() {
        ExcelBatchDto excelBatchDto = DataBuilder.buildBatchDto("item1", "order1", 8);
        excelBatchDto.setFrom(null);
        excelBatchDto.setDepartment(BatchConstants.TOBACCO_DEPARTMENT);
        StockDto stockDto = DataBuilder.buildStockDto("item2", ".RD", 50, LocationType.BIN);
        LookupDto lookupDto = DataBuilder.buildLookupData("item1", "order1", UploadDocNameEnum.COSTCO);
        lookupDto.setDepartment(BatchConstants.TOBACCO_DEPARTMENT);
        List<LookupDto> lookupDtoList = new ArrayList<>();
        lookupDtoList.add(lookupDto);

        List<ExcelBatchDto> excelBatchDtoList = new ArrayList<>();
        excelBatchDtoList.add(excelBatchDto);
        Map<UploadDocNameEnum, List<LookupDto>> lookUpData = new EnumMap<>(UploadDocNameEnum.class);
        lookUpData.put(UploadDocNameEnum.COSTCO, lookupDtoList);
        lookUpData.put(UploadDocNameEnum.MASTER,
            List.of(DataBuilder.buildLookupData("item1", "order1", UploadDocNameEnum.MASTER)));

        PopulateCondition populateCondition = PopulateCondition.builder()
            .excelBatchDtoList(excelBatchDtoList)
            .lookUpData(lookUpData)
            .mfcStocks(List.of(stockDto))
            .build();

        List<ExcelBatchDto> result = strategy.populateBatchTemplate(populateCondition);

        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        assertEquals(8, result.getFirst().getQuantity());
        assertEquals(lookupDto.getAisle(), result.getFirst().getPrep());
    }

    @Test
    void when_populate_batch_template_and_MDC_MFC() {
        ExcelBatchDto excelBatchDto = DataBuilder.buildBatchDto("item1", "order1", 8);
        excelBatchDto.setFrom(null);
        ExcelBatchDto excelBatchDto1 = DataBuilder.buildBatchDto("item1", "order2", 8);
        excelBatchDto1.setFrom(null);
        StockDto stockDto = DataBuilder.buildStockDto("item1", "01-01-A-1", 50, LocationType.BIN);
        StockDto stockDto1 = DataBuilder.buildStockDto("item1", ".RD", 10, LocationType.BIN);
        stockDto1.setSource(SourceEnum.MDC);
        LookupDto lookupDto = DataBuilder.buildLookupData("item1", "order1", UploadDocNameEnum.MASTER);

        List<ExcelBatchDto> excelBatchDtoList = new ArrayList<>();
        excelBatchDtoList.add(excelBatchDto);
        excelBatchDtoList.add(excelBatchDto1);
        Map<UploadDocNameEnum, List<LookupDto>> lookUpData = new EnumMap<>(UploadDocNameEnum.class);
        lookUpData.put(UploadDocNameEnum.MASTER, List.of(lookupDto));

        PopulateCondition populateCondition = PopulateCondition.builder()
            .excelBatchDtoList(excelBatchDtoList)
            .lookUpData(lookUpData)
            .mfcStocks(List.of(stockDto))
            .mdcStocks(List.of(stockDto1))
            .build();

        List<ExcelBatchDto> result = strategy.populateBatchTemplate(populateCondition);

        assertFalse(result.isEmpty());
        assertEquals(2, result.size());
        assertEquals(8, result.getFirst().getQuantity());
        assertEquals(stockDto1.getName(), result.getFirst().getFrom());
        assertEquals(8, result.getLast().getQuantity());
        assertEquals(stockDto.getName(), result.getLast().getFrom());
    }

    @Test
    void when_populate_batch_template_use_special_upper_bin_as_upper_bin() {
        ExcelBatchDto excelBatchDto = DataBuilder.buildBatchDto("item1", "order1", 10);
        excelBatchDto.setFrom(null);
        StockDto stockDto = DataBuilder.buildStockDto("item1", ".RD-MDC", 5, LocationType.STOCK);
        StockDto stockDt1 = DataBuilder.buildStockDto("item1", "101-01-A-1", 5, LocationType.BIN);

        LookupDto lookupDto = DataBuilder.buildLookupData("item1", "order1", UploadDocNameEnum.MASTER);
        List<LookupDto> lookupDtoList = new ArrayList<>();
        lookupDtoList.add(lookupDto);

        List<ExcelBatchDto> excelBatchDtoList = new ArrayList<>();
        excelBatchDtoList.add(excelBatchDto);
        Map<UploadDocNameEnum, List<LookupDto>> lookUpData = new EnumMap<>(UploadDocNameEnum.class);
        lookUpData.put(UploadDocNameEnum.MASTER, lookupDtoList);

        PopulateCondition populateCondition = PopulateCondition.builder()
            .excelBatchDtoList(excelBatchDtoList)
            .lookUpData(lookUpData)
            .mdcStocks(List.of(stockDto, stockDt1))
            .build();

        List<ExcelBatchDto> result = strategy.populateBatchTemplate(populateCondition);

        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        assertEquals(10, result.getFirst().getQuantity());
        assertEquals("101-01-A-1", result.getFirst().getFrom());
    }

    @Test
    void when_populate_batch_template_use_tow_picking_bins() {
        List<ExcelBatchDto> excelBatchDtoList = new ArrayList<>();

        for (int i = 0; i < 50; i++) {
            ExcelBatchDto excelBatchDto = DataBuilder.buildBatchDto("item1", "order" + i, 1);
            excelBatchDtoList.add(excelBatchDto);
        }
        ExcelBatchDto excelBatchDto = DataBuilder.buildBatchDto("item1", "order1", 1);
        excelBatchDto.setFrom(null);
        StockDto stockDto = DataBuilder.buildStockDto("item1", "103-03-A-1", 18, LocationType.BIN);
        StockDto stockDt1 = DataBuilder.buildStockDto("item1", "108-11-A-2", 22, LocationType.BIN);
        StockDto stockDto2 = DataBuilder.buildStockDto("item1", "103-03-C-1", 50, LocationType.STOCK);

        LookupDto lookupDto = DataBuilder.buildLookupData("item1", "order1", UploadDocNameEnum.MASTER);
        List<LookupDto> lookupDtoList = new ArrayList<>();
        lookupDtoList.add(lookupDto);

        Map<UploadDocNameEnum, List<LookupDto>> lookUpData = new EnumMap<>(UploadDocNameEnum.class);
        lookUpData.put(UploadDocNameEnum.MASTER, lookupDtoList);

        PopulateCondition populateCondition = PopulateCondition.builder()
            .excelBatchDtoList(excelBatchDtoList)
            .lookUpData(lookUpData)
            .mdcStocks(List.of(stockDto, stockDt1, stockDto2))
            .build();

        List<ExcelBatchDto> result = strategy.populateBatchTemplate(populateCondition);

        assertFalse(result.isEmpty());
        assertEquals(50, result.size());
        assertEquals(28, result.stream().filter(batchDto -> batchDto.getFrom().equals("103-03-A-1")).count());
        assertEquals(22, result.stream().filter(batchDto -> batchDto.getFrom().equals("108-11-A-2")).count());
    }


}