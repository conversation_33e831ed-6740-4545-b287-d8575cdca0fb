package com.mercaso.wms.interfaces;

import static com.mercaso.wms.utils.MockDataUtils.buildAccountPreference;
import static com.mercaso.wms.utils.MockDataUtils.buildBatch;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.command.crossdock.BindItemAndTaskCommand;
import com.mercaso.wms.application.command.crossdock.BindItemAndTaskCommand.BindItemAndTaskItem;
import com.mercaso.wms.application.command.crossdock.CreateCrossDockTaskCommand;
import com.mercaso.wms.application.dto.CrossDockItemDto;
import com.mercaso.wms.application.dto.CrossDockTaskDto;
import com.mercaso.wms.domain.accountpreference.AccountPreference;
import com.mercaso.wms.domain.accountpreference.AccountPreferenceRepository;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.batch.BatchRepository;
import com.mercaso.wms.domain.crossdockitem.CrossDockTaskItem;
import com.mercaso.wms.domain.crossdockitem.CrossDockTaskItemRepository;
import com.mercaso.wms.domain.crossdockitem.enums.CrossDockItemSourceEnum;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.domain.warehouse.enums.WarehouseType;
import com.mercaso.wms.infrastructure.utils.DateUtils;
import com.mercaso.wms.utils.CrossDockTaskResourceApi;
import com.mercaso.wms.utils.MockDataUtils;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class CrossDockTaskResourceIT extends AbstractIT {

    @Autowired
    private CrossDockTaskResourceApi crossDockTaskResourceApi;

    @Autowired
    private AccountPreferenceRepository accountPreferenceRepository;

    @Autowired
    private CrossDockTaskItemRepository crossDockTaskItemRepository;

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private PickingTaskRepository pickingTaskRepository;

    @Test
    void when_createCrossDockTask_then_taskIsCreated_or_returned() {
        Warehouse mdcWarehouse = warehouseRepository.findByName("MDC");
        if (mdcWarehouse == null) {
            mdcWarehouse = Warehouse.builder()
                    .id(UUID.randomUUID())
                    .name("MDC")
                    .type(WarehouseType.INTERNAL)
                    .build();
            warehouseRepository.save(mdcWarehouse);
        }
        AccountPreference picker = buildAccountPreference(UUID.randomUUID(), mdcWarehouse, null);
        accountPreferenceRepository.save(picker);

        String deliveryDate = DateUtils.getNextDeliveryDate().format(DateTimeFormatter.ISO_LOCAL_DATE);

        CreateCrossDockTaskCommand command = CreateCrossDockTaskCommand.builder()
                .pickerUserId(picker.getUserId())
                .build();
        CrossDockTaskDto first = crossDockTaskResourceApi.createCrossDockTask(command);
        assertNotNull(first);
        assertNotNull(first.getId());
        assertEquals(picker.getUserId(), first.getPickerUserId());
        assertEquals(picker.getUserName(), first.getPickerUserName());
        assertEquals(mdcWarehouse.getId(), first.getWarehouseId());
        assertEquals(deliveryDate, first.getDeliveryDate());

        CrossDockTaskDto second = crossDockTaskResourceApi.createCrossDockTask(command);
        assertNotNull(second);
        assertEquals(first.getId(), second.getId());
        assertEquals(deliveryDate, second.getDeliveryDate());
    }

    @Test
    void when_bindCrossDockTaskItem_then_itemsAreBound() throws Exception {
        // Setup warehouse
        Warehouse mdcWarehouse = warehouseRepository.findByName("MDC");
        if (mdcWarehouse == null) {
            mdcWarehouse = Warehouse.builder()
                .id(UUID.randomUUID())
                .name("MDC")
                .type(WarehouseType.INTERNAL)
                .build();
            warehouseRepository.save(mdcWarehouse);
        }

        AccountPreference picker = buildAccountPreference(UUID.randomUUID(), mdcWarehouse, null);
        accountPreferenceRepository.save(picker);

        CreateCrossDockTaskCommand command = CreateCrossDockTaskCommand.builder()
            .pickerUserId(picker.getUserId())
            .build();
        CrossDockTaskDto crossDockTask = crossDockTaskResourceApi.createCrossDockTask(command);

        Batch batch = buildBatch(UUID.randomUUID());
        batch = batchRepository.save(batch);

        List<PickingTask> pickingTasks = MockDataUtils.buildPickingTask(batch.getId(), 1);
        List<PickingTask> pickingTasksList = MockDataUtils.buildPickingTask(batch.getId(), 1);
        List<PickingTaskItem> pickingTaskItems = MockDataUtils.buildPickingTaskItems(3);

        PickingTask pickingTask1 = pickingTasks.getFirst();

        PickingTask pickingTask2 = pickingTasksList.getFirst();
        pickingTask2.setPickingTaskItems(pickingTaskItems);

        PickingTaskItem pickingTaskItem = pickingTask1.getPickingTaskItems().getFirst();
        pickingTaskItem.setPickedQty(2);

        PickingTaskItem first = pickingTask1.getPickingTaskItems().getFirst();
        PickingTaskItem second = pickingTask2.getPickingTaskItems().getFirst();
        PickingTaskItem third = pickingTask2.getPickingTaskItems().get(1);

        UUID itemId = UUID.randomUUID();
        String breakdownName = "A-01";
        first.setBreakdownName(breakdownName);
        second.setBreakdownName(breakdownName);
        third.setBreakdownName(breakdownName);

        first.setPickedQty(3);
        second.setPickedQty(3);
        third.setPickedQty(3);

        first.setItemId(itemId);
        second.setItemId(itemId);
        third.setItemId(itemId);

        first.setSkuNumber("TEST-SKU1");
        second.setSkuNumber("TEST-SKU1");
        third.setSkuNumber("TEST-SKU1");

        pickingTask1 = pickingTaskRepository.save(pickingTask1);
        pickingTask2 = pickingTaskRepository.save(pickingTask2);

        first = pickingTask1.getPickingTaskItems().getFirst();
        second = pickingTask2.getPickingTaskItems().getFirst();
        third = pickingTask2.getPickingTaskItems().get(1);

        createCrossDockTaskItem(first);
        createCrossDockTaskItem(second);
        createCrossDockTaskItem(third);

        // Bind item to cross dock task
        BindItemAndTaskCommand bindItemAndTaskCommand = BindItemAndTaskCommand.builder()
            .items(List.of(
                BindItemAndTaskItem.builder()
                    .pickingTaskItemId(first.getId())
                    .sequence("1/3")
                    .build(),
                BindItemAndTaskItem.builder()
                    .pickingTaskItemId(first.getId())
                    .sequence("2/3")
                    .build(),
                BindItemAndTaskItem.builder()
                    .pickingTaskItemId(first.getId())
                    .sequence("3/3")
                    .build(),
                BindItemAndTaskItem.builder()
                    .pickingTaskItemId(second.getId())
                    .sequence("1/3")
                    .build()
                ,
                BindItemAndTaskItem.builder()
                    .pickingTaskItemId(second.getId())
                    .sequence("2/3")
                    .build()
                ,
                BindItemAndTaskItem.builder()
                    .pickingTaskItemId(second.getId())
                    .sequence("3/3")
                    .build(),
                BindItemAndTaskItem.builder()
                    .pickingTaskItemId(third.getId())
                    .sequence("1/3")
                    .build()
                ,
                BindItemAndTaskItem.builder()
                    .pickingTaskItemId(third.getId())
                    .sequence("2/3")
                    .build()
                ,
                BindItemAndTaskItem.builder()
                    .pickingTaskItemId(third.getId())
                    .sequence("3/3")
                    .build()
            ))
            .build();

        CrossDockTaskDto result = crossDockTaskResourceApi.bindItemAndTask(crossDockTask.getId(), bindItemAndTaskCommand);
        // Verify results
        assertNotNull(result);
        assertEquals(crossDockTask.getId(), result.getId());
        assertEquals(9, result.getCrossDockTaskItems().size());

        CrossDockItemDto boundItem = result.getCrossDockTaskItems().getFirst();
        assertEquals("[1/3]", boundItem.getSequence().toString());
        assertEquals(1, boundItem.getPickedQty());
        assertEquals(1, boundItem.getCrossDockedQty());

        CrossDockItemDto boundItem2 = result.getCrossDockTaskItems().get(1);
        assertEquals("[2/3]", boundItem2.getSequence().toString());
        assertEquals(1, boundItem2.getPickedQty());
        assertEquals(1, boundItem2.getCrossDockedQty());

        CrossDockItemDto boundItem3 = result.getCrossDockTaskItems().get(2);
        assertEquals("[3/3]", boundItem3.getSequence().toString());
        assertEquals(1, boundItem3.getPickedQty());
        assertEquals(1, boundItem3.getCrossDockedQty());

        CrossDockItemDto boundItem4 = result.getCrossDockTaskItems().get(3);
        assertEquals("[1/3]", boundItem4.getSequence().toString());
        assertEquals(1, boundItem4.getPickedQty());
        assertEquals(1, boundItem4.getCrossDockedQty());

        CrossDockItemDto boundItem5 = result.getCrossDockTaskItems().get(4);
        assertEquals("[2/3]", boundItem5.getSequence().toString());
        assertEquals(1, boundItem5.getPickedQty());
        assertEquals(1, boundItem5.getCrossDockedQty());

        CrossDockItemDto boundItem6 = result.getCrossDockTaskItems().get(5);
        assertEquals("[3/3]", boundItem6.getSequence().toString());
        assertEquals(1, boundItem6.getPickedQty());
        assertEquals(1, boundItem6.getCrossDockedQty());

        CrossDockItemDto boundItem7 = result.getCrossDockTaskItems().get(6);
        assertEquals("[1/3]", boundItem7.getSequence().toString());
        assertEquals(1, boundItem7.getPickedQty());
        assertEquals(1, boundItem7.getCrossDockedQty());

        CrossDockItemDto boundItem8 = result.getCrossDockTaskItems().get(7);
        assertEquals("[2/3]", boundItem8.getSequence().toString());
        assertEquals(1, boundItem8.getPickedQty());
        assertEquals(1, boundItem8.getCrossDockedQty());

        CrossDockItemDto boundItem9 = result.getCrossDockTaskItems().get(8);
        assertEquals("[3/3]", boundItem9.getSequence().toString());
        assertEquals(1, boundItem9.getPickedQty());
        assertEquals(1, boundItem9.getCrossDockedQty());

    }

    private void createCrossDockTaskItem(PickingTaskItem pickingTaskItem) {
        Integer pickedQty = pickingTaskItem.getPickedQty();
        for (int i = 1; i <= pickedQty; i++) {
            CrossDockTaskItem crossDockTaskItem = CrossDockTaskItem.builder()
                .batchId(UUID.randomUUID())
                .skuNumber(pickingTaskItem.getSkuNumber())
                .title(pickingTaskItem.getTitle())
                .itemId(pickingTaskItem.getItemId())
                .taskItemId(pickingTaskItem.getId())
                .source(CrossDockItemSourceEnum.PICKING_TASK)
                .shippingOrderId(UUID.randomUUID()) // Set this if applicable
                .shippingOrderItemId(UUID.randomUUID()) // Set this if applicable
                .pickedQty(1) // Assuming 1 for each item in the sequence
                .crossDockedQty(0) // Initially 0, will be updated later
                .breakdownName(pickingTaskItem.getBreakdownName())
                .orderNumber(pickingTaskItem.getOrderNumber()) // Assuming order number is available
                .build();
            crossDockTaskItemRepository.save(crossDockTaskItem);
        }
    }

}