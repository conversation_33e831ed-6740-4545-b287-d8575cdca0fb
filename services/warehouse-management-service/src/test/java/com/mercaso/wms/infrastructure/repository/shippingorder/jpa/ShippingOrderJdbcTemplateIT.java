package com.mercaso.wms.infrastructure.repository.shippingorder.jpa;

import static com.mercaso.wms.utils.MockDataUtils.buildShopifyOrderDto;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto;
import com.mercaso.wms.batch.dto.SkuCountByDeliveryDate;
import com.mercaso.wms.utils.ShopifyWebhookResourceApi;
import java.time.LocalDate;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class ShippingOrderJdbcTemplateIT extends AbstractIT {

    @Autowired
    ShopifyWebhookResourceApi shopifyWebhookResourceApi;
    @Autowired
    ShippingOrderJdbcTemplate shippingOrderJdbcTemplate;

    @Test
    void when_count_sku_by_delivery_date() throws Exception {
        ShopifyOrderDto shopifyOrderDto = buildShopifyOrderDto();
        shopifyOrderDto.setTags(LocalDate.now().plusDays(20) + ", SELLER_Mercaso");

        shopifyWebhookResourceApi.webhook(shopifyOrderDto);

        List<SkuCountByDeliveryDate> skuCountByDeliveryDate = shippingOrderJdbcTemplate.skuCountByDeliveryDate(LocalDate.now()
            .plusDays(20)
            .toString());

        assertEquals(10, skuCountByDeliveryDate.size());
    }

}