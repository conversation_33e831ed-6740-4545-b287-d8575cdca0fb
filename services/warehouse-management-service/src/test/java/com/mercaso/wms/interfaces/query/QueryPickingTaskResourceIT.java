package com.mercaso.wms.interfaces.query;

import static com.mercaso.wms.utils.MockDataUtils.buildBatch;
import static com.mercaso.wms.utils.MockDataUtils.buildPickingTask;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.dto.PickingTaskDto;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.batch.BatchRepository;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.utils.PickingTaskResourceApi;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class QueryPickingTaskResourceIT extends AbstractIT {

    @Autowired
    PickingTaskResourceApi pickingTaskResourceApi;
    @Autowired
    PickingTaskRepository pickingTaskRepository;
    @Autowired
    BatchRepository batchRepository;

    @Test
    void when_find_picking_task_by_id_then_return_picking_task() {
        // given

        Batch saved = batchRepository.save(buildBatch(UUID.randomUUID()));
        List<PickingTask> pickingTasks = buildPickingTask(saved.getId(), 2);

        // when
        List<PickingTask> savedPickingTasks = pickingTaskRepository.saveAll(pickingTasks);
        // then
        PickingTaskDto pickingTask = pickingTaskResourceApi.getPickingTask(savedPickingTasks.get(0).getId());

        assertNotNull(pickingTask);
        assertEquals(savedPickingTasks.getFirst().getId(), pickingTask.getId());
    }

}