springdoc:
  swagger-ui:
    enabled: true
spring:
  config:
    import: vault://
    activate:
      on-profile: dev

finale:
  domain: mercasosandbox
  mfc-stage: MFC-dev-Stage
  mdc-stage: MDC-dev-Stage
  ship-sb: SHIP-dev-SB
shopify:
  webhook:
    domain: dev-2-mercaso.myshopify.com
  shop-name: dev-2-mercaso
slack:
  fraud-order-alter:
    webhook: *********************************************************************************
    enable: true
  delivery:
    webhook-url: https://hooks.slack.com/triggers/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
    task-build-exception: https://hooks.slack.com/triggers/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
    order-issues: https://hooks.slack.com/triggers/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
    order-rescheduled: https://hooks.slack.com/triggers/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