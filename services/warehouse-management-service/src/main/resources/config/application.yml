server:
  port: 8080
  forward-headers-strategy: framework
  tomcat:
    connection-timeout: 360s
springdoc:
  swagger-ui:
    enabled: false

spring:
  application:
    name: warehouse-management-service
  profiles:
    active: local
  datasource:
    driver-class-name: org.postgresql.Driver
    url: jdbc:postgresql://${host.db_server}/warehouse_management_service
    hikari:
      connection-init-sql: "set role warehouse_management_user"
      connection-timeout: 15000
      minimum-idle: 1
      maximum-pool-size: 30
      allow-pool-suspension: true
  jpa:
    properties:
      hibernate:
        jdbc:
          batch_size: 500
        order_inserts: true
        dialect: org.hibernate.dialect.PostgreSQLDialect
        types:
          spatial:
            enabled: true
      database-platform: org.hibernate.dialect.PostgreSQLDialect
  flyway:
    locations: classpath:/db/migration
    baseline-on-migrate: true
    enabled: true
    init-sqls: SET ROLE warehouse_management_user
  cloud:
    vault:
      enabled: true
      scheme: http
      port: 8200
      host: vault.vault.svc.cluster.local
      authentication: KUBERNETES
      kubernetes:
        role: warehouse-management-service
        kubernetes-path: kubernetes
        service-account-token-file: /var/run/secrets/kubernetes.io/serviceaccount/token
      # Application will fail if it cannot connect to vault, remember to disable vault for envs that don't need it
      fail-fast: true

      # Need to disable generic engine so that spring cloud knows to only pull secrets from KV engine
      generic:
        enabled: false
      kv:
        enabled: true
        backend: secret
        profile-separator: '/'
        application-name: warehouse-management-service
      database:
        enabled: true
        role: warehouse-management-service
        backend: database
        username-property: spring.datasource.username
        password-property: spring.datasource.password
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
otel:
  traces:
    exporter: otlp
    sampler: always_on
  metrics:
    exporter: otlp
  exporter:
    otlp:
      endpoint: ${OTEL_EXPORTER_OTLP_ENDPOINT}
mercaso:
  encrypt:
    cipher-key: ${auth0.cipher-key}
  auth:
    ums-url: ${mercaso.ums-url}
    connect-timeout: 5000
    request-timeout: 10000
    m2m-token-api-key: ${auth0_m2m_wms_key}
  data-url: http://data-service.default.svc.cluster.local
  ims-url: http://item-management-service.default.svc.cluster.local
  ums-url: http://user-service.default.svc.cluster.local
  wms-external-url: https://svcs.us-west-2.${spring.config.activate.on-profile}.aws.mercaso.store/warehouse-management
  delivery-external-url: ${mercaso.wms-external-url}/delivery
  admin-portal-base-url: https://saas-${spring.config.activate.on-profile}.mercaso.store
  delivery-portal-base-url: ${mercaso.admin-portal-base-url}/delivery
  document:
    operations:
      enabled: true
      storage:
        bucket-name: ${aws-bucket-name}
      delivery:
        root-folder: delivery/
  featureflags:
    sdk: ${mercaso.featureflags.sdk}
    enabled: true
management:
  endpoint:
    health:
      show-details: always
      probes:
        enabled: true
      group:
        readiness:
          include: readinessState
    shutdown:
      enabled: true
  endpoints:
    enabled-by-default: true
    web:
      exposure:
        include: '*'
  health:
    db:
      enabled: true
    vault:
      enabled: false
    livenessState:
      enabled: true
    readinessState:
      enabled: true
  server:
    port: 8081

security:
  public-paths:
    - /shopify/webhook
    - /delivery/route-manager-webhook/**
    - /shopify/delivery-order/webhook
    - /delivery/documents/download

google:
  maps:
    api-key: ${google.maps.api-key}
    connect-timeout: 10000
    read-timeout: 10000

shopify:
  token: ${shopify.token}
  admin-order-url: https://admin.shopify.com/store/${shopify.shop-name}/orders/
account:
  secret:
    public-key: ${account.secret.public-key}
  system-user: cb8016f0-fd7d-4cc8-b0d0-30e8fcfc1e6c
  driver:
    creation:
      timeout:
        seconds: 10
      poll:
        interval:
          seconds: 1
finale:
  graphql-api-url: https://app.finaleinventory.com/${finale.domain}/api/graphql
  mfc-facility-url: /${finale.domain}/api/facility/100000
  token: ${finale.token}
  facility-url: https://app.finaleinventory.com/${finale.domain}/api/facility/
  transfer-url: https://app.finaleinventory.com/${finale.domain}/api/inventorytransfer/
  create-transfer-shipment-url: https://app.finaleinventory.com/${finale.domain}/api/shipment
  ship-transfer-shipment-url: https://app.finaleinventory.com/${finale.domain}/api/shipment/%s/ship
  receive-transfer-shipment-url: https://app.finaleinventory.com/${finale.domain}/api/shipment/%s/receive
  create-purchase-order-url: https://app.finaleinventory.com/${finale.domain}/api/order
  receive-purchase-order-url: https://app.finaleinventory.com/${finale.domain}/api/shipment
  complete-purchase-order-url: https://app.finaleinventory.com/${finale.domain}/api/order/%s/complete
  create-shipment-url: https://app.finaleinventory.com/${finale.domain}/api/shipment
  receive-shipment-url: https://app.finaleinventory.com/${finale.domain}/api/shipment/%s/receive
  mfc-stage: MFC-Stage
  mdc-stage: MDC-Stage
  ship-sb: SHIP-SB
  sub-locations:
    lost-found: LOST-FOUND
    photo-studio: PHOTO-STUDIO

route-manager:
  base-url: https://wwrm.workwave.com
  api-key: ${route-manager.api-key}
  territory-id: ${route-manager.territory-id}
  hmac-secret: ${route-manager.hmac-secret}
  default-secret-key: ${route-manager.default-secret-key}
fraud-order:
  ip: **************,**************,**************,**************,**************
  device-id: adf3ba57-4c21-43a9-91e4-f1aa03e0675fR,91f51421-7a3d-4387-9443-2d600c1d520fR,5cbd76a7-7d6d-416a-95c3-30c8afd2eddcR,040d4a7f-970d-41f1-8ad0-9e852989677a
  phone-number: +19098438998,+19092158680,+16263233709,+16262779906,+17147299731
onesignal:
  api-key: ${onesignal.api-key}
  app-id: ${onesignal.app-id}
  create-user-url: https://api.onesignal.com/apps/${onesignal.app-id}/users
  get-user-url: https://api.onesignal.com/apps/${onesignal.app-id}/users/by/external_id/
  send-email-url: https://api.onesignal.com/notifications?c=email
  invoice-template-id: ${onesignal.invoice-template-id}
external:
  public-download-signature-url: ${mercaso.delivery-external-url}/documents/download?signature=