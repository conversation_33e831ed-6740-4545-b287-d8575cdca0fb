host:
  db_server: postgres-wms:5432
spring:
  cloud:
    vault:
      enabled: false
  config:
    activate:
      on-profile: integration
  datasource:
    username: warehouse_management_user
    password: mercaso
    url: jdbc:postgresql://${host.db_server}/warehouse_management_service
otel:
  exporter:
    otlp:
      endpoint: http://localhost:4317
mercaso:
  encrypt:
    cipher-key: 8qWW8HbvJyyDxkO/Po6zqGbGntzyCrL4
  auth:
    m2m-token-api-key: testkey
  document:
    operations:
      storage:
        bucket-name: mercaso-wms
  featureflags:
    sdk: test
security:
  enable-method-security: false
  public-paths:
    - /**
shopify:
  webhook:
    domain: dev-2-mercaso.myshopify.com
  token: XXXXX
  shop-name: dev-2-mercaso

finale:
  domain: mercasosandbox
  token: XXX
account:
  secret:
    public-key: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCnJAL3qHah8wQGXtbUfqxNwyqv/8OuA8hII4z3C57IX8zB/fEbczvK78kWEH5o2nvQWnUP6/kU4xxytMVojU+jI8nxlPYg6eFjtXWqP1UQRoI9MLB8ncQcZYv0o8girhlJi6y4TE0kQHtEhi0rZiYx8TaHKVBPM2di/tCXl9LPeQIDAQAB
route-manager:
  api-key: 8a22o14p-526c-43be-a933-5b211aa85ade # mock in the local environment
  territory-id: ********-f8af-4f13-b1bb-3c1f73b0b0a1 # mock in the local environment
  hmac-secret: 8a22o14p-526c-43be-a933-5b211aa85ade # mock in the local environment
  default-secret-key: Mercaso@666

fraud-order:
  ip: 127.0.0.1
  device-id: ddf3ba57-4c21-43a9-91e4-f1aa03e0675fR
  phone-number: **********

slack:
  fraud-order-alter:
    webhook: https://fake.slack.com/services/T02AVL4UJG4/B08M9K8QMNF/RSQKzUvUWnyjxpHnQzagVBJC
    enable: false
  delivery:
    webhook-url: https://hooks.slack.com/triggers/1231
    task-build-exception: https://hooks.slack.com/triggers/1231
    order-issues: https://hooks.slack.com/triggers/1231
    order-rescheduled: https://hooks.slack.com/triggers/1231
onesignal:
  api-key: testKey
  app-id: testAppId
  invoice-template-id: testInvoiceTemplateId

google:
  maps:
    api-key: mockapikey123