package com.mercaso.wms.infrastructure.repository.shippingorder.jpa.dataobject;

import com.mercaso.wms.domain.shippingorder.enums.ShippingOrderStatus;
import com.mercaso.wms.infrastructure.repository.BaseDo;
import com.mercaso.wms.infrastructure.repository.customer.jpa.dataobject.CustomerAddressDo;
import com.mercaso.wms.infrastructure.repository.location.jpa.dataobject.LocationDo;
import com.mercaso.wms.infrastructure.repository.shippingorderitem.jpa.dataobject.ShippingOrderItemDo;
import com.mercaso.wms.infrastructure.repository.warehouse.jpa.dataobject.WarehouseDo;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(exclude = "shippingOrderItems")
@Table(name = "shipping_order")
@SQLDelete(sql = "update shipping_order set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
public class ShippingOrderDo extends BaseDo {

    @JoinColumn(name = "warehouse_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    private WarehouseDo warehouse;

    @Column(name = "batch_id")
    private UUID batchId;

    @JoinColumn(name = "customer_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private CustomerAddressDo customerAddress;

    @Column(name = "shopify_order_id", length = 100)
    private String shopifyOrderId;

    @Column(name = "order_number", nullable = false, length = 100)
    private String orderNumber;

    @Column(name = "fulfillment_status", length = 50)
    private String fulfillmentStatus;

    @Column(name = "order_date")
    private Instant orderDate;

    @Column(name = "delivery_date")
    private String deliveryDate;

    @Column(name = "shipped_date")
    private Instant shippedDate;

    @Column(name = "status", nullable = false, length = 20)
    @Enumerated(value = EnumType.STRING)
    private ShippingOrderStatus status;

    @JoinColumn(name = "breakdown_location_id")
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    private LocationDo breakdownLocation;

    @OneToMany(mappedBy = "shippingOrder", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @OrderBy("line ASC")
    private List<ShippingOrderItemDo> shippingOrderItems;

}