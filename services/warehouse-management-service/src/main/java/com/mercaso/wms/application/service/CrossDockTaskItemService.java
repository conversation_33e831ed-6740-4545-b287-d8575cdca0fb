package com.mercaso.wms.application.service;

import com.mercaso.wms.domain.crossdockitem.CrossDockTaskItem;
import com.mercaso.wms.domain.crossdockitem.CrossDockTaskItemRepository;
import com.mercaso.wms.domain.crossdockitem.enums.CrossDockItemSourceEnum;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItemRepository;
import com.mercaso.wms.domain.receivingtaskitem.ReceivingTaskItem;
import com.mercaso.wms.domain.receivingtaskitem.ReceivingTaskItemRepository;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class CrossDockTaskItemService {

    private final CrossDockTaskItemRepository crossDockTaskItemRepository;

    private final PickingTaskItemRepository pickingTaskItemRepository;

    private final ReceivingTaskItemRepository receivingTaskItemRepository;

    @Async
    public void handleTaskItemQtyChange(Map<UUID, Integer> qtyChangeMap, CrossDockItemSourceEnum source, UUID batchId) {
        if (qtyChangeMap == null || qtyChangeMap.isEmpty()) {
            return;
        }
        List<UUID> ids = new ArrayList<>(qtyChangeMap.keySet());
        if (source == CrossDockItemSourceEnum.PICKING_TASK) {
            handlePickingTaskQtyChange(ids, qtyChangeMap, batchId);
        } else if (source == CrossDockItemSourceEnum.RECEIVING_TASK) {
            handleReceivingTaskQtyChange(ids, qtyChangeMap, batchId);
        }
    }

    private void handlePickingTaskQtyChange(List<UUID> pickingTaskItemIds, Map<UUID, Integer> qtyChangeMap, UUID batchId) {
        Map<UUID, PickingTaskItem> itemMap = pickingTaskItemRepository.findByIds(pickingTaskItemIds)
            .stream().collect(Collectors.toMap(PickingTaskItem::getId, x -> x));
        for (UUID itemId : pickingTaskItemIds) {
            PickingTaskItem pickingTaskItem = itemMap.get(itemId);
            if (pickingTaskItem == null) {
                continue;
            }
            int qty = qtyChangeMap.getOrDefault(itemId, 0);
            List<CrossDockTaskItem> crossDockItems = crossDockTaskItemRepository.findByTaskItemId(itemId);
            int diff = qty - crossDockItems.size();
            if (diff > 0) {
                insertCrossDockItems(pickingTaskItem, diff, batchId);
                log.info("[handleTaskItemQtyChange] Inserted {} cross dock items for taskItemId={}", diff, itemId);
            } else if (diff < 0) {
                deleteCrossDockItems(crossDockItems, -diff);
                log.info("[handleTaskItemQtyChange] Deleted {} cross dock items for taskItemId={}", -diff, itemId);
            } else {
                log.info("[handleTaskItemQtyChange] No change for taskItemId={}", itemId);
            }
        }
    }

    private void handleReceivingTaskQtyChange(List<UUID> receivingTaskIds, Map<UUID, Integer> qtyChangeMap, UUID batchId) {
        Map<UUID, ReceivingTaskItem> itemMap = new HashMap<>();
        for (UUID id : receivingTaskIds) {
            ReceivingTaskItem item = receivingTaskItemRepository.findById(id);
            if (item != null) {
                itemMap.put(id, item);
            }
        }
        for (UUID itemId : receivingTaskIds) {
            ReceivingTaskItem item = itemMap.get(itemId);
            if (item == null) {
                continue;
            }
            int qty = qtyChangeMap.getOrDefault(itemId, 0);
            List<CrossDockTaskItem> crossDockItems = crossDockTaskItemRepository.findByTaskItemId(itemId);
            int diff = qty - crossDockItems.size();
            if (diff > 0) {
                insertCrossDockItems(item, diff, batchId);
                log.info("[handleTaskItemQtyChange] Inserted {} cross dock items for receivingTaskItemId={}", diff, itemId);
            }
        }
    }

    private void insertCrossDockItems(PickingTaskItem item, int count, UUID batchId) {
        for (int i = 0; i < count; i++) {
            CrossDockTaskItem newItem = CrossDockTaskItem.builder().build().create(item, batchId);
            crossDockTaskItemRepository.save(newItem);
        }
    }

    private void insertCrossDockItems(ReceivingTaskItem item, int count, UUID batchId) {
        for (int i = 0; i < count; i++) {
            CrossDockTaskItem newItem = CrossDockTaskItem.builder().build().create(item, batchId);
            crossDockTaskItemRepository.save(newItem);
        }
    }

    private void deleteCrossDockItems(List<CrossDockTaskItem> crossDockItems, int count) {
        List<UUID> toDeleteIds = crossDockItems.stream()
            .sorted(Comparator.comparing(CrossDockTaskItem::getId))
            .limit(count)
            .map(CrossDockTaskItem::getId)
            .toList();
        if (!CollectionUtils.isEmpty(toDeleteIds)) {
            crossDockTaskItemRepository.deleteByIds(toDeleteIds);
        }
    }

    @Async
    public void handlePickingTaskItemsDelete(List<UUID> pickingTaskItemIds) {
        if (CollectionUtils.isEmpty(pickingTaskItemIds)) {
            return;
        }
        try {
            crossDockTaskItemRepository.deleteByTaskItemIds(pickingTaskItemIds);
        } catch (Exception e) {
            log.error("[handlePickingTaskItemsDelete] Failed to delete cross dock task items for pickingTaskItemIds={}",
                pickingTaskItemIds,
                e);
        }
    }
} 