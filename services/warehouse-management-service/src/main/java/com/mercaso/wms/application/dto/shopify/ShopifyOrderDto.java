package com.mercaso.wms.application.dto.shopify;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ShopifyOrderDto {

    private String id;
    private String name;
    @JsonProperty("fulfillment_status")
    private String fulfillmentStatus;
    @JsonProperty("updated_at")
    private Instant updatedAt;
    @JsonProperty("cancelled_at")
    private Instant cancelledAt;
    private String tags;
    private String note;
    @JsonProperty("shipping_address")
    private ShippingAddressDto shippingAddress;
    @JsonProperty("line_items")
    private List<ShopifyLineItemDto> lineItems;
    @JsonProperty("note_attributes")
    private List<NoteAttributeDto> noteAttributes;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ShopifyLineItemDto {

        private String id;
        private String sku;
        @JsonProperty("current_quantity")
        private int currentQuantity;
        @JsonProperty("fulfillable_quantity")
        private int fulfillableQuantity;
        private String title;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NoteAttributeDto{
        private String name;
        private String value;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ShippingAddressDto {

        @JsonProperty("first_name")
        private String firstName;
        @JsonProperty("last_name")
        private String lastName;
        private String address1;
        private String address2;
        private String phone;
        private String city;
        private String zip;
        private String province;
        private String country;
        private String company;
        private BigDecimal latitude;
        private BigDecimal longitude;
        private String name;


    }
}
