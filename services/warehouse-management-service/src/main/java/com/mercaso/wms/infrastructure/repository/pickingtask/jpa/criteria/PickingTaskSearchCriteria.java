package com.mercaso.wms.infrastructure.repository.pickingtask.jpa.criteria;


import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import java.util.List;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PickingTaskSearchCriteria {

    private UUID pickerUserId;

    private PickingTaskStatus[] statuses;

    private SourceEnum source;

    private List<String> numbers;

    private String deliveryDate;

    private List<String> orderNumbers;

    private List<String> departments;

    private List<String> categories;

    private List<String> aisleNumbers;

    private String breakdownName;

    private PickingTaskType type;

    private List<String>  skuNumbers;

}