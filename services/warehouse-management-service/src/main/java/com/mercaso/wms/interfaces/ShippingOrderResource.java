package com.mercaso.wms.interfaces;

import com.mercaso.ims.client.dto.ItemSerachDto;
import com.mercaso.wms.application.dto.shopify.ShopifyOrderAdminDto;
import com.mercaso.wms.application.service.ShippingOrderApplicationService;
import com.mercaso.wms.infrastructure.external.ims.ImsAdaptor;
import com.mercaso.wms.infrastructure.external.shopify.ShopifyAdaptor;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Validated
@RestController
@RequestMapping("/shipping-orders")
@RequiredArgsConstructor
public class ShippingOrderResource {

    private final ShippingOrderApplicationService shippingOrderApplicationService;

    private final ShopifyAdaptor shopifyAdaptor;

    private final ImsAdaptor imsAdaptor;

    @PutMapping("/{orderId}/sync")
    @PreAuthorize("hasAuthority('wms:write:shipping-orders')")
    public void syncShippingOrder(@PathVariable String orderId) {
        ShopifyOrderAdminDto shopifyOrderByAdminApi = shopifyAdaptor.getShopifyOrderByAdminApi(orderId);
        shopifyOrderByAdminApi.getOrder().getLineItems().forEach(item -> {
            if (item.getSku() == null) {
                ItemSerachDto itemSerachDto = imsAdaptor.searchItemsByTitle(item.getTitle());
                if (itemSerachDto != null) {
                    item.setSku(itemSerachDto.getSkuNumber());
                    log.info("Sku number is null for item: {} in order: {}. Searching for sku number in IMS: {}",
                        item.getTitle(), orderId, item.getSku());
                }
            }
        });
        shippingOrderApplicationService.createOrUpdate(shopifyOrderByAdminApi.getOrder());
    }

    @GetMapping("/{deliveryDate}/picked-shipping-orders-export")
    @PreAuthorize("hasAuthority('wms:read:shipping-orders')")
    public void pickedShippingOrdersExport(@PathVariable(value = "deliveryDate") String deliveryDate,
        HttpServletResponse response)
        throws IOException {

    }

}
