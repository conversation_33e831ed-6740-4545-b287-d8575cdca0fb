package com.mercaso.wms.infrastructure.repository.crossdockitem.jpa;

import com.mercaso.wms.infrastructure.repository.crossdockitem.jpa.dataobject.CrossDockTaskItemDo;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface CrossDockTaskItemJpaDao extends JpaRepository<CrossDockTaskItemDo, UUID> {
    List<CrossDockTaskItemDo> findByTaskItemId(UUID taskItemId);

    List<CrossDockTaskItemDo> findByTaskItemIdIn(List<UUID> taskItemIds);
} 