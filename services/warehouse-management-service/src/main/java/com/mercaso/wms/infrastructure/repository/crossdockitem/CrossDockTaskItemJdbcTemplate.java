package com.mercaso.wms.infrastructure.repository.crossdockitem;

import com.alibaba.excel.util.StringUtils;
import com.mercaso.wms.application.dto.view.SearchCrossDockTaskItemView;
import com.mercaso.wms.application.query.CrossDockTaskItemQuery;
import com.mercaso.wms.domain.crossdockitem.enums.CrossDockItemStatusEnum;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Collections;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;
import java.util.Arrays;
import java.util.UUID;

@Slf4j
@Repository
@RequiredArgsConstructor
public class CrossDockTaskItemJdbcTemplate {

    private final NamedParameterJdbcTemplate jdbcTemplate;

    public Page<SearchCrossDockTaskItemView> search(CrossDockTaskItemQuery criteria) {
        StringBuilder sql = new StringBuilder();
        StringBuilder countSql = new StringBuilder();
        MapSqlParameterSource params = new MapSqlParameterSource();

        sql.append("SELECT cdt_items.task_item_id as task_item_id, ")
            .append("CAST(MIN(cdt_items.batch_id::text) AS uuid) as batch_id, ")
            .append("MIN(cdt_items.sku_number) as sku_number, ")
            .append("MIN(cdt_items.title) as title, ")
            .append("CAST(MIN(cdt_items.item_id::text) AS uuid) as item_id, ")
            .append("MIN(cdt_items.source) as source, ")
            .append("CAST(MIN(cdt_items.shipping_order_id::text) AS uuid) as shipping_order_id, ")
            .append("CAST(MIN(cdt_items.shipping_order_item_id::text) AS uuid) as shipping_order_item_id, ")
            .append("NULLIF(array_agg(s) filter (where s is not null), '{}') as sequence, ")
            .append("SUM(cdt_items.picked_qty) as picked_qty, ")
            .append("SUM(cdt_items.cross_docked_qty) as cross_docked_qty, ")
            .append("MIN(cdt_items.breakdown_name) as breakdown_name, ")
            .append("MAX(cdt_items.created_at) as created_at, ")
            .append("MAX(cdt_items.updated_at) as updated_at, ")
            .append("MIN(cdt_items.order_number) as order_number, ")
            .append("MIN(soi.line) as line ")
            .append("FROM cross_dock_task_items cdt_items ")
            .append("LEFT JOIN batch b on cdt_items.batch_id = b.id ")
            .append("LEFT JOIN shipping_order_items soi on cdt_items.shipping_order_item_id = soi.id ")
            .append("LEFT JOIN LATERAL unnest(cdt_items.sequence) AS s ON TRUE ")
            .append("WHERE cdt_items.deleted_at IS NULL ");

        countSql.append("SELECT COUNT(*) FROM (SELECT cdt_items.task_item_id FROM cross_dock_task_items cdt_items ")
            .append("LEFT JOIN batch b on cdt_items.batch_id = b.id ")
            .append("LEFT JOIN shipping_order_items soi on cdt_items.shipping_order_item_id = soi.id ")
            .append("WHERE cdt_items.deleted_at IS NULL ");

        if (StringUtils.isNotBlank(criteria.getSku())) {
            sql.append("AND cdt_items.sku_number = :sku ");
            countSql.append("AND cdt_items.sku_number = :sku ");
            params.addValue("sku", criteria.getSku());
        }

        if (StringUtils.isNotBlank(criteria.getDeliveryDate())) {
            sql.append("AND b.tag = :deliveryDate ");
            countSql.append("AND b.tag = :deliveryDate ");
            params.addValue("deliveryDate", criteria.getDeliveryDate());
        }

        if (criteria.getPickingTaskItemId() != null) {
            sql.append("AND cdt_items.task_item_id = :pickingTaskItemId ");
            countSql.append("AND cdt_items.task_item_id = :pickingTaskItemId ");
            params.addValue("pickingTaskItemId", criteria.getPickingTaskItemId());
        }

        if (criteria.getReceivingTaskItemId() != null) {
            sql.append("AND cdt_items.task_item_id = :receivingTaskItemId ");
            countSql.append("AND cdt_items.task_item_id = :receivingTaskItemId ");
            params.addValue("receivingTaskItemId", criteria.getReceivingTaskItemId());
        }

        sql.append("GROUP BY cdt_items.task_item_id ");
        countSql.append("GROUP BY cdt_items.task_item_id");
        if (criteria.getStatus() != null && CrossDockItemStatusEnum.ALL != criteria.getStatus()) {
            if (CrossDockItemStatusEnum.CROSSED == criteria.getStatus()) {
                sql.append(" HAVING SUM(cdt_items.cross_docked_qty) > 0 ");
                countSql.append(" HAVING SUM(cdt_items.cross_docked_qty) > 0 ");
            } else if (CrossDockItemStatusEnum.NOT_CROSSED == criteria.getStatus()) {
                sql.append(" HAVING (SUM(cdt_items.cross_docked_qty) = 0 OR SUM(cdt_items.cross_docked_qty) IS NULL) ");
                countSql.append(" HAVING (SUM(cdt_items.cross_docked_qty) = 0 OR SUM(cdt_items.cross_docked_qty) IS NULL) ");
            }
        }
        countSql.append(") t");

        sql.append("ORDER BY MAX(cdt_items.created_at) DESC ");

        int page = criteria.getPage() != null ? criteria.getPage() : 1;
        int pageSize = criteria.getPageSize() != null ? criteria.getPageSize() : 20;
        int offset = (page - 1) * pageSize;

        sql.append("LIMIT :limit OFFSET :offset");
        params.addValue("limit", pageSize);
        params.addValue("offset", offset);

        Long total = jdbcTemplate.queryForObject(countSql.toString(), params, Long.class);
        List<SearchCrossDockTaskItemView> content = jdbcTemplate.query(sql.toString(), params, (rs, rowNum) -> mapToView(rs));
        Pageable pageable = PageRequest.of(page - 1, pageSize);
        return new PageImpl<>(content, pageable, total != null ? total : 0);
    }

    private SearchCrossDockTaskItemView mapToView(ResultSet rs) throws SQLException {
        SearchCrossDockTaskItemView view = new SearchCrossDockTaskItemView();
        view.setBatchId(rs.getObject("batch_id", UUID.class));
        view.setSkuNumber(rs.getString("sku_number"));
        view.setTitle(rs.getString("title"));
        view.setItemId(rs.getObject("item_id", UUID.class));
        view.setTaskItemId(rs.getObject("task_item_id", UUID.class));
        view.setSource(rs.getString("source") == null ? null
            : com.mercaso.wms.domain.crossdockitem.enums.CrossDockItemSourceEnum.valueOf(rs.getString("source")));
        view.setShippingOrderId(rs.getObject("shipping_order_id", UUID.class));
        view.setShippingOrderItemId(rs.getObject("shipping_order_item_id", UUID.class));
        java.sql.Array sequenceArray = rs.getArray("sequence");
        view.setSequence(sequenceArray == null ? Collections.emptyList()
            : Arrays.asList((String[]) sequenceArray.getArray()));
        view.setPickedQty(rs.getObject("picked_qty") == null ? null : rs.getInt("picked_qty"));
        view.setCrossDockedQty(rs.getObject("cross_docked_qty") == null ? null : rs.getInt("cross_docked_qty"));
        view.setBreakdownName(rs.getString("breakdown_name"));
        view.setOrderNumber(rs.getString("order_number"));
        view.setLine(rs.getObject("line") == null ? null : rs.getInt("line"));
        return view;
    }
} 