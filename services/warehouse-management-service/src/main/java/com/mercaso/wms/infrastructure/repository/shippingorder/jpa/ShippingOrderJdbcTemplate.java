package com.mercaso.wms.infrastructure.repository.shippingorder.jpa;

import com.mercaso.wms.batch.dto.SkuCountByDeliveryDate;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ShippingOrderJdbcTemplate {

    private final NamedParameterJdbcTemplate jdbcTemplate;

    public List<SkuCountByDeliveryDate> skuCountByDeliveryDate(String deliveryDate) {
        String sql = "SELECT soi.sku_number as sku, CAST(sum(soi.qty) as BIGINT) as count FROM shipping_order_items soi "
            + "left join public.shipping_order so on so.id = soi.shipping_order_id "
            + "WHERE so.status = 'OPEN' and so.fulfillment_status is null and so.delivery_date = :deliveryDate GROUP BY sku";
        Map<String, Object> params = Map.of("deliveryDate", deliveryDate);

        List<Map<String, Object>> results = jdbcTemplate.queryForList(sql, params);

        return results.stream()
            .filter(row -> row.get("sku") != null && row.get("count") != null)
            .map(row -> SkuCountByDeliveryDate.builder().sku((String) row.get("sku")).count((Long) row.get("count")).build())
            .toList();
    }

}
