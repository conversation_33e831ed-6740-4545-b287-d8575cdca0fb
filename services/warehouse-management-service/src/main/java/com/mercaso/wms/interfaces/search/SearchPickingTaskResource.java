package com.mercaso.wms.interfaces.search;

import static com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus.excludeCanceledStatus;

import com.mercaso.wms.application.dto.PickingTaskDto;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.query.PickingTaskQuery;
import com.mercaso.wms.application.query.SortType;
import com.mercaso.wms.application.searchservice.PickingTaskSearchService;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.businessevent.EntityEnums;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.infrastructure.utils.DateUtils;
import com.mercaso.wms.interfaces.util.SortByParseUtil;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Validated
@RestController
@RequestMapping("/search")
@RequiredArgsConstructor
public class SearchPickingTaskResource {

    private final PickingTaskSearchService pickingTaskSearchService;

    @PreAuthorize("hasAuthority('wms:read:picking-tasks')")
    @GetMapping("/picking-tasks")
    public Result<PickingTaskDto> searchPickingTasks(
        @RequestParam(value = "page", defaultValue = "1") @Min(value = 1, message = "Page number must be greater than 0")
        int page,
        @RequestParam(value = "pageSize", defaultValue = "20") @Min(value = 1, message = "Page size must be greater than 0")
        @Max(value = 1000, message = "Page size must be less than or equal to 1000")
        int pageSize,
        @RequestParam(value = "numbers", required = false) List<String> numbers,
        @RequestParam(value = "pickerUserId", required = false) UUID pickerUserId,
        @RequestParam(value = "source", required = false) SourceEnum source,
        @RequestParam(value = "statuses", required = false) PickingTaskStatus[] statuses,
        @RequestParam(value = "deliveryDate", required = false) LocalDate deliveryDate,
        @RequestParam(value = "orderNumbers", required = false) List<String> orderNumbers,
        @RequestParam(value = "type", required = false) PickingTaskType type,
        @RequestParam(value = "departments", required = false) List<String> departments,
        @RequestParam(value = "aisleNumbers", required = false) List<String> aisleNumbers,
        @RequestParam(value = "breakdownName", required = false) String breakdownName,
        @RequestParam(value = "categories", required = false) List<String> categories,
        @RequestParam(value = "sortTypes", required = false) List<SortType> sortTypes,
        @RequestParam(value = "skuNumbers", required = false) List<String> skuNumbers) {
        log.info(
            "Search picking tasks with page: {}, pageSize: {}, numbers: {}, pickerUserId: {}, source: {}, statuses: {}, deliveryDate: {}, orderNumbers: {}, type: {}, departments: {}, aisleNumbers: {}, breakdownName: {}, sortTypes: {}, skuNumbers: {}",
            page,
            pageSize,
            numbers,
            pickerUserId,
            source,
            statuses,
            deliveryDate,
            orderNumbers,
            type,
            departments,
            aisleNumbers,
            breakdownName,
            sortTypes,
            skuNumbers);

        if (deliveryDate == null && pickerUserId == null) {
            deliveryDate = DateUtils.getNextDeliveryDate();
            log.info("[searchPickingTasks] No delivery date provided, using next delivery date: {}",
                deliveryDate);
        }
        Page<PickingTaskDto> pickingTaskDtoPage = pickingTaskSearchService.searchPickingTasks(
            PickingTaskQuery.builder()
                .pickerUserId(pickerUserId)
                .source(source)
                .numbers(CollectionUtils.isEmpty(numbers) ? null : numbers.stream().map(String::toUpperCase).toList())
                .statuses(statuses == null ? excludeCanceledStatus().toArray(new PickingTaskStatus[0]) : statuses)
                .deliveryDate(deliveryDate)
                .orderNumbers(orderNumbers)
                .type(type)
                .departments(departments)
                .breakdownName(StringUtils.isEmpty(breakdownName) ? null : breakdownName.toUpperCase())
                .aisleNumbers(aisleNumbers)
                .categories(categories)
                .skuNumbers(skuNumbers)
                .build(),
            PageRequest.of(page - 1,
                pageSize,
                Objects.requireNonNull(
                    CollectionUtils.isEmpty(sortTypes) ? SortByParseUtil.getCamelCaseSortFields(List.of(SortType.CREATED_AT_DESC),
                        EntityEnums.PICKING_TASK)
                        : SortByParseUtil.getCamelCaseSortFields(sortTypes, EntityEnums.PICKING_TASK))));
        return new Result<>(pickingTaskDtoPage.getContent(), pickingTaskDtoPage.getTotalElements());
    }

    @PreAuthorize("hasAuthority('wms:read:picking-tasks')")
    @GetMapping("/v2/picking-tasks")
    public Result<PickingTaskDto> searchPickingTasksV2(
        @RequestParam(value = "page", defaultValue = "1") @Min(value = 1, message = "Page number must be greater than 0")
        int page,
        @RequestParam(value = "pageSize", defaultValue = "20") @Min(value = 1, message = "Page size must be greater than 0")
        int pageSize,
        @RequestParam(value = "numbers", required = false) List<String> numbers,
        @RequestParam(value = "pickerUserId", required = false) UUID pickerUserId,
        @RequestParam(value = "source", required = false) SourceEnum source,
        @RequestParam(value = "statuses", required = false) PickingTaskStatus[] statuses,
        @RequestParam(value = "deliveryDate", required = false) LocalDate deliveryDate,
        @RequestParam(value = "orderNumbers", required = false) List<String> orderNumbers,
        @RequestParam(value = "type", required = false) PickingTaskType type,
        @RequestParam(value = "departments", required = false) List<String> departments,
        @RequestParam(value = "aisleNumbers", required = false) List<String> aisleNumbers,
        @RequestParam(value = "breakdownName", required = false) String breakdownName,
        @RequestParam(value = "categories", required = false) List<String> categories,
        @RequestParam(value = "sortTypes", required = false) List<SortType> sortTypes,
        @RequestParam(value = "skuNumbers", required = false) List<String> skuNumbers) {
        log.info(
            "[searchPickingTaskList] Search picking tasks v2 with page: {}, pageSize: {}, numbers: {}, pickerUserId: {}, source: {}, statuses: {}, deliveryDate: {}, orderNumbers: {}, type: {}, departments: {}, aisleNumbers: {}, breakdownName: {}, sortTypes: {}, skuNumbers: {}",
            page,
            pageSize,
            numbers,
            pickerUserId,
            source,
            statuses,
            deliveryDate,
            orderNumbers,
            type,
            departments,
            aisleNumbers,
            breakdownName,
            sortTypes,
            skuNumbers);

        Page<PickingTaskDto> pickingTaskDtoPage = pickingTaskSearchService.searchPickingTaskList(
            PickingTaskQuery.builder()
                .pickerUserId(pickerUserId)
                .source(source)
                .numbers(CollectionUtils.isEmpty(numbers) ? null : numbers.stream().map(String::toUpperCase).toList())
                .statuses(statuses == null ? excludeCanceledStatus().toArray(new PickingTaskStatus[0]) : statuses)
                .deliveryDate(deliveryDate)
                .orderNumbers(orderNumbers)
                .type(type)
                .departments(departments)
                .breakdownName(StringUtils.isEmpty(breakdownName) ? null : breakdownName.toUpperCase())
                .aisleNumbers(aisleNumbers)
                .categories(categories)
                .skuNumbers(skuNumbers)
                .build(),
            PageRequest.of(page - 1,
                pageSize,
                Objects.requireNonNull(
                    CollectionUtils.isEmpty(sortTypes) ? SortByParseUtil.getCamelCaseSortFields(List.of(SortType.CREATED_AT_DESC),
                        EntityEnums.PICKING_TASK)
                        : SortByParseUtil.getCamelCaseSortFields(sortTypes, EntityEnums.PICKING_TASK))));
        return new Result<>(pickingTaskDtoPage.getContent(), pickingTaskDtoPage.getTotalElements());
    }

}
