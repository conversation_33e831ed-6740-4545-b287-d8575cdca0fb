package com.mercaso.wms.batch.enums;

import lombok.Getter;

@Getter
public enum GeneratedDocNameEnum {

    NEW_BATCH_TEMPLATE("NEW BATCH TEMPLATE"),
    FULL_BREAKDOWN_ALL("SZ - FULL BREAK DOWN ALL"),
    FULL_BREAKDOWN("SZ - FULL BREAK DOWN"),
    FULL_BREAKDOWN_SMALL("SZ - FULL BREAK DOWN SMALL"),
    INPUT_ORDER_DATA("Input Order Data"),
    INPUT_FINALE_STOCK_QTY("Input FINALE Stock Qty"),
    <PERSON><PERSON>("MFC"),
    MFC_BEVERAGES_BIG_ORDER("MFC Beverage (big orders)"),
    MFC_BEVERAGES_BIG_ORDER_WMS_PICKING("MFC Beverage (big)-WMS"),
    <PERSON>FC_BEVERAGES_SMALL_ORDER("MFC Beverage (small)-WMS"),
    <PERSON><PERSON>_CANDY("M<PERSON> Candy-WMS"),
    VERNON("VERNON"),
    VERNON_LABELS("VERN<PERSON> LABELS"),
    VERNON_TOTALS("VERN<PERSON> TOTALS"),
    EXOTIC("EXOTIC"),
    EXOTIC_LABELS("EXOTIC LABELS"),
    EXOTIC_TOTALS("EXOTIC TOTALS"),
    SEVEN_STARS("7 Stars QA"),
    SEVEN_STARS_LABELS("7 Stars Labels"),
    SEVEN_STARS_TOTALS("7 Stars Totals"),
    JETRO("Jetro QA"),
    JETRO_LABELS("Jetro Labels"),
    JETRO_TOTALS("Jetro Totals"),
    DOWNEY("DOWNEY"),
    DOWNEY_LABELS("DOWNEY LABELS"),
    COSTCO("COSTCO"),
    COSTCO_LABELS("COSTCO LABELS"),
    COSTCO_TOBACCO_TOTALS("COSTCO TOBACCO TOTALS"),
    MISSION("MISSION"),
    MISSION_LABELS("MISSION LABELS"),
    MISSION_ORDER_FORM("MISSION Order Form"),
    TOBACCO_DRIVER_QA("TOBACCO DRIVER QA"),
    REFRIGERATED_DRIVER_QA("REFRIGERATED DRIVER QA"),
    MFC_PICK_DEDUCTIONS("MFC PICK DEDUCTIONS"),
    MFC_BEVERAGES_BIG_ORDER_LABELS("MFC Beverage (big orders)LABELS"),
    MFC_BEVERAGES_BIG_ORDER_LABELS_WMS_PICKING("MFC Beverage (big)LABEL-WMS"),
    MFC_BEVERAGES_SMALL_ORDER_LABELS("MFC Beverage (small)LABEL-WMS"),
    MFC_CANDY_LABELS("MFC Candy LABELS-WMS"),
    DA_DAILY_LOG("Daily Log Automation"),
    ;

    private final String value;

    GeneratedDocNameEnum(String value) {
        this.value = value;
    }

}
