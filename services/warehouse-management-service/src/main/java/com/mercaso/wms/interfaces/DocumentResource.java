package com.mercaso.wms.interfaces;

import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.document.operations.models.UploadDocumentRequest;
import com.mercaso.document.operations.operations.DocumentOperations;
import com.mercaso.wms.infrastructure.utils.DateUtils;
import java.io.IOException;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@RestController
@RequestMapping("/documents")
@RequiredArgsConstructor
public class DocumentResource {

    private final DocumentOperations documentOperations;

    @PostMapping
    @PreAuthorize("hasAuthority('wms:write:documents')")
    public DocumentResponse uploadDocuments(@RequestParam("file") MultipartFile file) throws IOException {
        UploadDocumentRequest document = UploadDocumentRequest.builder()
            .content(file.getBytes())
            .documentName(DateUtils.laDateTime().concat("-")
                .concat(Objects.requireNonNull(file.getOriginalFilename())))
            .build();
        log.info("[uploadDocuments] Uploading document: {}", document.getDocumentName());
        return documentOperations.uploadDocument(document);
    }

    @GetMapping("/{documentName}")
    @PreAuthorize("hasAuthority('wms:read:documents')")
    public String getDocument(@PathVariable String documentName) {
        
        return documentOperations.getSignedUrl(documentName);
    }

}
