package com.mercaso.wms.delivery.application.dto.deliverytask;


import com.mercaso.wms.application.dto.BaseDto;
import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderDto;
import com.mercaso.wms.delivery.domain.deliverytask.enums.DeliveryTaskStatus;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DeliveryTaskDto extends BaseDto {

    private UUID id;
    private String number;
    private String deliveryDate;
    private DeliveryTaskStatus status;
    private String truckNumber;
    private UUID driverUserId;
    private String driverUserName;
    private Instant clockIn;
    private Instant dispatchAt;
    private Instant breakStartAt;
    private Instant breakEndAt;
    private Instant completedAt;
    private List<DeliveryOrderDto> deliveryOrders;
    private PreCheckDto preCheck;
    private PostCheckDto postCheck;
    private Instant createdAt;
    private String createdBy;
    private Instant updatedAt;
    private String updatedBy;
}
