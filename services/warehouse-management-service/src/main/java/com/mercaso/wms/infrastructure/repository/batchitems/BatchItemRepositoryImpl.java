package com.mercaso.wms.infrastructure.repository.batchitems;

import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.batchitem.BatchItemRepository;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.repository.batchitems.jpa.BatchItemsJpaDao;
import com.mercaso.wms.infrastructure.repository.batchitems.jpa.dataobject.BatchItemsDo;
import com.mercaso.wms.infrastructure.repository.batchitems.jpa.mapper.BatchItemDoMapper;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class BatchItemRepositoryImpl implements BatchItemRepository {

    private final BatchItemsJpaDao jpaDao;
    private final BatchItemDoMapper mapper;
    private final BatchItemsJpaDao batchItemsJpaDao;

    @Override
    public BatchItem save(BatchItem domain) {
        BatchItemsDo batchItemsDo = jpaDao.save(mapper.domainToDo(domain));
        return mapper.doToDomain(batchItemsDo);
    }

    @Override
    public BatchItem findById(UUID id) {
        return mapper.doToDomain(jpaDao.findById(id).orElse(null));
    }

    @Override
    public BatchItem update(BatchItem domain) {
        BatchItemsDo batchItemsDo = jpaDao.findById(domain.getId()).orElse(null);
        if (null == batchItemsDo) {
            throw new WmsBusinessException("BatchItem not found.");
        }
        BatchItemsDo target = mapper.domainToDo(domain);
        List<String> ignoreProperties = new ArrayList<>(List.of("createdBy",
            "createdAt"));
        BeanUtils.copyProperties(target, batchItemsDo, ignoreProperties.toArray(new String[0]));
        batchItemsDo = jpaDao.save(batchItemsDo);
        return mapper.doToDomain(batchItemsDo);
    }

    @Override
    public List<BatchItem> saveAll(List<BatchItem> batchItemList) {
        List<BatchItemsDo> batchItemsDos = mapper.domainToDos(batchItemList);
        return mapper.doToDomains(jpaDao.saveAll(batchItemsDos));
    }

    @Override
    public List<BatchItem> findBatchItemsBy(UUID batchId, String source, List<String> departments, boolean bigOrder) {
        return mapper.doToDomains(jpaDao.findBy(batchId, source, departments, bigOrder));
    }

    @Override
    public List<BatchItem> findBatchItemsByDepartmentNotIn(UUID batchId, String source, List<String> preps) {
        return mapper.doToDomains(jpaDao.findByBatchIdAndSourceAndDepartmentNotIn(batchId, source, preps));
    }

    @Override
    public List<BatchItem> findBatchItemsBy(UUID batchId, String source) {
        return mapper.doToDomains(jpaDao.findByBatchIdAndSource(batchId, source));
    }

    @Override
    public Page<BatchItem> searchBatchItems(String source, String deliveryDate, Pageable page) {
        Page<BatchItemsDo> batchItems = batchItemsJpaDao.findBySourceAndDeliveryDate(source, deliveryDate, page);
        return batchItems.map(mapper::doToDomain);
    }

    @Override
    public List<BatchItem> findByBatchIdAndSourceAndLocationName(UUID batchId, String source, String locationName) {
        return mapper.doToDomains(jpaDao.findByBatchIdAndSourceAndLocationName(batchId, source, locationName));
    }

    @Override
    public List<BatchItem> findBatchItemsBy(UUID batchId, String source, boolean bigOrder) {
        return mapper.doToDomains(jpaDao.findBy(batchId, source, List.of(), bigOrder));
    }

    @Override
    public List<BatchItem> findBatchItemsBy(UUID batchId, List<String> existingOrderNumbers) {
        return mapper.doToDomains(jpaDao.findByBatchIdAndOrderNumberIn(batchId, existingOrderNumbers));
    }

    @Override
    public void deleteAll(List<BatchItem> batchItems) {
        jpaDao.deleteAll(mapper.domainToDos(batchItems));
    }
}
