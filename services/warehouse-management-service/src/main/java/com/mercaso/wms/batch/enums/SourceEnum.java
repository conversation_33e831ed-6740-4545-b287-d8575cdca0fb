package com.mercaso.wms.batch.enums;

import java.util.Collections;
import java.util.EnumSet;
import java.util.Set;
import lombok.Getter;

@Getter
public enum SourceEnum {

    COSTCO("Costco"),
    MISSION("Mission"),
    DOWNEY("Downey Wholesale"),
    MFC(null),
    MDC(null),
    VERNON("Vernon Sales"),
    EXOTIC("Exotic Blvd"),
    SEVEN_STARS("7 Star Savings"),
    JETRO("Jetro"),
    ;

    private final String vendorName;

    private static final Set<SourceEnum> ON_LINE_VENDOR_SET = EnumSet.of(
        MISSION,
        VERNON,
        EXOTIC,
        SEVEN_STARS
    );

    public static Set<SourceEnum> onlineVendors() {
        return Collections.unmodifiableSet(ON_LINE_VENDOR_SET);
    }

    SourceEnum(String vendorName) {
        this.vendorName = vendorName;
    }

    public static SourceEnum fromName(String value) {
        for (SourceEnum sourceEnum : SourceEnum.values()) {
            if (sourceEnum.name().equals(value)) {
                return sourceEnum;
            }
        }
        throw new IllegalArgumentException("Invalid source: " + value);
    }

    public static SourceEnum fromVendorName(String vendorName) {
        for (SourceEnum sourceEnum : SourceEnum.values()) {
            if (sourceEnum.vendorName != null && sourceEnum.vendorName.equalsIgnoreCase(vendorName)) {
                return sourceEnum;
            }
        }
        return null;
    }

}
