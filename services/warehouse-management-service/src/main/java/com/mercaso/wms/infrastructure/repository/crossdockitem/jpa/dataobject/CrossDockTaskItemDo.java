package com.mercaso.wms.infrastructure.repository.crossdockitem.jpa.dataobject;

import com.mercaso.wms.domain.crossdockitem.enums.CrossDockItemSourceEnum;
import com.mercaso.wms.infrastructure.repository.BaseDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.util.List;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

@EqualsAndHashCode(callSuper = true)
@Data
@SQLRestriction("deleted_at is null")
@SQLDelete(sql = "update cross_dock_task_items set deleted_at = current_timestamp where id = ? and updated_at = ?")
@Entity
@Table(name = "cross_dock_task_items")
@NoArgsConstructor
public class CrossDockTaskItemDo extends BaseDo {

    @Column(name = "cross_dock_task_id")
    private UUID crossDockTaskId;

    private UUID batchId;

    private String skuNumber;

    private String title;

    private UUID itemId;

    private UUID taskItemId;

    @Enumerated(value = EnumType.STRING)
    private CrossDockItemSourceEnum source;

    private UUID shippingOrderId;

    private UUID shippingOrderItemId;

    private List<String> sequence;

    private Integer pickedQty;

    private Integer crossDockedQty;

    private String breakdownName;

    private String orderNumber;
}