package com.mercaso.wms.interfaces.search;

import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.dto.view.SearchCrossDockTaskView;
import com.mercaso.wms.application.query.CrossDockTaskQuery;
import com.mercaso.wms.application.searchservice.CrossDockTaskSearchService;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Validated
@RestController
@RequestMapping("/search")
@RequiredArgsConstructor
public class SearchCrossDockTaskResource {

    private final CrossDockTaskSearchService crossDockTaskSearchService;

    @PreAuthorize("hasAuthority('wms:read:cross-dock-tasks')")
    @GetMapping("/cross-dock-tasks")
    public Result<SearchCrossDockTaskView> searchCrossDockTasks(
        @RequestParam(value = "page", defaultValue = "1") @Min(value = 1, message = "Page size must be greater than 0") int page,
        @RequestParam(value = "pageSize", defaultValue = "20") @Min(value = 1, message = "Page size must be greater than 0")
        @Max(value = 1000, message = "Page size must be less than or equal to 1000") int pageSize,
        @RequestParam(value = "number", required = false) String number,
        @RequestParam(value = "pickerUserId", required = false) UUID pickerUserId,
        @RequestParam(value = "deliveryDate", required = false) String deliveryDate) {

        log.info(
            "[searchCrossDockTasks] Search picking tasks v2 with page: {}, pageSize: {}, number: {}, pickerUserId: {}, deliveryDate: {}",
            page,
            pageSize,
            number,
            pickerUserId,
            deliveryDate);

        Page<SearchCrossDockTaskView> crossDockTaskViews = crossDockTaskSearchService.searchCrossDockTasks(
            CrossDockTaskQuery.builder()
                .number(number)
                .deliveryDate(deliveryDate)
                .pickerUserId(pickerUserId)
                .build(),
            PageRequest.of(page - 1,
                pageSize));
        return new Result<>(crossDockTaskViews.getContent(), crossDockTaskViews.getTotalElements());
    }
} 