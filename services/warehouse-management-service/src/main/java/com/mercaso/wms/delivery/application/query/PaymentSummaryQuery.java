package com.mercaso.wms.delivery.application.query;

import com.mercaso.wms.application.query.SortType;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.util.List;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ToString
@Getter
@Setter
@Builder
public class PaymentSummaryQuery {

    private String deliveryDate;

    private List<String> paymentStatus;

    private Boolean issueOrder;

    @Min(value = 1, message = "Page number must be greater than 0")
    private int page;

    @Min(value = 1, message = "Page size must be greater than 0")
    @Max(value = 200, message = "Page size must be less than or equal to 200")
    private int pageSize;

    private List<SortType> sortTypes;
}
