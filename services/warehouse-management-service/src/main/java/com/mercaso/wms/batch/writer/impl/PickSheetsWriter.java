package com.mercaso.wms.batch.writer.impl;

import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.mercaso.wms.batch.constants.BatchConstants;
import com.mercaso.wms.batch.dto.ExcelBatchDto;
import com.mercaso.wms.batch.dto.WriteTemplateCondition;
import com.mercaso.wms.batch.enums.GeneratedDocNameEnum;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.batch.util.ExcelUtil;
import com.mercaso.wms.batch.writer.SheetWriter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Order(1)
@Slf4j
@AllArgsConstructor
public class PickSheetsWriter implements SheetWriter {

    @Override
    public void write(ExcelWriter excelWriter, FillConfig fillConfig, WriteTemplateCondition condition) {
        log.info("[PickSheetsWriter] Start to write pick sheets");
        List<ExcelBatchDto> excelBatchDtos = condition.getExcelBatchDtos();
        Map<String, List<ExcelBatchDto>> sourceAndListMap = condition.getSourceAndListMap();

        Map<String, String> deliveryDateMap = createDeliveryDateMap(condition.getTaggedWith());

        writeSheets(excelWriter, fillConfig, excelBatchDtos, sourceAndListMap, deliveryDateMap);
    }

    private Map<String, String> createDeliveryDateMap(String taggedWith) {
        Map<String, String> deliveryDateMap = new HashMap<>();
        deliveryDateMap.put("deliveryDate", taggedWith);
        return deliveryDateMap;
    }

    private void writeSheets(ExcelWriter excelWriter, FillConfig fillConfig, List<ExcelBatchDto> excelBatchDtos,
        Map<String, List<ExcelBatchDto>> sourceAndListMap, Map<String, String> deliveryDateMap) {
        List<ExcelBatchDto> allExcelBatchDtos = new ArrayList<>(excelBatchDtos.size());
        for (ExcelBatchDto batchDto : excelBatchDtos) {
            ExcelBatchDto excelBatchDto = new ExcelBatchDto();
            BeanUtils.copyProperties(batchDto, excelBatchDto);

            if (StringUtils.isEmpty(excelBatchDto.getSource())) {
                log.info("[PickSheetsWriter] Source is empty: {}", excelBatchDto);
                excelBatchDto.setSource("N/A");
            } else if ((excelBatchDto.getSource().equals(SourceEnum.MFC.name()) || excelBatchDto.getSource()
                .equals(SourceEnum.MDC.name()))
                && "N/A".equals(excelBatchDto.getFrom())) {
                excelBatchDto.setSource("N/A");
            }
            allExcelBatchDtos.add(excelBatchDto);
        }
        writeSheet(excelWriter,
            fillConfig,
            allExcelBatchDtos,
            GeneratedDocNameEnum.NEW_BATCH_TEMPLATE.getValue(),
            deliveryDateMap);
        writeSheet(excelWriter, fillConfig, sourceAndListMap.get(GeneratedDocNameEnum.MFC.getValue()),
            GeneratedDocNameEnum.MFC.getValue(), deliveryDateMap);
        writeSheet(excelWriter, fillConfig, sourceAndListMap.get(SourceEnum.SEVEN_STARS.name()),
            GeneratedDocNameEnum.SEVEN_STARS.getValue(), deliveryDateMap);
        writeSheet(excelWriter, fillConfig, sourceAndListMap.get(SourceEnum.JETRO.name()),
            GeneratedDocNameEnum.JETRO.getValue(), deliveryDateMap);
        writeSheet(excelWriter, fillConfig, sourceAndListMap.get(GeneratedDocNameEnum.VERNON.getValue()),
            GeneratedDocNameEnum.VERNON.getValue(), deliveryDateMap);
        writeSheet(excelWriter, fillConfig, sourceAndListMap.get(GeneratedDocNameEnum.EXOTIC.getValue()),
            GeneratedDocNameEnum.EXOTIC.getValue(), deliveryDateMap);
        writeSheet(excelWriter, fillConfig, sourceAndListMap.get(GeneratedDocNameEnum.DOWNEY.getValue()),
            GeneratedDocNameEnum.DOWNEY.getValue(), deliveryDateMap);
        writeSheet(excelWriter, fillConfig, sourceAndListMap.get(GeneratedDocNameEnum.MISSION.getValue()),
            GeneratedDocNameEnum.MISSION.getValue(), deliveryDateMap);
        writeSheet(excelWriter, fillConfig, sourceAndListMap.get(GeneratedDocNameEnum.COSTCO.getValue()),
            GeneratedDocNameEnum.COSTCO.getValue(), deliveryDateMap);
        ExcelUtil.writerSheetToTemplate(excelBatchDtos,
            BatchConstants.TOBACCO_DEPARTMENT,
            excelWriter,
            fillConfig,
            GeneratedDocNameEnum.TOBACCO_DRIVER_QA.getValue(),
            deliveryDateMap);
        ExcelUtil.writerSheetToTemplate(excelBatchDtos.stream().filter(ExcelBatchDto::isCooler).toList(),
            null,
            excelWriter,
            fillConfig,
            GeneratedDocNameEnum.REFRIGERATED_DRIVER_QA.getValue(),
            deliveryDateMap);
    }

    private void writeSheet(ExcelWriter excelWriter, FillConfig fillConfig, List<ExcelBatchDto> excelBatchDtos,
        String sheetName, Map<String, String> deliveryDateMap) {
        sortBatchDtos(sheetName, excelBatchDtos);
        ExcelUtil.writerSheetToTemplate(excelBatchDtos, null, excelWriter, fillConfig, sheetName, deliveryDateMap);
    }

    private void sortBatchDtos(String source, List<ExcelBatchDto> list) {
        if (list == null || list.isEmpty()) {
            return;
        }
        Comparator<ExcelBatchDto> fromComparator = Comparator.nullsLast(Comparator.comparing(ExcelBatchDto::getFrom,
            Comparator.nullsLast(Comparator.naturalOrder())));
        Comparator<ExcelBatchDto> prepComparator = Comparator.nullsLast((ExcelBatchDto o1, ExcelBatchDto o2) -> {
            if (o1.getPrep() == null || o2.getPrep() == null) {
                return 0;
            }
            String prep1 = o1.getPrep();
            String prep2 = o2.getPrep();
            if (prep1.length() == prep2.length()) {
                return prep1.compareTo(prep2);
            }
            if (prep1.length() > prep2.length()) {
                return 1;
            } else {
                return -1;
            }
        }).reversed();

        Comparator<ExcelBatchDto> commonComparator = Comparator.nullsLast(Comparator.comparing(ExcelBatchDto::getDepartment,
                Comparator.nullsLast(Comparator.naturalOrder())))
            .thenComparing(Comparator.nullsLast(Comparator.comparing(ExcelBatchDto::getSubCategory,
                Comparator.nullsLast(Comparator.naturalOrder()))))
            .thenComparing(Comparator.nullsLast(Comparator.comparing(ExcelBatchDto::getItemDescription,
                Comparator.nullsLast(Comparator.naturalOrder()))));
        if (source.equals(GeneratedDocNameEnum.DOWNEY.getValue())) {
            list.sort(fromComparator.thenComparing(prepComparator).thenComparing(commonComparator));
        }
        if (source.equals(GeneratedDocNameEnum.COSTCO.getValue())) {
            list.sort(fromComparator.thenComparing(commonComparator));
        }
    }

}
