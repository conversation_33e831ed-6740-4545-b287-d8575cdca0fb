package com.mercaso.wms.application.dto.view;

import com.mercaso.wms.application.dto.BaseDto;
import com.mercaso.wms.domain.crossdockitem.enums.CrossDockItemSourceEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SearchCrossDockTaskItemView extends BaseDto {

    private UUID id;

    private UUID crossDockTaskId;

    private UUID batchId;

    private String skuNumber;

    private String title;

    private UUID itemId;

    private UUID taskItemId;

    private CrossDockItemSourceEnum source;

    private UUID shippingOrderId;

    private UUID shippingOrderItemId;

    private List<String> sequence;

    private Integer pickedQty;

    private Integer crossDockedQty;

    private String breakdownName;

    private String orderNumber;

    private Integer line;

    private Instant createdAt;

    private Instant updatedAt;
}
