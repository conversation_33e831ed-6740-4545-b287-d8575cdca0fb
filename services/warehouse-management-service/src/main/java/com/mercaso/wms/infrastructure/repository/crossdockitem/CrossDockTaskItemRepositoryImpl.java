package com.mercaso.wms.infrastructure.repository.crossdockitem;

import com.mercaso.wms.domain.crossdockitem.CrossDockTaskItem;
import com.mercaso.wms.domain.crossdockitem.CrossDockTaskItemRepository;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.repository.crossdockitem.jpa.CrossDockTaskItemJpaDao;
import com.mercaso.wms.infrastructure.repository.crossdockitem.jpa.dataobject.CrossDockTaskItemDo;
import com.mercaso.wms.infrastructure.repository.crossdockitem.jpa.mapper.CrossDockTaskItemMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class CrossDockTaskItemRepositoryImpl implements CrossDockTaskItemRepository {
    private final CrossDockTaskItemMapper mapper;

    private final CrossDockTaskItemJpaDao jpaDao;

    private final CrossDockTaskItemJdbcTemplate crossDockTaskItemJdbcTemplate;

    @Override
    public CrossDockTaskItem save(CrossDockTaskItem item) {
        CrossDockTaskItemDo save = jpaDao.save(mapper.domainToDo(item));
        return mapper.doToDomain(save);
    }

    @Override
    public CrossDockTaskItem findById(UUID id) {
        Optional<CrossDockTaskItemDo> byId = jpaDao.findById(id);
        return byId.map(mapper::doToDomain).orElse(null);
    }

    @Override
    public CrossDockTaskItem update(CrossDockTaskItem domain) {
        CrossDockTaskItemDo dataObject = jpaDao.findById(domain.getId()).orElse(null);
        if (dataObject == null) {
            throw new WmsBusinessException("CrossDockTaskItem not found.");
        }
        CrossDockTaskItemDo target = mapper.domainToDo(domain);
        List<String> ignoreProperties = new ArrayList<>(List.of("createdBy", "createdAt"));
        BeanUtils.copyProperties(target, dataObject, ignoreProperties.toArray(new String[0]));
        dataObject = jpaDao.save(dataObject);
        return mapper.doToDomain(dataObject);
    }

    @Override
    public List<CrossDockTaskItem> findByTaskItemId(UUID taskItemId) {
        List<CrossDockTaskItemDo> dataObjects = jpaDao.findByTaskItemId(taskItemId);
        return mapper.doToDomains(dataObjects);
    }

    @Override
    public void deleteByIds(List<UUID> ids) {
        List<CrossDockTaskItemDo> allById = jpaDao.findAllById(ids);
        if (allById.isEmpty()) {
            log.warn("No CrossDockTaskItems found for the provided IDs: {}", ids);
            return;
        }
        allById.forEach(item -> item.setDeletedBy("system"));
        jpaDao.deleteAll(allById);
    }

    @Override
    public void deleteAll() {
        jpaDao.deleteAll();
    }

    @Override
    public void deleteByTaskItemIds(List<UUID> taskItemIds) {
        if (CollectionUtils.isEmpty(taskItemIds)) {
            log.warn("No taskItemIds provided for batch delete.");
            return;
        }
        List<CrossDockTaskItemDo> items = jpaDao.findByTaskItemIdIn(taskItemIds);
        if (items.isEmpty()) {
            log.warn("No CrossDockTaskItems found for the provided taskItemIds: {}", taskItemIds);
            return;
        }
        jpaDao.deleteAll(items);
    }

    public List<CrossDockTaskItem> saveAll(List<CrossDockTaskItem> items) {
        List<CrossDockTaskItemDo> dos = items.stream().map(mapper::domainToDo).toList();
        List<CrossDockTaskItemDo> saved = jpaDao.saveAll(dos);
        return mapper.doToDomains(saved);
    }
}