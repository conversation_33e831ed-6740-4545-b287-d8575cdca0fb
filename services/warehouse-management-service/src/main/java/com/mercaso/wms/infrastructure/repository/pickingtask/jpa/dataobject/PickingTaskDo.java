package com.mercaso.wms.infrastructure.repository.pickingtask.jpa.dataobject;

import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.infrastructure.annotation.NumberGenerator;
import com.mercaso.wms.infrastructure.repository.BaseDo;
import com.mercaso.wms.infrastructure.repository.pickingtaskitem.jpa.dataobject.PickingTaskItemDo;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(exclude = "pickingTaskItems")
@Table(name = "picking_task")
@SQLDelete(sql = "update picking_task set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
public class PickingTaskDo extends BaseDo {

    @Column(name = "batch_id", nullable = false)
    private UUID batchId;

    @NumberGenerator
    private String number;

    @Column(name = "status", nullable = false, length = 20)
    @Enumerated(value = EnumType.STRING)
    private PickingTaskStatus status;

    @Column(name = "type")
    @Enumerated(value = EnumType.STRING)
    private PickingTaskType type;

    @Column(name = "picking_start_time")
    private Instant pickingStartTime;

    @Column(name = "picked_time")
    private Instant pickedTime;

    @Column(name = "picker_user_id")
    private UUID pickerUserId;

    @Column(name = "picker_user_name", length = 50)
    private String pickerUserName;

    @Column(name = "source")
    @Enumerated(value = EnumType.STRING)
    private SourceEnum source;

    @Column(name = "created_user_name")
    private String createdUserName;

    @OneToMany(mappedBy = "pickingTask", fetch = FetchType.EAGER, cascade = CascadeType.ALL)
    @OrderBy("pickingSequence ASC")
    private List<PickingTaskItemDo> pickingTaskItems;
}