package com.mercaso.wms.application.query;

import com.mercaso.wms.domain.crossdockitem.enums.CrossDockItemStatusEnum;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CrossDockTaskItemQuery {
    private String sku;

    private String deliveryDate;

    private UUID pickingTaskItemId;

    private UUID receivingTaskItemId;

    private CrossDockItemStatusEnum status;

    @Min(value = 1, message = "Page size must be greater than 0")
    private Integer page;

    @Min(value = 1, message = "Page size must be greater than 0")
    @Max(value = 1000, message = "Page size must be less than or equal to 1000")
    private Integer pageSize;
} 