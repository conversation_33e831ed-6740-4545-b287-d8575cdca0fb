package com.mercaso.wms.application.dto;

import com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PickingTaskDto extends BaseDto {

    private UUID id;

    private UUID batchId;

    private String number;

    private Integer orderQty;

    private PickingTaskType type;

    private PickingTaskStatus status;

    private Instant pickingStartTime;

    private Instant pickedTime;

    private UUID pickerUserId;

    private String pickerUserName;

    private String source;

    private String lastModifiedBy;

    private List<PickingTaskItemDto> pickingTaskItems;

    private Instant createdAt;

    private String createdBy;

    private Instant updatedAt;

    private String updatedBy;

    private LocalDate deliveryDate;

}
