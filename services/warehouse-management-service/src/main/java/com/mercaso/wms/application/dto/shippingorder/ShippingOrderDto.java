package com.mercaso.wms.application.dto.shippingorder;

import com.mercaso.wms.application.dto.BaseDto;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.shippingorder.enums.ShippingOrderStatus;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ShippingOrderDto extends BaseDto {

    private UUID id;

    private UUID batchId;

    private CustomerAddressDto customerAddress;

    private String fulfillmentStatus;

    private String orderNumber;

    private Instant orderDate;

    private String deliveryDate;

    private Instant shippedDate;

    private ShippingOrderStatus status;

    private Location breakdownLocation;

    private List<ShippingOrderItemDto> shippingOrderItems;

}
