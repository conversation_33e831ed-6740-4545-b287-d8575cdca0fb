package com.mercaso.wms.infrastructure.contant;

public class FeatureFlagKeys {

    private FeatureFlagKeys() {
    }

    public static final String SYNC_SUBLOCATIONS_FROM_FINALE = "sync_sublocations_from_finale";

    public static final String SYNC_INVENTORY_FROM_FINALE = "sync_inventory_from_finale";

    public static final String USE_FINALE_TRANSFER_FEATURE = "use_finale_transfer_feature";

    public static final String SEND_EXECUTION_EVENTS_TO_RM = "send_execution_events_to_rm";

    public static final String USE_FINALE_PURCHASE_ORDER_FEATURE = "use_finale_purchase_order_feature";

    public static final String TRIGGER_DELIVERY_DETAILS_WORKFLOW = "trigger_delivery_details_workflow";

    public static final String SEND_INVOICE_EMAIL_TO_CUSTOMER = "send_invoice_email_to_customer";

    public static final String USE_NEW_MFC_BATCH_LEVEL_TASK_LOGIC = "use_new_mfc_batch_level_task_logic";

    public static final String TRANSFER_LOST_FOUND_INVENTORY = "transfer_lost_found_inventory";

}
