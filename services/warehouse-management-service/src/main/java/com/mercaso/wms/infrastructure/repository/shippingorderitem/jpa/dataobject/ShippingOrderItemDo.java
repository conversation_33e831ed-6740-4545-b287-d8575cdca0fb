package com.mercaso.wms.infrastructure.repository.shippingorderitem.jpa.dataobject;

import com.mercaso.wms.infrastructure.repository.BaseDo;
import com.mercaso.wms.infrastructure.repository.shippingorder.jpa.dataobject.ShippingOrderDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@ToString()
@Table(name = "shipping_order_items")
@SQLDelete(sql = "update shipping_order_items set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
public class ShippingOrderItemDo extends BaseDo {

    @JoinColumn(name = "shipping_order_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    private ShippingOrderDo shippingOrder;

    @Column(name = "shopify_order_item_id", nullable = false)
    private String shopifyOrderItemId;

    @Column(name = "item_id")
    private UUID itemId;

    @Column(name = "sku_number")
    private String skuNumber;

    @Column(name = "title")
    private String title;

    @Column(name = "qty")
    private Integer qty;

    @Column(name = "picked_qty")
    private Integer pickedQty;

    @Column(name = "fulfilled_qty")
    private Integer fulfilledQty;

    @Column(name = "line")
    private Integer line;

    @Column(name = "department")
    private String department;

    @Column(name = "category")
    private String category;

    @Column(name = "sub_category")
    private String subCategory;

    @Column(name = "picked")
    private Boolean picked;

    @Column(name = "primary_vendor_id")
    private UUID primaryVendorId;

    @Column(name = "backup_vendor_id")
    private UUID backupVendorId;

    @Column(name = "primary_vendor_name")
    private String primaryVendorName;

    @Column(name = "backup_vendor_name")
    private String backupVendorName;

    @Column(name = "version_number")
    private Integer versionNumber;

}