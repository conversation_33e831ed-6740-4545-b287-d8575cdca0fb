package com.mercaso.wms.infrastructure.repository.shippingorder.jpa;

import com.mercaso.wms.infrastructure.repository.shippingorder.criteria.ShippingOrderSearchCriteria;
import com.mercaso.wms.infrastructure.repository.shippingorder.jpa.dataobject.ShippingOrderDo;
import java.util.List;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface ShippingOrderJpaDao extends JpaRepository<ShippingOrderDo, UUID> {

    ShippingOrderDo findByOrderNumber(String orderNumber);

    List<ShippingOrderDo> findByOrderNumberIn(List<String> orderNumbers);

    @Query("select so from ShippingOrderDo so "
        + "where (:#{#criteria.orderNumber} is null or so.orderNumber like %:#{#criteria.orderNumber}%) "
        + "and (:#{#criteria.statuses} is null or so.status IN :#{#criteria.statuses}) "
        + "and (:#{#criteria.deliveryDate} is null or so.deliveryDate = :#{#criteria.deliveryDate}) ")
    Page<ShippingOrderDo> findShippingOrderList(@Param("criteria") ShippingOrderSearchCriteria criteria,
        Pageable pageable);

    @Query("select so from ShippingOrderDo so "
        + " where so.deliveryDate = :deliveryDate "
        + " and so.status = 'OPEN' ")
    List<ShippingOrderDo> findActiveShippingOrdersByDeliveryDate(String deliveryDate);

    List<ShippingOrderDo> findByBatchId(UUID batchId);

    ShippingOrderDo findByOrderNumberAndShopifyOrderId(String orderNumber, String shopifyOrderId);

    @Query(value = "select so.* from shipping_order so "
        + " where so.delivery_date >= :deliveryDate "
        + " and so.breakdown_location_id is not null ", nativeQuery = true)
    List<ShippingOrderDo> findRescheduledShippingOrders(String deliveryDate);

}
