package com.mercaso.wms.delivery.application.queryservice;

import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderDto;
import com.mercaso.wms.delivery.application.mapper.deliveryorder.DeliveryOrderDtoApplicationMapper;
import com.mercaso.wms.delivery.application.mapper.gps.LatestGpsCacheDtoApplicationMapper;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrderRepository;
import com.mercaso.wms.delivery.domain.latestgps.LatestGpsCacheRepository;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class DeliveryOrderQueryService {

    private final DeliveryOrderRepository deliveryOrderRepository;

    private final DeliveryOrderDtoApplicationMapper deliveryOrderDtoApplicationMapper;

    private final LatestGpsCacheRepository latestGpsCacheRepository;

    private final LatestGpsCacheDtoApplicationMapper latestGpsCacheDtoApplicationMapper;

    @Value("${shopify.admin-order-url}")
    private String shopifyOrderUrl;

    public DeliveryOrderDto findById(UUID id) {
        return deliveryOrderDtoApplicationMapper.domainToDto(deliveryOrderRepository.findById(id), shopifyOrderUrl);
    }

    public DeliveryOrderDto findByOrderNumber(String orderNumber) {
        DeliveryOrderDto deliveryOrderDto = deliveryOrderDtoApplicationMapper.domainToDto(deliveryOrderRepository.findByOrderNumber(
            orderNumber), shopifyOrderUrl);
        if (deliveryOrderDto == null) {
            log.warn("Delivery order not found for order number: {}", orderNumber);
            return null;
        }
        latestGpsCacheRepository.findByDeliveryOrderId(deliveryOrderDto.getId())
            .ifPresent(latestGpsCache -> deliveryOrderDto.setLatestGps(latestGpsCacheDtoApplicationMapper.domainToDto(
                latestGpsCache)));
        return deliveryOrderDto;
    }

    public List<DeliveryOrderDto> findByOrderNumbers(List<String> orderNumbers) {
        if (orderNumbers == null || orderNumbers.isEmpty()) {
            return List.of();
        }
        List<DeliveryOrderDto> deliveryOrderDtos = deliveryOrderDtoApplicationMapper.domainToDtos(
            deliveryOrderRepository.findAllByOrderNumberIn(orderNumbers));
        if (CollectionUtils.isNotEmpty(deliveryOrderDtos)) {
            List<UUID> deliveryOrderIds = deliveryOrderDtos.stream()
                .map(DeliveryOrderDto::getId)
                .toList();
            latestGpsCacheRepository.findAllByDeliveryOrderIdIn(deliveryOrderIds)
                .forEach(latestGpsCache -> deliveryOrderDtos.stream()
                    .filter(dto -> dto.getId().equals(latestGpsCache.getDeliveryOrderId()))
                    .findFirst()
                    .ifPresent(dto -> dto.setLatestGps(
                        latestGpsCacheDtoApplicationMapper.domainToDto(latestGpsCache))));
        }
        return deliveryOrderDtos;
    }

}
