package com.mercaso.wms.application.query;

import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@SuperBuilder
public class PickingTaskQuery {

    private UUID pickerUserId;

    private List<String> numbers;

    private SourceEnum source;

    private PickingTaskStatus[] statuses;

    private LocalDate deliveryDate;

    private List<String> orderNumbers;

    private PickingTaskType type;

    private List<String> departments;

    private List<String> aisleNumbers;

    private List<String> categories;

    private String breakdownName;

    private List<String> skuNumbers;

}
