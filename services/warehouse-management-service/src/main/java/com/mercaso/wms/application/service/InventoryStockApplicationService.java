package com.mercaso.wms.application.service;

import com.mercaso.businessevents.dispatcher.BusinessEventDispatcher;
import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.client.dto.ItemCategoryDto;
import com.mercaso.wms.application.command.inventorystock.CreateInventoryStockCommand;
import com.mercaso.wms.application.dto.event.InventoryStockAvailablePayloadDto;
import com.mercaso.wms.application.dto.event.InventoryStockCreatedPayloadDto;
import com.mercaso.wms.application.dto.event.InventoryStockUnavailablePayloadDto;
import com.mercaso.wms.application.dto.inventory.InventoryStockDto;
import com.mercaso.wms.application.mapper.inventorystock.InventoryStockDtoApplicationMapper;
import com.mercaso.wms.domain.inventorystock.InventoryStock;
import com.mercaso.wms.domain.inventorystock.InventoryStockRepository;
import com.mercaso.wms.domain.inventorystockhistory.InventoryStockHistory;
import com.mercaso.wms.domain.inventorystockhistory.InventoryStockHistoryRepository;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.location.LocationRepository;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.domain.warehouse.WarehouseRepository;
import com.mercaso.wms.infrastructure.cache.LocationCache;
import com.mercaso.wms.infrastructure.config.PgAdvisoryLock;
import com.mercaso.wms.infrastructure.contant.FeatureFlagKeys;
import com.mercaso.wms.infrastructure.event.BusinessEventFactory;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.external.finale.FinaleProductService;
import com.mercaso.wms.infrastructure.external.finale.config.FinaleConfigProperties;
import com.mercaso.wms.infrastructure.external.finale.dto.FinaleAvailableStockDto;
import com.mercaso.wms.infrastructure.external.finale.dto.FinaleAvailableStockItemsOnHandDto;
import com.mercaso.wms.infrastructure.external.ims.ImsAdaptor;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
@Transactional
public class InventoryStockApplicationService {

    private final InventoryStockRepository inventoryStockRepository;
    private final WarehouseRepository warehouseRepository;
    private final LocationRepository locationRepository;
    private final BusinessEventDispatcher businessEventDispatcher;
    private final InventoryStockHistoryRepository inventoryStockHistoryRepository;
    private final ImsAdaptor imsAdaptor;
    private final FinaleProductService finaleProductService;
    private final LocationCache locationCache;
    private final PgAdvisoryLock pgAdvisoryLock;
    private final FeatureFlagsManager featureFlagsManager;
    private static final String LOCK_KEY = "InventoryStockApplicationService.syncInventoryStocks";
    private final FinaleConfigProperties finaleConfigProperties;

    public InventoryStockApplicationService(InventoryStockRepository inventoryStockRepository,
        WarehouseRepository warehouseRepository,
        LocationRepository locationRepository,
        BusinessEventDispatcher businessEventDispatcher,
        InventoryStockHistoryRepository inventoryStockHistoryRepository,
        ImsAdaptor imsAdaptor,
        FinaleProductService finaleProductService,
        LocationCache locationCache,
        PgAdvisoryLock pgAdvisoryLock,
        FeatureFlagsManager featureFlagsManager, FinaleConfigProperties finaleConfigProperties) {
        this.inventoryStockRepository = inventoryStockRepository;
        this.warehouseRepository = warehouseRepository;
        this.locationRepository = locationRepository;
        this.businessEventDispatcher = businessEventDispatcher;
        this.inventoryStockHistoryRepository = inventoryStockHistoryRepository;
        this.imsAdaptor = imsAdaptor;
        this.finaleProductService = finaleProductService;
        this.locationCache = locationCache;
        this.pgAdvisoryLock = pgAdvisoryLock;
        this.featureFlagsManager = featureFlagsManager;
        this.finaleConfigProperties = finaleConfigProperties;
    }

    public InventoryStockDto create(CreateInventoryStockCommand command) {
        Warehouse warehouse = warehouseRepository.findById(command.getWarehouseId());
        if (warehouse == null) {
            throw new WmsBusinessException("Warehouse not found");
        }
        Location location = locationRepository.findById(command.getLocationId());
        if (location == null) {
            throw new WmsBusinessException("Location not found");
        }
        InventoryStock inventoryStock = inventoryStockRepository.save(InventoryStock.builder()
            .build()
            .create(command, warehouse, location));

        InventoryStockDto inventoryStockDto = InventoryStockDtoApplicationMapper.INSTANCE.domainToDto(inventoryStock);
        businessEventDispatcher.dispatch(BusinessEventFactory.build(InventoryStockCreatedPayloadDto.builder()
            .inventoryStockId(inventoryStockDto.getId())
            .data(inventoryStockDto)
            .build()));
        inventoryStockHistoryRepository.save(InventoryStockHistory.builder().build().receive(inventoryStock));
        return inventoryStockDto;
    }

    public InventoryStockDto available(UUID inventoryStockId) {
        InventoryStock inventoryStock = inventoryStockRepository.findById(inventoryStockId);
        if (inventoryStock == null) {
            throw new WmsBusinessException("Inventory stock not found");
        }
        inventoryStock.available();
        InventoryStock saved = inventoryStockRepository.save(inventoryStock);
        InventoryStockDto inventoryStockDto = InventoryStockDtoApplicationMapper.INSTANCE.domainToDto(saved);
        businessEventDispatcher.dispatch(BusinessEventFactory.build(InventoryStockAvailablePayloadDto.builder()
            .inventoryStockId(inventoryStockDto.getId())
            .data(inventoryStockDto)
            .build()));
        return inventoryStockDto;
    }

    public InventoryStockDto unavailable(UUID inventoryStockId) {
        InventoryStock inventoryStock = inventoryStockRepository.findById(inventoryStockId);
        if (inventoryStock == null) {
            throw new WmsBusinessException("Inventory stock not found");
        }
        inventoryStock.unavailable();
        InventoryStock saved = inventoryStockRepository.save(inventoryStock);
        InventoryStockDto inventoryStockDto = InventoryStockDtoApplicationMapper.INSTANCE.domainToDto(saved);
        businessEventDispatcher.dispatch(BusinessEventFactory.build(InventoryStockUnavailablePayloadDto.builder()
            .inventoryStockId(inventoryStockDto.getId())
            .data(inventoryStockDto)
            .build()));
        return inventoryStockDto;
    }

    @Scheduled(fixedRate = 300000) // Executes every 5 minutes (300,000 ms)
    public void syncInventoryStocks() {
        if (!featureFlagsManager.isFeatureOn(FeatureFlagKeys.SYNC_INVENTORY_FROM_FINALE)) {
            return;
        }
        try {
            if (!acquireLock()) {
                return;
            }

            log.debug("[syncInventoryStocks] Start syncing inventory stocks");
            Map<String, Location> locationMap = getLocationMap();
            Map<UUID, InventoryStock> existingStocks = getExistingStocks();
            processAvailableStocks(locationMap, existingStocks);

            log.info("[syncInventoryStocks] Finish syncing inventory stocks");
        } finally {
            pgAdvisoryLock.unLock(LOCK_KEY.hashCode());
        }

    }

    private void deleteRemovedStocks(Map<UUID, InventoryStock> existingStocks) {
        if (!existingStocks.isEmpty()) {
            List<InventoryStock> toDelete = new ArrayList<>(existingStocks.values());
            toDelete.forEach(InventoryStock::delete);
            inventoryStockRepository.saveAll(toDelete);
            log.info("[syncInventoryStocks] Deleted inventory stocks size: {}", toDelete.size());
        }
    }

    private void createNewStocks(List<InventoryStock> needCreateStocks) {
        if (!needCreateStocks.isEmpty()) {
            Set<String> skuNumbers = needCreateStocks.stream()
                .map(inventoryStock -> inventoryStock.getItem().getSkuNumber())
                .collect(Collectors.toSet());

            Map<String, ItemCategoryDto> skuToItemMap = imsAdaptor.getItemsBySkus(new ArrayList<>(skuNumbers)).stream()
                .collect(Collectors.toMap(ItemCategoryDto::getSkuNumber,
                    Function.identity(),
                    (existing, replacement) -> existing));

            List<InventoryStock> toSave = needCreateStocks.stream()
                .filter(inventoryStock -> {
                    ItemCategoryDto itemCategory = skuToItemMap.get(inventoryStock.getItem().getSkuNumber());
                    if (itemCategory != null) {
                        inventoryStock.getItem().setTitle(itemCategory.getTitle());
                        inventoryStock.getItem().setId(itemCategory.getId());
                        return true;
                    }
                    return false;
                })
                .toList();

            if (!toSave.isEmpty()) {
                inventoryStockRepository.saveAll(toSave);
                log.info("[syncInventoryStocks] Created inventory stocks size: {}", toSave.size());
            }
        }
    }

    private boolean acquireLock() {
        Boolean isAcquired = pgAdvisoryLock.tryLockWithSessionLevel(LOCK_KEY.hashCode());
        return isAcquired != null && isAcquired;
    }

    private Map<String, Location> getLocationMap() {
        return locationCache.getLocationMap().values().stream()
            .collect(Collectors.toMap(Location::getName, Function.identity(), (existing, replacement) -> existing));
    }

    private Map<UUID, InventoryStock> getExistingStocks() {
        List<InventoryStock> stocks = inventoryStockRepository.findAll();
        log.info("[syncInventoryStocks] Inventory stocks size: {}", stocks.size());

        Map<UUID, InventoryStock> stockMap = new LinkedHashMap<>();
        stocks.forEach(stock -> stockMap.put(stock.getId(), stock));
        return stockMap;
    }

    private void processAvailableStocks(
        Map<String, Location> locationMap,
        Map<UUID, InventoryStock> existingStocks) {

        List<FinaleAvailableStockDto> availableStocks = finaleProductService.getAvailableStock(10000);
        if (CollectionUtils.isEmpty(availableStocks)) {
            log.warn("[syncInventoryStocks] No available stocks returned from Finale. Skipping sync to prevent data loss.");
            return;
        }

        List<InventoryStock> needCreateStocks = Lists.newArrayList();
        List<InventoryStock> needUpdateStocks = Lists.newArrayList();
        availableStocks.forEach(availableStock ->
            processStockItemsOnHand(availableStock, locationMap, existingStocks, needCreateStocks, needUpdateStocks));

        updateStocks(needUpdateStocks);
        createNewStocks(needCreateStocks);
        deleteRemovedStocks(existingStocks);
    }

    private void updateStocks(List<InventoryStock> needUpdateStocks) {
        if (!needUpdateStocks.isEmpty()) {
            inventoryStockRepository.saveAll(needUpdateStocks);
            log.info("[syncInventoryStocks] Update inventory stocks size: {}", needUpdateStocks.size());
        }
    }

    private void processStockItemsOnHand(
        FinaleAvailableStockDto availableStock,
        Map<String, Location> locationMap,
        Map<UUID, InventoryStock> existingStocks,
        List<InventoryStock> needCreateStocks,
        List<InventoryStock> needUpdateStocks) {

        availableStock.getStockItemsOnHand().forEach(itemOnHand -> {
            try {
                processItemOnHand(itemOnHand,
                    availableStock.getSku(),
                    locationMap,
                    existingStocks,
                    needCreateStocks,
                    needUpdateStocks);
            } catch (Exception e) {
                log.warn("[syncInventoryStocks] Error while syncing inventory stock", e);
            }
        });
    }

    private void processItemOnHand(
        FinaleAvailableStockItemsOnHandDto itemOnHand,
        String sku,
        Map<String, Location> locationMap,
        Map<UUID, InventoryStock> existingStocks,
        List<InventoryStock> needCreateStocks,
        List<InventoryStock> needUpdateStocks) {

        FinaleAvailableStockItemsOnHandDto.Location subLocation = itemOnHand.getSubLocation();
        if (subLocation == null || "Main".equals(subLocation.getName())
            || finaleConfigProperties.getExcludeSyncInventoryStocksLocations().contains(subLocation.getName())) {
            return;
        }
        Location location = locationMap.get(subLocation.getName());
        if (location == null) {
            log.debug("[syncInventoryStocks] Location not found: {}", subLocation.getName());
            return;
        }
        Optional<InventoryStock> stockOptional = existingStocks.values()
            .stream()
            .filter(stock -> stock.getItem().getSkuNumber().equals(sku))
            .filter(stock -> stock.getLocation().getId().equals(location.getId()))
            .findFirst();
        if (stockOptional.isPresent()) {
            itemOnHand.setQuantityOnHand(calculateQuantityOnHand(itemOnHand.getQuantityOnHand(), itemOnHand.getPacking()));
            InventoryStock inventoryStock = stockOptional.get();
            if (inventoryStock.getQty().compareTo(BigDecimal.valueOf(itemOnHand.getQuantityOnHand())) != 0) {
                inventoryStock.update(itemOnHand);
                needUpdateStocks.add(inventoryStock);
            }
            existingStocks.remove(inventoryStock.getId());
        } else {
            needCreateStocks.add(InventoryStock.builder()
                .build()
                .create(itemOnHand, sku, location.getWarehouse(), location));
        }
    }

    private Long calculateQuantityOnHand(Long quantityOnHand, String packing) {
        if (StringUtils.isEmpty(packing)) {
            return quantityOnHand;
        }
        Long packRate = extractPackRate(packing);

        return packRate != null && packRate > 0 ? quantityOnHand * packRate : quantityOnHand;
    }

    private Long extractPackRate(String input) {
        if (input == null || !input.contains("/")) {
            return null;
        }

        String beforeSlash = input.substring(0, input.indexOf("/")).trim();

        String[] parts = beforeSlash.split("\\s+");
        String possibleNumber = parts[parts.length - 1];

        for (char c : possibleNumber.toCharArray()) {
            if (!Character.isDigit(c)) {
                log.warn("[syncInventoryStocks] Invalid pack number: {}", input);
                return null;
            }
        }
        try {
            return Long.parseLong(possibleNumber);
        } catch (NumberFormatException e) {
            log.warn("[syncInventoryStocks] Error while extracting pack rate", e);
            return null;
        }
    }

}
