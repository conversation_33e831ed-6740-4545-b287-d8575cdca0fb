package com.mercaso.wms.delivery.domain.deliveryorder;

import static com.mercaso.wms.infrastructure.utils.DateUtils.secondsSinceMidnight;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mercaso.wms.delivery.application.command.deliveryorder.DeliveryOrderDeliveredCommand;
import com.mercaso.wms.delivery.application.command.deliveryorder.UpdateDeliveryOrderCommand;
import com.mercaso.wms.delivery.application.command.deliveryorder.UpdateDeliveryOrderItemCommand;
import com.mercaso.wms.delivery.application.dto.deliveryorder.dto.DiscountAllocation;
import com.mercaso.wms.delivery.application.dto.deliveryorder.dto.DiscountApplication;
import com.mercaso.wms.delivery.application.dto.shopify.ShopifyOrderForDeliveryDto;
import com.mercaso.wms.delivery.application.dto.shopify.ShopifyOrderForDeliveryDto.DiscountApplicationDto;
import com.mercaso.wms.delivery.application.dto.shopify.ShopifyOrderForDeliveryDto.ShippingAddressDto;
import com.mercaso.wms.delivery.application.dto.shopify.ShopifyOrderForDeliveryDto.ShopifyLineItemDto;
import com.mercaso.wms.delivery.domain.customer.Customer;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.DeliveryOrderStatus;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.DeliveryOrderTransitionEvents;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.PaymentType;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.RescheduleType;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTask;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.Step;
import com.mercaso.wms.infrastructure.statemachine.BaseStateMachine;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Configurable;

@Data
@ToString
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@Configurable(preConstruction = true)
@Slf4j
public class DeliveryOrder extends BaseStateMachine<DeliveryOrder, DeliveryOrderStatus, DeliveryOrderTransitionEvents> {

    private final UUID id;
    private UUID warehouseId;
    private UUID deliveryTaskId;
    private String shopifyOrderId;
    private String orderNumber;
    private String deliveryDate;
    private String deliveryTimeWindow;
    private Address address;
    private Customer customer;
    private List<PaymentType> paymentType;
    private String paymentStatus;
    private String fulfillmentStatus;
    private Integer sequence;
    private Instant planArriveAt;
    private Instant planDeliveryAt;
    private Instant inTransitAt;
    private Instant arrivedAt;
    private Instant unloadedAt;
    private Instant deliveredAt;
    private String customerNotes;
    private String notes;
    private BigDecimal originalTotalPrice;
    private BigDecimal totalPrice;
    private BigDecimal currentTotalDiscounts;
    private String discountApplications;
    private RescheduleType rescheduleType;
    private List<DeliveryOrderItem> deliveryOrderItems;
    private String rmOrderId;

    public DeliveryOrder update(UpdateDeliveryOrderCommand command) {
        if (command.getRescheduleType() != null) {
            this.rescheduleType = command.getRescheduleType();
        }
        if (CollectionUtils.isNotEmpty(command.getUpdateDeliveryOrderItemDtos())) {
            this.updateDeliveredItems(command.getUpdateDeliveryOrderItemDtos());
        }
        return this;
    }

    public DeliveryOrder create(ShopifyOrderForDeliveryDto shopifyOrderDto) {
        this.address = convertAddress(shopifyOrderDto.getShippingAddress());
        this.shopifyOrderId = shopifyOrderDto.getId();
        this.orderNumber = shopifyOrderDto.getName();
        this.fulfillmentStatus = shopifyOrderDto.getFulfillmentStatus();
        this.paymentStatus = shopifyOrderDto.getFinancialStatus();
        this.customerNotes = shopifyOrderDto.getNote();
        this.deliveryDate = convertDeliveryDate(shopifyOrderDto.getTags());
        this.deliveryTimeWindow = convertDeliveryTimeWindow(shopifyOrderDto.getTags());
        this.originalTotalPrice = shopifyOrderDto.getTotalPrice();
        this.currentTotalDiscounts = shopifyOrderDto.getCurrentTotalDiscounts();
        convertDiscountApplication(shopifyOrderDto);

        if (shopifyOrderDto.getCancelledAt() != null) {
            this.setState(DeliveryOrderStatus.CANCELED);
        } else {
            this.setState(DeliveryOrderStatus.CREATED);
        }
        this.deliveryOrderItems = convertDeliveryOrderItem(shopifyOrderDto.getLineItems(),
            shopifyOrderDto.getDiscountApplications());
        return this;
    }

    private void convertDiscountApplication(ShopifyOrderForDeliveryDto shopifyOrderDto) {
        if (CollectionUtils.isEmpty(shopifyOrderDto.getDiscountApplications())) {
            return;
        }
        List<DiscountApplication> copyDiscountApplications = Lists.newArrayList();
        shopifyOrderDto.getDiscountApplications().forEach(
            applicationDto -> copyDiscountApplications.add(
                DiscountApplication.builder()
                    .type(applicationDto.getType())
                    .code(applicationDto.getCode())
                    .valueType(applicationDto.getValueType())
                    .value(applicationDto.getValue())
                    .targetType(applicationDto.getTargetType())
                    .build()
            ));
        this.discountApplications = SerializationUtils.serialize(copyDiscountApplications);
    }

    public Address convertAddress(ShippingAddressDto addressDto) {
        if (addressDto == null) {
            return null;
        }
        return Address.builder()
            .addressOne(addressDto.getAddress1())
            .addressTwo(addressDto.getAddress2())
            .city(addressDto.getCity())
            .state(addressDto.getProvince())
            .country(addressDto.getCountry())
            .name(addressDto.getName())
            .firstName(addressDto.getFirstName())
            .lastName(addressDto.getLastName())
            .latitude(addressDto.getLatitude())
            .longitude(addressDto.getLongitude())
            .postalCode(addressDto.getZip())
            .phone(addressDto.getPhone())
            .build();
    }

    public List<DeliveryOrderItem> convertDeliveryOrderItem(List<ShopifyLineItemDto> lineItems,
        List<DiscountApplicationDto> discountApplications) {
        if (CollectionUtils.isEmpty(lineItems)) {
            return Collections.emptyList();
        }
        List<DeliveryOrderItem> items = Lists.newArrayList();
        for (int i = 0; i < lineItems.size(); i++) {
            ShopifyLineItemDto shopifyLineItemDto = lineItems.get(i);
            items.add(DeliveryOrderItem.builder()
                .skuNumber(shopifyLineItemDto.getSku())
                .qty(BigDecimal.valueOf(shopifyLineItemDto.getQuantity()))
                .currentQty(BigDecimal.valueOf(shopifyLineItemDto.getCurrentQuantity()))
                .title(shopifyLineItemDto.getTitle())
                .shopifyOrderItemId(shopifyLineItemDto.getId())
                .price(shopifyLineItemDto.getPrice())
                .discountAllocations(convertDiscountAllocations(shopifyLineItemDto, discountApplications))
                .line(i + 1)
                .build());
        }
        return items;
    }

    private String convertDiscountAllocations(ShopifyLineItemDto shopifyLineItemDto,
        List<DiscountApplicationDto> discountApplications) {
        if (CollectionUtils.isEmpty(shopifyLineItemDto.getDiscountAllocations())) {
            return null;
        }
        List<DiscountAllocation> copyDiscountAllocations = Lists.newArrayList();
        shopifyLineItemDto.getDiscountAllocations().forEach(
            applicationDto -> copyDiscountAllocations.add(
                DiscountAllocation.builder()
                    .amount(applicationDto.getAmount())
                    .discountApplicationIndex(applicationDto.getDiscountApplicationIndex())
                    .valueType(CollectionUtils.isNotEmpty(discountApplications)
                        && discountApplications.get(applicationDto.getDiscountApplicationIndex()) != null ?
                        discountApplications.get(applicationDto.getDiscountApplicationIndex()).getValueType() : null)
                    .build()
            ));
        return SerializationUtils.serialize(copyDiscountAllocations);
    }

    public void updateDeliveredItems(List<UpdateDeliveryOrderItemCommand> updateDeliveryOrderItemCommands) {
        deliveryOrderItems.forEach(deliveryOrderItem -> updateDeliveryOrderItemCommands
            .forEach(updateDeliveryOrderItemDto -> {
                if (deliveryOrderItem.getId().equals(updateDeliveryOrderItemDto.getId())) {
                    deliveryOrderItem.setDeliveredQty(updateDeliveryOrderItemDto.getDeliveredQty());
                    if (deliveryOrderItem.getDeliveredQty().compareTo(deliveryOrderItem.getCurrentQty()) > 0) {
                        log.warn("Delivery Order {} {} delivered quantity {} is greater than the original quantity {}.",
                            getOrderNumber(),
                            deliveryOrderItem.getSkuNumber(),
                            deliveryOrderItem.getDeliveredQty(),
                            deliveryOrderItem.getCurrentQty());
                    }
                    deliveryOrderItem.setReasonCode(updateDeliveryOrderItemDto.getReasonCode());
                }
            }));
        calculateTotalDeliveredAmount();
    }

    private void calculateTotalDeliveredAmount() {
        this.totalPrice = deliveryOrderItems.stream()
            .filter(item -> item.getDeliveredQty() != null && item.getPrice() != null)
            .map(DeliveryOrder::calculateItemPrice)
            .reduce(BigDecimal.ZERO, BigDecimal::add)
            .setScale(2, RoundingMode.CEILING);
        if (this.discountApplications != null && !this.discountApplications.isEmpty()) {
            try {
                List<DiscountApplication> discountApplicationList = SerializationUtils.readValue(this.discountApplications,
                    new TypeReference<>() {
                    });
                if (CollectionUtils.isNotEmpty(discountApplicationList)) {
                    Optional<DiscountApplication> discountApplicationOptional = discountApplicationList.stream()
                        .filter(discountApplication -> "percentage".equals(discountApplication.getValueType()))
                        .findFirst();
                    if (discountApplicationOptional.isPresent()) {
                        BigDecimal discountMultiplier = BigDecimal.valueOf(100)
                            .subtract(new BigDecimal(discountApplicationOptional.get().getValue()))
                            .divide(BigDecimal.valueOf(100), 4, RoundingMode.DOWN);
                        this.totalPrice = totalPrice.multiply(discountMultiplier).setScale(2, RoundingMode.DOWN);
                    }
                }
            } catch (IOException e) {
                log.error("Failed to deserialize discount applications: {}", e.getMessage());
            }
        }
    }

    private static BigDecimal calculateItemPrice(DeliveryOrderItem item) {
        if (item.getDiscountAllocations() != null) {
            try {
                List<DiscountAllocation> discountAllocations = SerializationUtils.readValue(item.getDiscountAllocations(),
                    new TypeReference<>() {
                    });
                if (CollectionUtils.isNotEmpty(discountAllocations) && discountAllocations.stream().anyMatch(discountAllocation ->
                    discountAllocation.getValueType() != null && discountAllocation.getValueType().equals("fixed_amount"))) {
                    BigDecimal totalDiscount = discountAllocations.stream()
                        .filter(discountAllocation -> discountAllocation.getValueType() != null
                            && discountAllocation.getValueType().equals("fixed_amount"))
                        .map(DiscountAllocation::getAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                    if (item.getCurrentQty().compareTo(item.getDeliveredQty()) == 0) {
                        BigDecimal totalItemPrice = item.getDeliveredQty().multiply(item.getPrice());
                        return totalItemPrice.subtract(totalDiscount);
                    } else {
                        BigDecimal discountPerUnit = totalDiscount.divide(item.getCurrentQty(), 2, RoundingMode.CEILING);
                        return item.getDeliveredQty().multiply(item.getPrice().subtract(discountPerUnit));
                    }
                }
            } catch (IOException e) {
                log.error("Failed to deserialize discount allocations for item {}: {}", item.getSkuNumber(), e.getMessage());
                return item.getDeliveredQty().multiply(item.getPrice());
            }
        }
        return item.getDeliveredQty().multiply(item.getPrice());
    }

    public DeliveryOrder update(ShopifyOrderForDeliveryDto shopifyOrderDto) {
        if (shopifyOrderDto == null) {
            throw new IllegalArgumentException("ShopifyOrderDto cannot be null");
        }

        String newDeliveryDate = convertDeliveryDate(shopifyOrderDto.getTags());
        if (!Objects.equals(newDeliveryDate, this.deliveryDate)) {
            this.deliveryDate = newDeliveryDate;
        }

        if (shopifyOrderDto.getCancelledAt() != null) {
            this.setState(DeliveryOrderStatus.CANCELED);
        }

        this.fulfillmentStatus = shopifyOrderDto.getFulfillmentStatus();
        this.paymentStatus = shopifyOrderDto.getFinancialStatus();
        this.customerNotes = shopifyOrderDto.getNote();
        this.address = convertAddress(shopifyOrderDto.getShippingAddress());
        this.deliveryTimeWindow = convertDeliveryTimeWindow(shopifyOrderDto.getTags());
        this.originalTotalPrice = shopifyOrderDto.getTotalPrice();
        this.currentTotalDiscounts = shopifyOrderDto.getCurrentTotalDiscounts();
        convertDiscountApplication(shopifyOrderDto);

        List<DeliveryOrderItem> newItems = convertDeliveryOrderItem(shopifyOrderDto.getLineItems(),
            shopifyOrderDto.getDiscountApplications());

        Map<String, DeliveryOrderItem> newItemMap = newItems.stream()
            .collect(Collectors.toMap(DeliveryOrderItem::getShopifyOrderItemId, item -> item));

        for (DeliveryOrderItem existingItem : this.deliveryOrderItems) {
            DeliveryOrderItem updatedItem = newItemMap.get(existingItem.getShopifyOrderItemId());

            if (updatedItem != null) {
                existingItem.setQty(updatedItem.getQty());
                existingItem.setCurrentQty(updatedItem.getCurrentQty());
                existingItem.setLine(updatedItem.getLine());
                existingItem.setTitle(updatedItem.getTitle());
                existingItem.setSkuNumber(updatedItem.getSkuNumber());
                existingItem.setPrice(updatedItem.getPrice());
                existingItem.setDiscountAllocations(updatedItem.getDiscountAllocations());
                newItemMap.remove(existingItem.getShopifyOrderItemId());
            } else {
                existingItem.setDeletedAt(Instant.now());
                existingItem.setDeletedBy("Shopify");
            }
        }

        this.deliveryOrderItems.addAll(newItemMap.values());
        return this;
    }

    public String convertDeliveryDate(String tags) {
        if (tags == null) {
            return null;
        }
        String[] tagArray = tags.split(",");
        String regex = "\\d{4}-\\d{2}-\\d{2}";
        Pattern pattern = Pattern.compile(regex);

        for (String tag : tagArray) {
            Matcher matcher = pattern.matcher(tag);
            if (matcher.find()) {
                return matcher.group();
            }
        }
        return null;
    }

    private String convertDeliveryTimeWindow(String tags) {
        if (tags == null) {
            return null;
        }
        String[] tagArray = tags.split(",");
        String regex = "\\b(1[0-2]|0?[1-9])\\s?(AM|PM)\\s?-\\s?(1[0-2]|0?[1-9])\\s?(AM|PM)\\b";
        Pattern pattern = Pattern.compile(regex);

        for (String tag : tagArray) {
            Matcher matcher = pattern.matcher(tag);
            if (matcher.find()) {
                return matcher.group();
            }
        }
        return null;
    }

    public void inTransit() {
        if (this.getState() == DeliveryOrderStatus.IN_TRANSIT) {
            log.warn("DeliveryOrder {} already in transit", this.getOrderNumber());
            return;
        }
        this.inTransitAt = Instant.now();
        processEvent(DeliveryOrderTransitionEvents.IN_TRANSIT);
    }

    public void arrive() {
        if (this.getState() == DeliveryOrderStatus.ARRIVED) {
            log.warn("DeliveryOrder {} already arrived", this.getOrderNumber());
            return;
        }
        this.arrivedAt = Instant.now();
        processEvent(DeliveryOrderTransitionEvents.ARRIVE);
    }

    public void delivery(DeliveryOrderDeliveredCommand command) {
        if (this.getState() == DeliveryOrderStatus.DELIVERED) {
            log.warn("DeliveryOrder {} already completed", this.getOrderNumber());
            return;
        }
        if (this.rescheduleType != null && RescheduleType.NEED_REMOVE.contains(this.rescheduleType)) {
            this.setRescheduleType(null);
        }
        this.deliveredAt = Instant.now();
        this.paymentType = command.getPaymentType();
        this.notes = command.getNotes();
        processEvent(DeliveryOrderTransitionEvents.COMPLETE);
    }

    public void revertToCreated(DeliveryOrder order) {
        order.setDeliveryTaskId(null);
        order.setSequence(null);
        order.setPlanArriveAt(null);
        order.setPlanDeliveryAt(null);
        order.setRmOrderId(null);
        order.setRescheduleType(null);
        order.setArrivedAt(null);
        processEvent(DeliveryOrderTransitionEvents.REVERT_TO_CREATED);
    }

    public void removeFromTask(DeliveryOrder order) {
        order.setDeliveryTaskId(null);
    }

    public void updateStatus(DeliveryOrderStatus newStatus, DeliveryOrderDeliveredCommand command) {
        if (this.getState() == newStatus) {
            log.warn("DeliveryOrder {} already in status {}", this.getOrderNumber(), newStatus);
            return;
        }

        switch (newStatus) {
            case ASSIGNED -> this.assign();
            case IN_TRANSIT -> this.inTransit();
            case ARRIVED -> this.arrive();
            case UNLOADED -> this.unload();
            case DELIVERED -> this.delivery(command);
            case CREATED -> this.revertToCreated(this);
            case CANCELED -> this.processEvent(DeliveryOrderTransitionEvents.CANCEL);
            default -> throw new IllegalArgumentException(
                "Unsupported status transition to " + newStatus.name());
        }
    }

    private void unload() {
        if (this.getState() == DeliveryOrderStatus.UNLOADED) {
            log.warn("DeliveryOrder {} already unloaded", this.getOrderNumber());
            return;
        }
        this.unloadedAt = Instant.now();
        processEvent(DeliveryOrderTransitionEvents.UNLOAD);
    }

    private void assign() {
        if (this.getState() == DeliveryOrderStatus.ASSIGNED) {
            log.warn("DeliveryOrder {} already assigned", this.getOrderNumber());
            return;
        }
        processEvent(DeliveryOrderTransitionEvents.ASSIGN);
    }

    public void updateSequenceFromStep(Step step) {
        if (step == null) {
            log.warn("No step information available for order {}, setting default sequence", getOrderNumber());
            this.setSequence(999);
            return;
        }
        this.setSequence(step.getOrderSequence());
    }

    public void updatePlannedTimesFromStep(String deliveryDate, Step step) {

        if (List.of(DeliveryOrderStatus.ARRIVED, DeliveryOrderStatus.UNLOADED, DeliveryOrderStatus.DELIVERED)
            .contains(this.getState())) {
            log.warn("Cannot update planned times for order {} because it is already delivered", getOrderNumber());
            return;
        }

        if (step == null) {
            return;
        }

        Integer startSec = step.getStartSec();
        Integer endSec = step.getEndSec();

        if (startSec != null) {
            setPlanArriveAt(secondsSinceMidnight(deliveryDate, startSec));
        }

        if (endSec != null) {
            setPlanDeliveryAt(secondsSinceMidnight(deliveryDate, endSec));
        }

    }

    public void assignToTask(DeliveryTask task,
        Step step) {
        setRmOrderId(step.getOrderId());
        setDeliveryTaskId(task.getId());
        updateStatus(DeliveryOrderStatus.ASSIGNED, null);
        updateSequenceFromStep(step);
        updatePlannedTimesFromStep(task.getDeliveryDate(), step);
    }

    public void assignDeliveredOrderToTask(DeliveryTask task, Step step) {
        setRmOrderId(step.getOrderId());
        setDeliveryTaskId(task.getId());
        updateSequenceFromStep(step);
        updatePlannedTimesFromStep(task.getDeliveryDate(), step);
    }

    public void updateTaskAssignment(String deliveryDate, Step step) {
        if (this.getDeliveryTaskId() == null) {
            throw new IllegalStateException("Cannot update task assignment for order " +
                getOrderNumber() + " because it is not assigned to any task");
        }

        updateSequenceFromStep(step);
        updatePlannedTimesFromStep(deliveryDate, step);
    }
} 