package com.mercaso.wms.infrastructure.repository.pickingtask.jpa;

import com.mercaso.wms.application.dto.PickedItemsDto;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.repository.pickingtask.jpa.criteria.PickingTaskSearchCriteria;
import com.mercaso.wms.interfaces.util.SortByParseUtil;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

@Repository
@RequiredArgsConstructor
@Slf4j
public class PickingTaskJdbcTemplate {

    private final NamedParameterJdbcTemplate jdbcTemplate;

    public List<PickedItemsDto> fetchPickedItemsForDeliveryDate(String deliveryDate) {
        String sql = """ 
             SELECT pt.source, pti.sku_number, pti.picked_qty, pti.expect_qty as requested_qty,
                    pti.title, bi.source as initial_source, pt.source as final_source, bi.order_number, bi.line, pti.breakdown_name as breakdown
             FROM picking_task_items pti
                      left join picking_task pt on pt.id = pti.picking_task_id
                      left join batch b on b.id = pt.batch_id
                      left join batch_items bi on pti.batch_item_id = bi.id
             WHERE b.tag = :deliveryDate
             and pt.deleted_at is null
             and pti.deleted_at is null
             order by bi.order_number, bi.line
            """;
        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("deliveryDate", deliveryDate);
        return jdbcTemplate.query(sql, params, (rs, rowNum) -> PickedItemsDto.builder()
            .itemNumber(rs.getString("sku_number"))
            .pickedQty(rs.getInt("picked_qty"))
            .requestedQty(rs.getInt("requested_qty"))
            .itemDescription(rs.getString("title"))
            .breakdown(rs.getString("breakdown"))
            .initialFrom(rs.getString("initial_source"))
            .finalFrom(rs.getString("final_source"))
            .orderNumber(rs.getString("order_number"))
            .line(rs.getString("line"))
            .build());
    }

    public Page<UUID> findPickingTaskIds(PickingTaskSearchCriteria criteria, Pageable pageable) {
        StringBuilder sql = new StringBuilder();
        sql.append("WITH ranked_tasks AS (");
        sql.append("    SELECT pt.id,");
        sql.append("        pt.created_at,");
        sql.append("        pt.updated_at,");
        sql.append("        pt.created_by,");
        sql.append("        pt.picker_user_name,");
        sql.append("        pti.order_number,");
        sql.append("        pti.breakdown_name,");
        sql.append("        pti.aisle_number,");
        sql.append("        ROW_NUMBER() OVER(PARTITION BY pt.id ");

        StringBuilder orderByClause = new StringBuilder();
        if (pageable.getSort().isSorted()) {
            orderByClause.append("ORDER BY ");
            pageable.getSort()
                .forEach(order -> orderByClause.append(SortByParseUtil.camelCaseToUnderscored(order.getProperty()))
                    .append(" ")
                    .append(order.getDirection().name())
                    .append(", "));
            orderByClause.delete(orderByClause.length() - 2, orderByClause.length());
        } else {
            orderByClause.append("ORDER BY pt.created_at DESC");
        }

        sql.append(orderByClause);
        sql.append(") AS rn");
        sql.append("    FROM picking_task pt");
        sql.append("    JOIN picking_task_items pti ON pti.picking_task_id = pt.id");
        sql.append("    WHERE pt.deleted_at IS NULL");
        sql.append("    AND pti.deleted_at IS NULL");

        MapSqlParameterSource params = new MapSqlParameterSource();

        StringBuilder conditionSql = new StringBuilder();
        addCondition(criteria, conditionSql, params);
        if (!conditionSql.isEmpty()) {
            sql.append(" ").append(conditionSql);
        }

        sql.append(")");

        sql.append(" SELECT id FROM ranked_tasks WHERE rn = 1 ");

        if (pageable.getSort().isSorted()) {
            sql.append(orderByClause.toString()
                .replace("pti", "ranked_tasks")
                .replace("pt", "ranked_tasks"));
        }

        sql.append(" LIMIT :limit OFFSET :offset");
        params.addValue("limit", pageable.getPageSize());
        params.addValue("offset", pageable.getOffset());

        try {
            List<UUID> content = jdbcTemplate.query(
                sql.toString(),
                params,
                (rs, rowNum) -> rs.getObject("id", UUID.class)
            );

            String countSql = "SELECT COUNT(*) FROM ( SELECT DISTINCT pt.id FROM picking_task pt "
                + "JOIN picking_task_items pti ON pti.picking_task_id = pt.id "
                + "WHERE pt.deleted_at IS NULL AND pti.deleted_at IS NULL";

            StringBuilder countCondition = new StringBuilder();
            addCondition(criteria, countCondition, params);
            if (!countCondition.isEmpty()) {
                countSql += " " + countCondition;
            }
            countSql += ") AS count_query";

            Long total = jdbcTemplate.queryForObject(countSql, params, Long.class);
            return new PageImpl<>(content, pageable, total != null ? total : 0);
        } catch (Exception e) {
            log.error("Error executing search query: {}", sql, e);
            throw new WmsBusinessException("Error executing search query", e);
        }
    }

    private void addCondition(PickingTaskSearchCriteria criteria, StringBuilder sql, MapSqlParameterSource params) {
        if (StringUtils.isNotBlank(criteria.getDeliveryDate())) {
            sql.append(" AND EXISTS (SELECT 1 FROM batch bd WHERE pt.batch_id = bd.id AND bd.tag = :deliveryDate) ");
            params.addValue("deliveryDate", criteria.getDeliveryDate());
        }

        if (!CollectionUtils.isEmpty(criteria.getOrderNumbers())) {
            sql.append(" AND pti.order_number IN (:orderNumbers) ");
            params.addValue("orderNumbers", criteria.getOrderNumbers());
        }

        if (!CollectionUtils.isEmpty(criteria.getDepartments())) {
            sql.append(" AND pti.department IN (:departments) ");
            params.addValue("departments", criteria.getDepartments());
        }

        if (!CollectionUtils.isEmpty(criteria.getCategories())) {
            sql.append(" AND pti.category IN (:categories) ");
            params.addValue("categories", criteria.getCategories());
        }

        if (StringUtils.isNotBlank(criteria.getBreakdownName())) {
            sql.append(" AND pti.breakdown_name = :breakdownName ");
            params.addValue("breakdownName", criteria.getBreakdownName());
        }

        if (!CollectionUtils.isEmpty(criteria.getAisleNumbers())) {
            sql.append(" AND pti.aisle_number IN (:aisleNumbers) ");
            params.addValue("aisleNumbers", criteria.getAisleNumbers());
        }

        if (criteria.getPickerUserId() != null) {
            sql.append(" AND pt.picker_user_id = :pickerUserId ");
            params.addValue("pickerUserId", criteria.getPickerUserId());
        }

        if (criteria.getStatuses() != null && criteria.getStatuses().length > 0) {
            sql.append(" AND pt.status IN (:statuses) ");
            params.addValue("statuses", Arrays.stream(criteria.getStatuses()).map(Enum::name).toList());
        }

        if (criteria.getType() != null) {
            sql.append(" AND pt.type = :type ");
            params.addValue("type", criteria.getType().name());
        }

        if (!CollectionUtils.isEmpty(criteria.getNumbers())) {
            sql.append(" AND pt.number IN (:numbers) ");
            params.addValue("numbers", criteria.getNumbers());
        }

        if (criteria.getSource() != null) {
            sql.append(" AND pt.source = :source ");
            params.addValue("source", criteria.getSource().name());
        }

        if (!CollectionUtils.isEmpty(criteria.getSkuNumbers())) {
            sql.append(" AND pti.sku_number IN (:skuNumbers) ");
            params.addValue("skuNumbers", criteria.getSkuNumbers());
        }
    }

}
