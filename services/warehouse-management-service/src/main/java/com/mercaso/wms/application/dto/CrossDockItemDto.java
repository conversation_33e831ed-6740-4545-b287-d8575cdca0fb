package com.mercaso.wms.application.dto;

import com.mercaso.wms.domain.crossdockitem.enums.CrossDockItemSourceEnum;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CrossDockItemDto extends BaseDto {

    private UUID id;

    private UUID crossDockTaskId;

    private UUID batchId;

    private String skuNumber;

    private String title;

    private UUID itemId;

    private UUID taskItemId;

    private CrossDockItemSourceEnum source;

    private UUID shippingOrderId;

    private UUID shippingOrderItemId;

    private List<String> sequence;

    private Integer pickedQty;

    private Integer crossDockedQty;

    private String breakdownName;

    private String orderNumber;

    private Instant createdAt;

    private String createdBy;

    private Instant updatedAt;

    private String updatedBy;
}