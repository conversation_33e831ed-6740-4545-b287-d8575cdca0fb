package com.mercaso.wms.application.command.pickingtask;

import com.mercaso.wms.application.command.BaseCommand;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class BulkSplitPickingTaskCommand extends BaseCommand {

    private List<UUID> pickingTaskItemIds;

    private UUID pickerUserId;

    private String pickerUserName;

    private String warehouseName;

    private List<ItemSuggestLocation> itemSuggestLocations;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ItemSuggestLocation implements Serializable {

        private UUID pickingTaskItemId;

        private UUID locationId;
    }

}


