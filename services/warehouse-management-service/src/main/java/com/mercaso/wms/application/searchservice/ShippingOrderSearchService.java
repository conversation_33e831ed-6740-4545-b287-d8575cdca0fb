package com.mercaso.wms.application.searchservice;

import com.mercaso.wms.application.dto.shippingorder.ShippingOrderDto;
import com.mercaso.wms.application.mapper.shippingorder.ShippingOrderDtoApplicationMapper;
import com.mercaso.wms.application.query.ShippingOrderQuery;
import com.mercaso.wms.domain.shippingorder.ShippingOrderRepository;
import com.mercaso.wms.domain.shippingorder.enums.ShippingOrderStatus;
import com.mercaso.wms.infrastructure.repository.shippingorder.criteria.ShippingOrderSearchCriteria;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

@Slf4j
@Validated
@Service
@RequiredArgsConstructor
public class ShippingOrderSearchService {

    private final ShippingOrderRepository shippingOrderRepository;

    private final ShippingOrderDtoApplicationMapper shippingOrderDtoApplicationMapper;


    public Page<ShippingOrderDto> searchShippingOrders(ShippingOrderQuery shippingOrderQuery, Pageable pageable) {
        ShippingOrderSearchCriteria criteria = new ShippingOrderSearchCriteria();
        BeanUtils.copyProperties(shippingOrderQuery, criteria);

        if (!CollectionUtils.isEmpty(shippingOrderQuery.getStatuses())) {
            criteria.setStatuses(shippingOrderQuery.getStatuses().stream()
                .map(ShippingOrderStatus::valueOf)
                .toList());
        }

        return shippingOrderRepository.findShippingOrderList(criteria, pageable)
            .map(shippingOrderDtoApplicationMapper::domainToDto);
    }
}
