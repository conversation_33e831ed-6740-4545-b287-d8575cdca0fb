package com.mercaso.wms.application.queryservice;

import com.mercaso.wms.application.dto.shippingorder.ShippingOrderDto;
import com.mercaso.wms.application.mapper.shippingorder.ShippingOrderDtoApplicationMapper;
import com.mercaso.wms.domain.shippingorder.ShippingOrderRepository;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

@Slf4j
@Validated
@Service
@RequiredArgsConstructor
public class ShippingOrderQueryService {

    private final ShippingOrderRepository shippingOrderRepository;

    private final ShippingOrderDtoApplicationMapper shippingOrderDtoApplicationMapper;

    public List<ShippingOrderDto> findByOrderNumbers(List<String> orderNumbers) {
        return shippingOrderRepository.findByOrderNumbers(orderNumbers)
            .stream()
            .map(shippingOrderDtoApplicationMapper::domainToDto)
            .toList();
    }

    public ShippingOrderDto findById(UUID id) {
        return shippingOrderDtoApplicationMapper.domainToDto(shippingOrderRepository.findById(id));
    }

}