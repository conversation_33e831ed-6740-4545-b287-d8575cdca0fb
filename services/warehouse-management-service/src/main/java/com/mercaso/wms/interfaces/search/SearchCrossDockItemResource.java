package com.mercaso.wms.interfaces.search;

import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.dto.view.SearchCrossDockTaskItemView;
import com.mercaso.wms.application.query.CrossDockTaskItemQuery;
import com.mercaso.wms.application.searchservice.CrossDockTaskItemSearchService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Validated
@RestController
@RequestMapping("/search")
@RequiredArgsConstructor
public class SearchCrossDockItemResource {

    private final CrossDockTaskItemSearchService crossDockTaskItemSearchService;

    @PreAuthorize("hasAuthority('wms:read:cross-dock-tasks')")
    @GetMapping("/cross-dock-task-items")
    public Result<SearchCrossDockTaskItemView> searchCrossDockTaskItems(@ModelAttribute CrossDockTaskItemQuery query) {
        Page<SearchCrossDockTaskItemView> crossDockTaskItemViews = crossDockTaskItemSearchService.searchCrossDockTaskItems(query);
        return new Result<>(crossDockTaskItemViews.getContent(), crossDockTaskItemViews.getTotalElements());
    }
} 