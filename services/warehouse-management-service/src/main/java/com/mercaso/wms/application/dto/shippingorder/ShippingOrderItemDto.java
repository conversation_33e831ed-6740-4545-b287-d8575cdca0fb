package com.mercaso.wms.application.dto.shippingorder;

import com.mercaso.wms.application.dto.BaseDto;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ShippingOrderItemDto extends BaseDto {

    private UUID id;

    private UUID itemId;

    private String skuNumber;

    private String title;

    private Integer qty;

    private Integer pickedQty;

    private Integer fulfilledQty;

    private Integer line;

    private String department;

    private String category;

    private String subCategory;

    private boolean picked;

}
