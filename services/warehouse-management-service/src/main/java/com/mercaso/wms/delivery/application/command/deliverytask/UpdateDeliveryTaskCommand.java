package com.mercaso.wms.delivery.application.command.deliverytask;

import com.mercaso.wms.application.command.BaseCommand;
import com.mercaso.wms.delivery.application.dto.deliverytask.PostCheckDto;
import com.mercaso.wms.delivery.application.dto.deliverytask.PreCheckDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateDeliveryTaskCommand extends BaseCommand {

    private PreCheckDto preCheck;

    private PostCheckDto postCheck;

}
