package com.mercaso.wms.delivery.application.dto.deliverytask;

import com.mercaso.wms.application.dto.BaseDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PostCheckDto extends BaseDto {

    private boolean checked;

    private String notes;
}
