package com.mercaso.wms.infrastructure.statemachine.config;


import com.mercaso.wms.domain.shippingorder.ShippingOrder;
import com.mercaso.wms.domain.shippingorder.enums.ShippingOrderStatus;
import com.mercaso.wms.domain.shippingorder.enums.ShippingOrderTransitionEvents;
import com.mercaso.wms.infrastructure.statemachine.StatemachineFactory;
import com.mercaso.wms.infrastructure.statemachine.factory.WmsStateMachineFactory;
import java.util.EnumSet;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.statemachine.StateMachinePersist;
import org.springframework.statemachine.config.EnableStateMachineFactory;
import org.springframework.statemachine.config.EnumStateMachineConfigurerAdapter;
import org.springframework.statemachine.config.StateMachineFactory;
import org.springframework.statemachine.config.builders.StateMachineStateConfigurer;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;
import org.springframework.statemachine.persist.DefaultStateMachinePersister;
import org.springframework.statemachine.persist.StateMachinePersister;

@Configuration
@EnableStateMachineFactory(contextEvents = false, name = "shippingOrderTransitionEventsStateMachineFactory")
public class ShippingOrderStateMachineConfig extends
    EnumStateMachineConfigurerAdapter<ShippingOrderStatus, ShippingOrderTransitionEvents> {

    @Override
    public void configure(StateMachineStateConfigurer<ShippingOrderStatus, ShippingOrderTransitionEvents> states)
        throws Exception {
        states
            .withStates()
            .initial(ShippingOrderStatus.OPEN)
            .states(EnumSet.allOf(ShippingOrderStatus.class))
        ;
    }

    @Override
    public void configure(StateMachineTransitionConfigurer<ShippingOrderStatus, ShippingOrderTransitionEvents> transitions)
        throws Exception {
        transitions
            .withExternal()
            .source(ShippingOrderStatus.OPEN).target(ShippingOrderStatus.IN_PROGRESS)
            .event(ShippingOrderTransitionEvents.ADD_TO_BATCH)
            .and()
            //picked
            .withExternal()
            .source(ShippingOrderStatus.OPEN).target(ShippingOrderStatus.PICKED)
            .event(ShippingOrderTransitionEvents.PICK_FINISH)
            .and()
            .withExternal()
            .source(ShippingOrderStatus.IN_PROGRESS).target(ShippingOrderStatus.PICKED)
            .event(ShippingOrderTransitionEvents.PICK_FINISH)
        ;
    }

    @Bean
    @StatemachineFactory(domainClass = ShippingOrder.class)
    public WmsStateMachineFactory<ShippingOrderStatus, ShippingOrderTransitionEvents, ShippingOrder> shippingOrderStateMachineAdapter(
        StateMachineFactory<ShippingOrderStatus, ShippingOrderTransitionEvents> shippingOrderTransitionEventsStateMachineFactory,
        StateMachinePersister<ShippingOrderStatus, ShippingOrderTransitionEvents, ShippingOrder> shippingOrderStateMachinePersister) {
        return new WmsStateMachineFactory<>(shippingOrderTransitionEventsStateMachineFactory, shippingOrderStateMachinePersister);
    }

    @Bean
    public StateMachinePersister<ShippingOrderStatus, ShippingOrderTransitionEvents, ShippingOrder> shippingOrderStateMachinePersister(
        StateMachinePersist<ShippingOrderStatus, ShippingOrderTransitionEvents, ShippingOrder> shippingOrderStateMachinePersist) {
        return new DefaultStateMachinePersister<>(shippingOrderStateMachinePersist);
    }

}
