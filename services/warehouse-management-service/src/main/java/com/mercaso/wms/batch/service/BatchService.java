package com.mercaso.wms.batch.service;

import static com.mercaso.wms.infrastructure.utils.DateUtils.laDateTime;

import com.mercaso.document.operations.operations.DocumentOperations;
import com.mercaso.ims.client.dto.ItemCategoryDto;
import com.mercaso.ims.client.dto.ItemSerachDto;
import com.mercaso.ims.client.dto.VendorItemDto;
import com.mercaso.wms.application.dto.BatchDto;
import com.mercaso.wms.application.service.BatchItemApplicationService;
import com.mercaso.wms.application.service.ShippingOrderApplicationService;
import com.mercaso.wms.batch.dto.BreakdownDto;
import com.mercaso.wms.batch.dto.CreateBatchDto;
import com.mercaso.wms.batch.dto.ExcelBatchDto;
import com.mercaso.wms.batch.dto.IgnoredOrderDto;
import com.mercaso.wms.batch.dto.LookupDto;
import com.mercaso.wms.batch.dto.PopulateCondition;
import com.mercaso.wms.batch.dto.WriteTemplateCondition;
import com.mercaso.wms.batch.dto.response.Response;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.batch.enums.UploadDocNameEnum;
import com.mercaso.wms.batch.mapper.DtoMapper;
import com.mercaso.wms.batch.strategy.PopulateBreakdownStrategy;
import com.mercaso.wms.batch.writer.TemplateWriterService;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.batch.BatchRepository;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.inventorystock.InventoryStock;
import com.mercaso.wms.domain.inventorystock.InventoryStockRepository;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.location.LocationRepository;
import com.mercaso.wms.domain.location.enums.LocationType;
import com.mercaso.wms.domain.shippingorder.ShippingOrder;
import com.mercaso.wms.domain.shippingorder.ShippingOrderRepository;
import com.mercaso.wms.domain.shippingorderitem.ShippingOrderItem;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.domain.warehouse.WarehouseRepository;
import com.mercaso.wms.infrastructure.config.PgAdvisoryLock;
import com.mercaso.wms.infrastructure.event.applicationevent.listener.PickingTaskApplicationEventListener;
import com.mercaso.wms.infrastructure.excel.ReadExcelService;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.external.ims.ImsAdaptor;
import com.mercaso.wms.infrastructure.utils.ResponseUtil;
import com.mercaso.wms.infrastructure.utils.StockUtil;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
@AllArgsConstructor
public class BatchService {

    private final PopulateBreakdownStrategy fullBreakdownPopulateStrategy;

    private final PopulateBreakdownStrategy fullBreakDownSmallPopulateStrategy;

    private final ShippingOrderApplicationService shippingOrderApplicationService;

    private final DocumentOperations documentOperations;

    private final PopulateStrategyService populateStrategyService;

    private final LocationRepository locationRepository;

    private final ReadExcelService readExcelService;

    private final ImsAdaptor imsAdaptor;

    private final TemplateWriterService templateWriterService;

    private final BatchItemApplicationService batchItemApplicationService;

    private final BatchRepository batchRepository;

    private final ShippingOrderRepository shippingOrderRepository;

    private final PickingTaskApplicationEventListener pickingTaskApplicationEventListener;

    private final InventoryStockRepository inventoryStockRepository;

    private final WarehouseRepository warehouseRepository;

    private final PgAdvisoryLock pgAdvisoryLock;

    @Transactional
    public Response<BatchDto> createBatch(CreateBatchDto createBatchDto) throws WmsBusinessException {
        log.info("[createBatch] createBatchDto: {}.", createBatchDto);
        String taggedWith = createBatchDto.getTaggedWith().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        Boolean isAcquired = pgAdvisoryLock.tryLockWithSessionLevel(("createBatch_" + taggedWith).hashCode());
        if (isAcquired == null || !isAcquired) {
            throw new WmsBusinessException("The batch is being created.");
        }
        List<ShippingOrder> shippingOrders = shippingOrderApplicationService.findActiveShippingOrdersByDeliveryDate(taggedWith);
        log.info("[createBatch] shopifyOrders size: {}.", shippingOrders.size());
        if (shippingOrders.isEmpty()) {
            throw new WmsBusinessException("No shopify orders or stock data found.");
        }
        List<Location> locations = locationRepository.findAll();

        List<InventoryStock> mfcInventoryStocks = getInventoryStocks("MFC");

        File file = null;
        try {
            file = downloadAndConvertFile(createBatchDto);

            List<IgnoredOrderDto> ignoredOrders = readExcelService.getIgnoredOrders(file);
            addExistingBreakdownToIgnoreOrders(taggedWith, ignoredOrders);

            List<String> skuList = shippingOrders.stream()
                .flatMap(order -> order.getShippingOrderItems().stream())
                .map(ShippingOrderItem::getSkuNumber)
                .distinct()
                .toList();
            if (skuList.isEmpty()) {
                throw new WmsBusinessException("No sku found in shopify orders.");
            }
            Map<String, ItemCategoryDto> itemMap = getItemMap(skuList);

            PopulateCondition populateCondition = processLookupData(shippingOrders, file, itemMap);
            List<ExcelBatchDto> excelBatchDtos = new ArrayList<>();
            populateCondition.setShippingOrders(shippingOrders);
            populateCondition.setExcelBatchDtoList(excelBatchDtos);
            populateCondition.setMfcStocks(StockUtil.getStockListByInventory(mfcInventoryStocks));
            populateCondition.setItemMap(itemMap);

            List<InventoryStock> mdcInventoryStocks = getInventoryStocks("MDC");
            log.info("[createBatch] mdcInventoryStocks size: {}.", mdcInventoryStocks.size());
            populateCondition.setMdcStocks(StockUtil.getStockListByInventory(mdcInventoryStocks));

            populateCondition.setDeliveryDate(taggedWith);
            populateCondition.setIgnoredOrders(ignoredOrders);
            populateCondition.setLocations(locations);

            excelBatchDtos = populateStrategyService.populateBatchTemplate(populateCondition);
            log.info("[createBatch] batchDto populate size: {}.", excelBatchDtos.size());

            List<BreakdownDto> bigBreakdownDtos = fullBreakdownPopulateStrategy.populateBreakdownTemplate(excelBatchDtos);
            List<BreakdownDto> smallBreakdownDtos = fullBreakDownSmallPopulateStrategy.populateBreakdownTemplate(excelBatchDtos);

            Map<String, List<ExcelBatchDto>> sourceAndListMap = new HashMap<>();
            for (ExcelBatchDto batchDto : excelBatchDtos) {
                if (StringUtils.isNotEmpty(batchDto.getSource())) {
                    if (batchDto.getSource().equals(SourceEnum.MDC.name())) {
                        sourceAndListMap.computeIfAbsent(SourceEnum.MFC.name(), k -> new ArrayList<>())
                            .add(batchDto);
                    } else {
                        sourceAndListMap.computeIfAbsent(batchDto.getSource(), k -> new ArrayList<>())
                            .add(batchDto);
                    }
                }
            }

            WriteTemplateCondition writeTemplateCondition = WriteTemplateCondition.builder()
                .excelBatchDtos(excelBatchDtos)
                .bigBreakdownDtos(bigBreakdownDtos)
                .smallBreakdownDtos(smallBreakdownDtos)
                .taggedWith(taggedWith)
                .fileNames(CollectionUtils.isEmpty(createBatchDto.getFileNames()) ? null
                    : List.of(createBatchDto.getFileNames().getFirst()))
                .sourceAndListMap(sourceAndListMap)
                .build();

            BatchDto batchDto = templateWriterService.writeTemplate(writeTemplateCondition);
            saveBatchItems(writeTemplateCondition, batchDto, shippingOrders);

            Batch batch = batchRepository.findById(batchDto.getId());
            updateShippingOrders(excelBatchDtos, shippingOrders, batch, locations);
            pickingTaskApplicationEventListener.handleBatchCreatedEvent(batchDto.getId());

            return ResponseUtil.successResponse(List.of(batchDto));

        } catch (WmsBusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("[createBatch] create batch failed.", e);
            throw new WmsBusinessException("Create batch failed.", e);
        } finally {
            if (file != null) {
                try {
                    Files.delete(file.toPath());
                } catch (IOException e) {
                    log.warn("Failed to delete file: {}", file.getName(), e);
                }
            }
            pgAdvisoryLock.unLock(("createBatch_" + taggedWith).hashCode());
        }
    }

    private List<InventoryStock> getInventoryStocks(String warehouseName) {
        Warehouse warehouse = warehouseRepository.findByName(warehouseName);
        if (warehouse == null) {
            throw new WmsBusinessException("Warehouse not found.");
        }
        return inventoryStockRepository.findByWarehouseIdAndLocationTypes(warehouse.getId(),
            List.of(LocationType.BIN, LocationType.STOCK));
    }

    public void updateShippingOrders(List<ExcelBatchDto> excelBatchDtos,
        List<ShippingOrder> shippingOrders,
        Batch batch,
        List<Location> locations) {
        Map<String, UUID> orderNumberAndBreakdownLocationIdMapping = excelBatchDtos.stream()
            .filter(dto -> dto.getBreakdownLocationId() != null)
            .collect(Collectors.toMap(ExcelBatchDto::getOrderNumber,
                ExcelBatchDto::getBreakdownLocationId,
                (existingValue, newValue) -> newValue));

        shippingOrders.forEach(shopifyOrder -> {
            shopifyOrder.setBatchId(batch.getId());
            UUID breakdownLocationId = orderNumberAndBreakdownLocationIdMapping.get(shopifyOrder.getOrderNumber());
            if (breakdownLocationId != null) {
                shopifyOrder.setBreakdownLocation(locations.stream()
                    .filter(location -> location.getId().equals(breakdownLocationId))
                    .findFirst()
                    .orElse(null));
            }
        });
        shippingOrderRepository.saveAll(shippingOrders);
    }

    public void saveBatchItems(WriteTemplateCondition writeTemplateCondition,
        BatchDto batchDto,
        List<ShippingOrder> shippingOrders) {
        if (!CollectionUtils.isEmpty(writeTemplateCondition.getExcelBatchDtos())) {
            batchItemApplicationService.createBatchItems(covertBatchDtoToBatchItems(
                writeTemplateCondition.getExcelBatchDtos(),
                shippingOrders,
                batchDto.getId()));
        }
    }

    private void addExistingBreakdownToIgnoreOrders(String deliveryDate, List<IgnoredOrderDto> ignoredOrders) {
        List<ShippingOrder> rescheduledShippingOrders = shippingOrderRepository.findRescheduledShippingOrders(deliveryDate);
        if (!CollectionUtils.isEmpty(rescheduledShippingOrders)) {
            for (ShippingOrder rescheduledShippingOrder : rescheduledShippingOrders) {
                IgnoredOrderDto ignoredOrderDto = new IgnoredOrderDto();
                ignoredOrderDto.setOrderNumber(rescheduledShippingOrder.getOrderNumber());
                ignoredOrderDto.setBreakdown(
                    rescheduledShippingOrder.getBreakdownLocation() != null ? rescheduledShippingOrder.getBreakdownLocation()
                        .getName() : null);
                if (!ignoredOrders.contains(ignoredOrderDto)) {
                    ignoredOrders.add(ignoredOrderDto);
                }
            }
        }
    }

    private List<BatchItem> covertBatchDtoToBatchItems(List<ExcelBatchDto> excelBatchDtos,
        List<ShippingOrder> shippingOrders, UUID batchId) {

        Map<String, ShippingOrder> orderMap = shippingOrders.stream()
            .collect(Collectors.toMap(ShippingOrder::getOrderNumber, order -> order));

        List<BatchItem> batchItems = new LinkedList<>();

        for (ExcelBatchDto excelBatchDto : excelBatchDtos) {
            BatchItem batchItem = DtoMapper.INSTANCE.toBatchItem(excelBatchDto);
            batchItem.setBatchId(batchId);
            batchItem.setPickingAppCovered(true);

            ShippingOrder shippingOrder = orderMap.get(excelBatchDto.getOrderNumber());
            if (shippingOrder != null) {
                batchItem.setShippingOrderId(shippingOrder.getId());
                batchItem.setShippingOrderItemId(shippingOrder.getShippingOrderItems().stream()
                    .filter(item -> item.getSkuNumber().equals(excelBatchDto.getItemNumber())
                        && item.getLine().equals(excelBatchDto.getLine()))
                    .map(ShippingOrderItem::getId)
                    .findFirst()
                    .orElse(null));
            } else {
                log.error("[covertBatchDtoToBatchItems] Shipping order not found for order number: {}",
                    excelBatchDto.getOrderNumber());
            }
            batchItems.add(batchItem);
        }
        return batchItems;
    }

    private File downloadAndConvertFile(CreateBatchDto createBatchDto) throws WmsBusinessException {
        try {
            if (CollectionUtils.isEmpty(createBatchDto.getFileNames())) {
                return null;
            }
            log.info("[createBatch] start download file.");
            byte[] bytes = documentOperations.downloadDocument(createBatchDto.getFileNames().getFirst());
            File file = convertToFile(bytes);
            log.info("[createBatch] end download file: {}.", file.getName());
            return file;
        } catch (IOException e) {
            throw new WmsBusinessException("Failed to download and convert file.", e);
        }
    }

    private PopulateCondition processLookupData(List<ShippingOrder> shippingOrders,
        File file,
        Map<String, ItemCategoryDto> itemCategoryDtoMap) throws WmsBusinessException {
        Map<UploadDocNameEnum, List<LookupDto>> lookUpData = new EnumMap<>(UploadDocNameEnum.class);
        removeBlankShippingOrderItems(shippingOrders);
        populateSkuIfNeeded(shippingOrders);


        if (readExcelService.isSheetExists(file, UploadDocNameEnum.MASTER.getValue())) {
            lookUpData = readExcelService.getLookUpData(file);
            lookUpData.put(UploadDocNameEnum.VERNON, readExcelService.getVernonLookupData(file).stream()
                .map(DtoMapper.INSTANCE::toLookupDto)
                .toList());

            Map<UploadDocNameEnum, List<LookupDto>> finalLookUpData = lookUpData;
            readExcelService.getExoticLookupData(file).forEach(exoticDto -> {
                LookupDto lookupDto = DtoMapper.INSTANCE.toLookupDto(exoticDto);
                finalLookUpData.computeIfAbsent(UploadDocNameEnum.EXOTIC, k -> new LinkedList<>()).add(lookupDto);
            });

            lookUpData.values().forEach(lookupDtos -> lookupDtos.forEach(lookupDto -> {
                ItemCategoryDto itemCategoryDto = itemCategoryDtoMap.get(lookupDto.getItemNumber());
                if (itemCategoryDto != null) {
                    lookupDto.setPack(String.valueOf(itemCategoryDto.getPack()));
                    lookupDto.setBrand(itemCategoryDto.getBrand());
                    lookupDto.setDepartment(itemCategoryDto.getDepartment());
                    lookupDto.setCategory(itemCategoryDto.getCategory());
                    lookupDto.setSubCategory(itemCategoryDto.getSubCategory());
                    lookupDto.setClazz(itemCategoryDto.getClazz());
                    lookupDto.setId(itemCategoryDto.getId());
                }
            }));
        } else {
            buildLookupData(itemCategoryDtoMap, lookUpData);
        }

        for (ItemCategoryDto value : itemCategoryDtoMap.values()) {
            LookupDto lookupDto = DtoMapper.INSTANCE.toLookupDto(value);
            lookUpData.computeIfAbsent(UploadDocNameEnum.MASTER, k -> new LinkedList<>()).add(lookupDto);
        }

        return PopulateCondition.builder()
                .lookUpData(lookUpData)
                .build();
    }

    private void buildLookupData(Map<String, ItemCategoryDto> itemCategoryDtoMap,
                                 Map<UploadDocNameEnum, List<LookupDto>> lookUpData) {
        for (Map.Entry<String, ItemCategoryDto> entry : itemCategoryDtoMap.entrySet()) {
            ItemCategoryDto value = entry.getValue();
            LookupDto lookupDto = buildLookupData(value, value.getPrimaryVendorId());
            if (lookupDto == null) {
                lookupDto = buildLookupData(value, value.getBackupVendorId());
            }
            if (lookupDto != null) {
                lookUpData.computeIfAbsent(lookupDto.getLookupDocName(), k -> new LinkedList<>()).add(lookupDto);
            }
        }
    }

    private LookupDto buildLookupData(ItemCategoryDto value, UUID vendorId) {
        if (vendorId == null || CollectionUtils.isEmpty(value.getVendorItemDtos())) {
            return null;
        }
        for (VendorItemDto vendorItemDto : value.getVendorItemDtos()) {
            if (vendorId.equals(vendorItemDto.getVendorId())) {
                UploadDocNameEnum uploadDocNameEnum = UploadDocNameEnum.fromVendorName(vendorItemDto.getVendorName());
                if (uploadDocNameEnum != null) {
                    LookupDto lookupDto = DtoMapper.INSTANCE.toLookupDto(value);
                    lookupDto.setVendorItemNumber(vendorItemDto.getVendorSkuNumber());
                    lookupDto.setAisle(
                            StringUtils.isNotEmpty(vendorItemDto.getAisle()) ? vendorItemDto.getAisle() : value.getDepartment());
                    lookupDto.setLookupDocName(uploadDocNameEnum);
                    return lookupDto;
                }
            }
        }
        return null;
    }

    private void populateSkuIfNeeded(List<ShippingOrder> shippingOrders) {
        shippingOrders.stream()
                .flatMap(order -> order.getShippingOrderItems().stream())
                .filter(lineItem -> StringUtils.isEmpty(lineItem.getSkuNumber()))
                .forEach(shippingOrderItem -> {
                    ItemSerachDto itemSerachDto = imsAdaptor.searchItemsByTitle(shippingOrderItem.getTitle());
                    if (itemSerachDto != null) {
                        shippingOrderItem.setSkuNumber(itemSerachDto.getSkuNumber());
                    }
                });
    }

    private void removeBlankShippingOrderItems(List<ShippingOrder> shippingOrders) {
        shippingOrders.forEach(order -> {
            order.getShippingOrderItems().removeIf(shippingOrderItem -> StringUtils.isEmpty(shippingOrderItem.getSkuNumber())
                && StringUtils.isEmpty(shippingOrderItem.getTitle()));
        });
    }

    private File convertToFile(byte[] bytes) throws IOException {
        String filePath = generateFilePath();
        try (FileOutputStream outputStream = new FileOutputStream(filePath)) {
            outputStream.write(bytes);
        }
        return new File(filePath);
    }

    private String generateFilePath() {
        String projectRoot = Paths.get("").toAbsolutePath().toString();
        return projectRoot + "/".concat(laDateTime()).concat("-lookup-file.xlsx");
    }

    private Map<String, ItemCategoryDto> getItemMap(List<String> skuList) {
        return imsAdaptor.getItemsBySkus(skuList).stream()
            .collect(Collectors.toMap(ItemCategoryDto::getSkuNumber, item -> item));
    }

}
