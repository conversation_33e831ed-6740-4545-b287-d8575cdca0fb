package com.mercaso.wms.delivery.application.service;

import com.mercaso.businessevents.dispatcher.BusinessEventDispatcher;
import com.mercaso.wms.delivery.application.command.deliverytask.ReassignDeliveryTaskCommand;
import com.mercaso.wms.delivery.application.command.deliverytask.UpdateDeliveryTaskCommand;
import com.mercaso.wms.delivery.application.dto.deliverytask.DeliveryTaskDto;
import com.mercaso.wms.delivery.application.dto.slack.BuildDeliveryTaskExceptionEventDto;
import com.mercaso.wms.delivery.application.event.deliverytask.DeliveryTaskCompletedPayloadDto;
import com.mercaso.wms.delivery.application.event.deliverytask.DeliveryTaskInProgressPayloadDto;
import com.mercaso.wms.delivery.application.event.deliverytask.DeliveryTaskReassignedPayloadDto;
import com.mercaso.wms.delivery.application.event.deliverytask.DeliveryTaskUpdatedPayloadDto;
import com.mercaso.wms.delivery.application.event.dto.DeliveryTaskReassignedEventDto;
import com.mercaso.wms.delivery.application.event.dto.DeliveryTaskUpdatedEventDto;
import com.mercaso.wms.delivery.application.event.publisher.DeliveryTaskEventPublisher;
import com.mercaso.wms.delivery.application.mapper.deliverytask.DeliveryTaskDtoApplicationMapper;
import com.mercaso.wms.delivery.domain.account.Account;
import com.mercaso.wms.delivery.domain.account.AccountRepository;
import com.mercaso.wms.delivery.domain.account.AccountStatus;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrder;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrderRepository;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.DeliveryOrderStatus;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTask;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTaskRepository;
import com.mercaso.wms.delivery.domain.deliverytask.enums.DeliveryTaskStatus;
import com.mercaso.wms.delivery.domain.route.RmRoute;
import com.mercaso.wms.delivery.domain.route.RmRouteRepository;
import com.mercaso.wms.delivery.infrastructure.exception.DeliveryBusinessException;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.RouteManagerAdaptor;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.ApprovedRoute;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.CurrentRoutes;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.Order;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.Route;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.Step;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.TrackingData;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.enums.OrderStepType;
import com.mercaso.wms.infrastructure.event.BusinessEventFactory;
import com.mercaso.wms.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.wms.infrastructure.utils.DateUtils;
import com.mercaso.wms.infrastructure.utils.ManualNumberGenerate;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class DeliveryTaskService {


    private final RouteManagerAdaptor routeManagerAdaptor;
    private final DeliveryTaskRepository deliveryTaskRepository;
    private final DeliveryOrderRepository deliveryOrderRepository;
    private final AccountRepository accountRepository;
    private final RmRouteRepository rmRouteRepository;
    private final BusinessEventDispatcher dispatcher;
    private final RouteManagerService routeManagerService;
    private final SlackDeliveryNotificationService slackDeliveryNotificationService;
    private final ManualNumberGenerate manualNumberGenerate;
    private final DeliveryTaskEventPublisher deliveryTaskEventPublisher;

    @Transactional
    public void buildTasks(LocalDate deliveryDate) {
        log.info("Building delivery tasks deliveryDate is {}.", deliveryDate);

        cleanupExistingTasksAndOrders(verifyTaskStatus(deliveryDate));

        List<ApprovedRoute> approvedRoutes = getApprovedRoutes(deliveryDate);
        if (CollectionUtils.isEmpty(approvedRoutes)) {
            throw new DeliveryBusinessException(ErrorCodeEnums.DELIVERY_ROUTE_NOT_FOUND.getCode(),
                ErrorCodeEnums.DELIVERY_ROUTE_NOT_FOUND.getMessage());
        }

        this.buildTasksBaseOnApprovedRoutes(approvedRoutes);
    }

    @Transactional
    public void buildTasksAutomatically(LocalDate deliveryDate) {

        String formattedDate = deliveryDate.format(DateUtils.DATE_TO_STRING_FORMATTER);

        log.info("Automatically building delivery tasks for date: {}", formattedDate);

        List<ApprovedRoute> approvedRoutes = getApprovedRoutes(deliveryDate);
        if (CollectionUtils.isEmpty(approvedRoutes)) {
            log.warn("Automatically building delivery tasks, no approved routes found for deliveryDate is {}.", deliveryDate);
            return;
        }

        List<DeliveryTask> tasks = deliveryTaskRepository.findByDeliveryDate(formattedDate);
        if (CollectionUtils.isNotEmpty(tasks)) {
            log.info("Automatically building delivery tasks, tasks already exist for deliveryDate {}, skipping build.",
                formattedDate);
            return;
        }

        this.buildTasksBaseOnApprovedRoutes(approvedRoutes);
    }

    @Transactional
    public void syncNewRoutes(LocalDate deliveryDate) {
        log.info("Syncing new routes for delivery date: {}", deliveryDate);

        String formattedDate = deliveryDate.format(DateUtils.DATE_TO_STRING_FORMATTER);
        List<DeliveryTask> existingTasks = deliveryTaskRepository.findByDeliveryDate(formattedDate);

        if (CollectionUtils.isEmpty(existingTasks)) {
            log.info("No existing tasks found for delivery date: {}, will build all tasks", deliveryDate);
            buildTasks(deliveryDate);
            return;
        }

        log.info("Found {} existing tasks for delivery date: {}", existingTasks.size(), deliveryDate);

        List<ApprovedRoute> newRoutes = identifyNewRoutes(deliveryDate, existingTasks);
        if (CollectionUtils.isEmpty(newRoutes)) {
            log.info("No new routes found for delivery date: {}, nothing to sync", deliveryDate);
            return;
        }

        createTasksForNewRoutes(newRoutes, deliveryDate);
    }

    private void buildTasksBaseOnApprovedRoutes(List<ApprovedRoute> approvedRoutes) {
        Map<String, UUID> driverEmailToUserIdMap = createEmailToUserIdMap();
        Map<String, DeliveryOrder> orderMap = findCreatedOrdersByOrderNumbers(extractOrderNumbersTrimFromRoutes(approvedRoutes));
        List<DeliveryOrder> allOrdersToUpdate = new ArrayList<>();
        List<BuildDeliveryTaskExceptionEventDto> exceptionEventsToNotify = new ArrayList<>();

        log.info("buildTasks orderMap = {}.", orderMap);
        approvedRoutes.forEach(route -> processRoute(route,
            driverEmailToUserIdMap,
            orderMap,
            allOrdersToUpdate,
            exceptionEventsToNotify));

        if (CollectionUtils.isNotEmpty(allOrdersToUpdate)) {
            deliveryOrderRepository.updateAll(allOrdersToUpdate);
            log.info("Updated {} orders in batch", allOrdersToUpdate.size());
        }

        if (CollectionUtils.isNotEmpty(exceptionEventsToNotify)) {

            slackDeliveryNotificationService.batchNotifyBuildTaskException(exceptionEventsToNotify);
        }
    }

    private List<DeliveryTask> verifyTaskStatus(LocalDate deliveryDate) {
        String formattedDate = deliveryDate.format(DateUtils.DATE_TO_STRING_FORMATTER);
        log.info("Verifying task status for date: {}", formattedDate);

        // Find tasks by delivery date using domain repository
        List<DeliveryTask> tasks = deliveryTaskRepository.findByDeliveryDate(formattedDate);

        if (CollectionUtils.isEmpty(tasks)) {
            log.info("No tasks found for date: {}", formattedDate);
            return tasks;
        }

        boolean existsInProgressTask = tasks.stream().anyMatch(task -> task.getStatus() != DeliveryTaskStatus.CREATED);
        if (existsInProgressTask) {
            throw new DeliveryBusinessException(ErrorCodeEnums.DELIVERY_TASK_CANNOT_BE_BUILT.getCode(),
                ErrorCodeEnums.DELIVERY_TASK_CANNOT_BE_BUILT.getMessage());
        }
        return tasks;
    }

    private void cleanupExistingTasksAndOrders(List<DeliveryTask> tasks) {

        log.info("Cleaning up existing tasks and revert orders");

        // Extract task IDs
        List<UUID> taskIds = tasks.stream().map(DeliveryTask::getId).toList();

        // Find all orders associated with these tasks using domain repository
        List<DeliveryOrder> orders = deliveryOrderRepository.findAllByDeliveryTaskIdIn(taskIds);
        int ordersCount = orders.size();
        log.info("Found {} orders associated with tasks to reset", ordersCount);

        // Reset orders
        if (CollectionUtils.isNotEmpty(orders)) {

            orders.forEach(order -> {
                if (order.getStatus() == DeliveryOrderStatus.DELIVERED || order.getStatus() == DeliveryOrderStatus.CANCELED) {
                    log.warn("Order {} is already delivered or canceled, removing from task", order.getOrderNumber());
                    order.removeFromTask(order);
                } else {
                    order.revertToCreated(order);
                }
            });
            // Save updated orders
            deliveryOrderRepository.saveAll(orders);
            log.info("Reset {} orders to CREATED status", ordersCount);
        }

        Set<UUID> toDeleteIds = tasks.stream().map(DeliveryTask::getId).collect(Collectors.toSet());

        // Delete routing information related to tasks.
        rmRouteRepository.deleteAllByDeliveryTaskIdIn(toDeleteIds);
        log.info("Deleted routes for task IDs: {}", toDeleteIds);

        // Mark tasks as logically deleted
        deliveryTaskRepository.deleteAllByIdIn(toDeleteIds);
        log.info("Marked {} tasks as deleted", tasks.size());
    }

    private List<ApprovedRoute> getApprovedRoutes(LocalDate deliveryDate) {
        List<ApprovedRoute> routes = routeManagerAdaptor.getApprovedRoutesV2(List.of(deliveryDate.format(DateUtils.RM_DATE_TO_STRING_FORMATTER)))
            .getApprovedRoutes();

        if (CollectionUtils.isEmpty(routes)) {
            log.info("No approved routes found for the given time interval");
        }
        return routes;
    }

    private Map<String, UUID> createEmailToUserIdMap() {

        return accountRepository.findAllActiveDriver()
            .stream()
            .collect(Collectors.toMap(Account::getEmail, Account::getUserId, (existing, replacement) -> existing));
    }

    private void processRoute(ApprovedRoute route, Map<String, UUID> driverEmailToUserIdMap, Map<String, DeliveryOrder> orderMap,
        List<DeliveryOrder> allOrdersToUpdate, List<BuildDeliveryTaskExceptionEventDto> exceptionEventsToNotify) {

        log.info("Processing route with ID: {}", route.getRoute().getId());

        DriverProperties driverProperties = getDriverProperties(route, driverEmailToUserIdMap);

        if (route.getOrders() == null) {

            log.warn("No orders found for route {}", route.getRoute().getId());
        } else {

            DeliveryTask task = deliveryTaskRepository.save(DeliveryTask.builder()
                .build()
                .create(route, driverProperties.driverUserId(), manualNumberGenerate.generateDeliveryTaskNumber()));

            UUID taskId = task.getId();
            rmRouteRepository.save(RmRoute.create(route, taskId));

            log.info("Saved task with ID {} for route {}", taskId, route.getRoute().getId());

            Map<String, Step> orderIdToStepMap = createOrderIdToDeliveryStepMap(route.getRoute().getSteps());
            processRouteOrders(route, task, orderMap, orderIdToStepMap, allOrdersToUpdate, exceptionEventsToNotify);

            deliveryTaskEventPublisher.publishDeliveryTaskBuildEvent(task, allOrdersToUpdate);
        }
    }

    private DriverProperties getDriverProperties(ApprovedRoute route, Map<String, UUID> driverEmailToUserIdMap) {

        if (route.getDriver() == null) {
            log.warn("The route does not have a driver, route ID: {}", route.getRoute().getId());
            return new DriverProperties(null, null);
        }

        String driverEmail = route.getDriver().getEmail();
        UUID driverUserId = this.getValueIgnoreKeyCase(driverEmailToUserIdMap, driverEmail);

        if (driverUserId != null) {
            return new DriverProperties(driverUserId, route.getDriver().getName());
        }

        log.warn("Driver not found for email: {}", driverEmail);

        return manuallyPullDriversFromRm(driverEmail);
    }

    private UUID getValueIgnoreKeyCase(Map<String, UUID> map, String key) {
        if (StringUtils.isBlank(key)) {
            log.warn("The key should not be blank");
            return null;
        }
        return map.entrySet().stream()
            .filter(entry -> {
                log.info("Checking map key {} for input key {}", entry.getKey(), key);
                return StringUtils.equalsIgnoreCase(entry.getKey(), key);
            })
            .map(Entry::getValue)
            .findFirst()
            .orElse(null);
    }

    private DriverProperties manuallyPullDriversFromRm(String driverEmail) {
        return routeManagerService.pullFromRouteManager().stream()
            .filter(account -> StringUtils.equalsIgnoreCase(account.getEmail(), driverEmail))
            .findFirst()
            .map(account -> {
                UUID userId = account.getUserId();
                return new DriverProperties(userId, account.getUserName());
            })
            .orElseGet(() -> {
                log.warn("Driver creation failed while creating task: {}", driverEmail);
                return new DriverProperties(null, null);
            });
    }


    private List<ApprovedRoute> identifyNewRoutes(LocalDate deliveryDate, List<DeliveryTask> existingTasks) {
        List<ApprovedRoute> approvedRoutes = getApprovedRoutesWithValidation(deliveryDate);
        if (CollectionUtils.isEmpty(approvedRoutes)) {
            return Collections.emptyList();
        }

        Set<String> existingRouteIds = getExistingRouteIds(existingTasks);

        List<ApprovedRoute> newRoutes = filterNewRoutes(approvedRoutes, existingRouteIds);

        logRouteAnalysis(deliveryDate, approvedRoutes.size(), existingRouteIds.size(), newRoutes.size());
        return newRoutes;
    }

    private List<ApprovedRoute> getApprovedRoutesWithValidation(LocalDate deliveryDate) {
        List<ApprovedRoute> approvedRoutes = getApprovedRoutes(deliveryDate);
        if (CollectionUtils.isEmpty(approvedRoutes)) {
            log.warn("No approved routes found for delivery date: {}, cannot sync", deliveryDate);
            return Collections.emptyList();
        }
        return approvedRoutes;
    }


    private List<ApprovedRoute> filterNewRoutes(List<ApprovedRoute> approvedRoutes, Set<String> existingRouteIds) {
        return approvedRoutes.stream()
            .filter(route -> route.getRoute() != null)
            .filter(route -> !existingRouteIds.contains(route.getRoute().getId()))
            .toList();
    }

    private void logRouteAnalysis(LocalDate deliveryDate, int totalRoutes, int existingRoutes, int newRoutes) {
        log.info("Route analysis for {}: total={}, existing={}, new={}",
            deliveryDate, totalRoutes, existingRoutes, newRoutes);
    }

    private void createTasksForNewRoutes(List<ApprovedRoute> newRoutes, LocalDate deliveryDate) {
        log.info("Creating tasks for {} new routes on delivery date: {}", newRoutes.size(), deliveryDate);

        buildTasksBaseOnApprovedRoutes(newRoutes);

        log.info("Completed syncing new routes for delivery date: {}. Created {} new tasks",
            deliveryDate, newRoutes.size());
    }


    public record DriverProperties(UUID driverUserId, String driverName) {

    }

    private void processRouteOrders(ApprovedRoute route, DeliveryTask task, Map<String, DeliveryOrder> orderMap,
        Map<String, Step> orderIdToStepMap, List<DeliveryOrder> allOrdersToUpdate,
        List<BuildDeliveryTaskExceptionEventDto> exceptionEventsToNotify) {

        route.getOrders().forEach((orderId, routeOrder) -> {
            String orderNumber = routeOrder.getName();
            DeliveryOrder deliveryOrder = orderMap.get(orderNumber);

            log.info("Processing order number {} for route {}, task {}",
                orderNumber, route.getRoute().getId(), task.getId());

            if (deliveryOrder == null) {
                log.warn("Order number {} not found in database", orderNumber);
                return;
            }

            log.info("Processing order number {} with status={}.", orderNumber, deliveryOrder.getState());

            processExistingOrder(task,
                orderIdToStepMap,
                allOrdersToUpdate,
                exceptionEventsToNotify,
                routeOrder,
                orderNumber,
                deliveryOrder);
        });
    }

    private void processExistingOrder(DeliveryTask task, Map<String, Step> orderIdToStepMap,
        List<DeliveryOrder> allOrdersToUpdate, List<BuildDeliveryTaskExceptionEventDto> exceptionEventsToNotify, Order routeOrder,
        String orderNumber, DeliveryOrder deliveryOrder) {

        DeliveryOrderStatus orderStatus = deliveryOrder.getState();
        Step orderStep = orderIdToStepMap.get(routeOrder.getId());
        log.info("processExistingOrder start, Order number {}, order status {}",
            orderNumber, orderStatus);
        if (orderStatus != DeliveryOrderStatus.CREATED) {
            handleNonCreatedOrderStatus(task, exceptionEventsToNotify, orderNumber, deliveryOrder, orderStatus, orderStep);
        } else {
            deliveryOrder.assignToTask(task, orderStep);
        }

        log.info("processExistingOrder end, Order number {}, order status {}",
            orderNumber,
            deliveryOrder.getState().name() + deliveryOrder.getStatus().name());

        log.info("processExistingOrder, Order  {}, assigning to order {}", orderNumber, deliveryOrder);
        allOrdersToUpdate.add(deliveryOrder);
        log.info("Order number {} associated with task {}", orderNumber, task.getId());
    }

    private void handleNonCreatedOrderStatus(DeliveryTask task, List<BuildDeliveryTaskExceptionEventDto> exceptionEventsToNotify,
        String orderNumber, DeliveryOrder deliveryOrder,
        DeliveryOrderStatus orderStatus, Step orderStep) {

        if (orderStatus == DeliveryOrderStatus.DELIVERED || orderStatus == DeliveryOrderStatus.CANCELED) {

            log.warn("The order is already delivered, please confirm current task {}, Order number: {}",
                task.getId(), orderNumber);

            handleExceptionOrder(task, exceptionEventsToNotify, orderNumber, deliveryOrder, orderStatus, orderStep);
        } else {
            log.warn("Revert to created state for order number {}, status {} in task {}",
                orderNumber, orderStatus.name(), task.getNumber());
            deliveryOrder.revertToCreated(deliveryOrder);
            deliveryOrder.assignToTask(task, orderStep);
        }
    }

    private void handleExceptionOrder(DeliveryTask task, List<BuildDeliveryTaskExceptionEventDto> exceptionEventsToNotify,
        String orderNumber, DeliveryOrder deliveryOrder,
        DeliveryOrderStatus orderStatus, Step orderStep) {

        log.info("handleExceptionOrder,deliveryOrder {} ", deliveryOrder);

        log.info("handleExceptionOrder, task={} ", task);
        UUID previousTaskId = deliveryOrder.getDeliveryTaskId();

        deliveryOrder.assignDeliveredOrderToTask(task, orderStep);
        task.setBuildHasException(true);

        DeliveryTask update = deliveryTaskRepository.update(task);
        log.info("handleExceptionOrder, task updated: {}", update);

        String deliveryDateStr =
            deliveryOrder.getDeliveredAt() == null ? null : DateUtils.formatInstantToDate(deliveryOrder.getDeliveredAt());

        BuildDeliveryTaskExceptionEventDto exceptionEvent = BuildDeliveryTaskExceptionEventDto.builder()
            .previousTaskId(previousTaskId)
            .currentTaskId(task.getId())
            .currentTaskNumber(update.getNumber())
            .orderId(deliveryOrder.getId())
            .orderNumber(orderNumber)
            .orderStatus(orderStatus.name())
            .planDeliveryDate(task.getDeliveryDate())
            .deliveryDate(deliveryDateStr)
            .customerNote(deliveryOrder.getCustomerNotes())
            .build();

        log.info("handleExceptionOrder, creating exception event for order {}, task {}, exceptionEvent = {}",
            orderNumber, task.getId(), exceptionEvent);

        exceptionEventsToNotify.add(exceptionEvent);
    }

    /**
     * Extracts unique order numbers from approved routes.
     * Filters out null routes, empty order collections, and invalid order names.
     *
     * @param routes the list of approved routes (can be null or empty)
     * @return an immutable set of unique, trimmed order numbers
     */
    private Set<String> extractOrderNumbersTrimFromRoutes(List<ApprovedRoute> routes) {
        if (CollectionUtils.isEmpty(routes)) {
            return Collections.emptySet();
        }

        return routes.stream()
            .filter(this::hasValidOrderCollection)
            .flatMap(route -> extractOrderNumbersTrim(route.getOrders().values()).stream())
            .collect(Collectors.toUnmodifiableSet());
    }

    /**
     * Validates that route has a non-empty orders collection
     */
    private boolean hasValidOrderCollection(ApprovedRoute route) {
        return route != null
            && route.getOrders() != null
            && !route.getOrders().isEmpty();
    }

    private Set<String> extractOrderNumbersTrim(Collection<Order> orders) {
        return orders.stream()
            .map(Order::getName)
            .filter(StringUtils::isNotBlank)
            .map(String::trim)
            .collect(Collectors.toUnmodifiableSet());
    }

    private Map<String, DeliveryOrder> findCreatedOrdersByOrderNumbers(Set<String> orderNumbers) {
        if (CollectionUtils.isEmpty(orderNumbers)) {
            return Collections.emptyMap();
        }

        return deliveryOrderRepository.findAllByOrderNumberIn(orderNumbers)
            .stream()
//            .filter(this::isOrderInCreatedState)
            .collect(Collectors.toMap(
                DeliveryOrder::getOrderNumber,
                Function.identity(),
                (existing, replacement) -> existing
            ));
    }

    private Map<String, DeliveryOrder> findOrdersByOrderNumbers(Set<String> orderNumbers) {
        if (CollectionUtils.isEmpty(orderNumbers)) {
            return Collections.emptyMap();
        }

        return deliveryOrderRepository.findAllByOrderNumberIn(orderNumbers)
            .stream()
            .collect(Collectors.toMap(
                DeliveryOrder::getOrderNumber,
                Function.identity(),
                (existing, replacement) -> existing
            ));
    }

    public Map<String, Step> createOrderIdToDeliveryStepMap(List<Step> steps) {
        if (CollectionUtils.isEmpty(steps)) {
            return new LinkedHashMap<>();
        }

        Map<String, Step> result = new LinkedHashMap<>();
        AtomicInteger sequenceCounter = new AtomicInteger(1);

        steps.stream()
            .filter(step -> step != null && OrderStepType.DELIVERY.getValue().equalsIgnoreCase(step.getType()))
            .forEach(step -> {
                if (!result.containsKey(step.getOrderId())) {
                    step.setOrderSequence(sequenceCounter.getAndIncrement());
                    result.put(step.getOrderId(), step);
                }
            });

        return result;
    }

    /**
     * Rebuilds a delivery task based on the latest route information from Route Manager.
     * This method will synchronize the delivery task with the current approved route data,
     * adding new orders, updating existing ones, and removing orders no longer in the route.
     *
     * @param taskId the ID of the task to rebuild
     * @return true if successful
     * @throws DeliveryBusinessException if task, route, or required data is not found
     */
    @Transactional
    public boolean rebuildTask(UUID taskId) {
        log.info("Rebuilding delivery task with ID: {}", taskId);

        DeliveryTask task = retrieveDeliveryTask(taskId);
        String routeId = retrieveRouteIdForTask(taskId);
        ApprovedRoute targetRoute = retrieveApprovedRoute(routeId);

        // save the source data
        rmRouteRepository.save(RmRoute.create(targetRoute, taskId));

        Set<Order> routeOrders = extractOrders(targetRoute);
        Set<Order> rescheduledOrders = extractRescheduledOrders(targetRoute);
        Set<String> routeOrderNumbers = extractOrderNumbersTrim(routeOrders);

        Map<String, DeliveryOrder> existingOrders = mapOrdersByNumber(deliveryOrderRepository.findAllByDeliveryTaskId(taskId));
        log.info("Found existing orders: {}", existingOrders);
        Map<String, DeliveryOrder> availableOrders = findOrdersByOrderNumbers(routeOrderNumbers);
        log.info("Found available orders: {}", availableOrders);

        Set<String> orderNumbersToAdd = difference(routeOrderNumbers, existingOrders.keySet());
        Set<String> orderNumbersToRemoveOrRescheduled = difference(existingOrders.keySet(), routeOrderNumbers);
        Set<String> orderNumbersToUpdate = intersection(existingOrders.keySet(), routeOrderNumbers);
        orderNumbersToRemoveOrRescheduled.addAll(extractOrderNumbersTrim(rescheduledOrders));

        log.info("Orders to add: {}, Orders to remove: {}, Orders to update: {}",
            orderNumbersToAdd,
            orderNumbersToRemoveOrRescheduled,
            orderNumbersToUpdate);

        Map<String, Step> orderStepMap = createOrderIdToDeliveryStepMap(targetRoute, routeOrders);
        log.info("Order step map: {}", orderStepMap);

        List<DeliveryOrder> ordersToAdd = prepareNewOrders(orderNumbersToAdd, task, availableOrders, orderStepMap);
        List<DeliveryOrder> ordersToUpdate = prepareOrderUpdates(orderNumbersToUpdate, existingOrders, orderStepMap);
        List<DeliveryOrder> ordersToRemoveOrRescheduled = prepareOrderRemovalsOrRescheduled(orderNumbersToRemoveOrRescheduled,
            existingOrders);

        int persistOrderChangesSize = persistOrderChanges(ordersToAdd, ordersToUpdate, ordersToRemoveOrRescheduled);
        boolean needUpdateTask = persistOrderChangesSize > 0;

        log.info("Persist size: {}, needUpdateTask boolean = : {}", persistOrderChangesSize, needUpdateTask);

        if (needUpdateTask) {
            task.updateBreak(targetRoute);
        }

        DriverProperties driverProperties = buildDriverPropertiesFromRoute(targetRoute);
        log.info("Driver properties: {}", driverProperties);

        if (!Objects.equals(driverProperties.driverUserId, task.getDriverUserId())) {
            task.assignDriverToTask(driverProperties);
            needUpdateTask = true;
        }

        if (needUpdateTask) {
            log.info("Task needUpdateTask: {}", task);
            deliveryTaskRepository.update(task);
        }

        return true;
    }

    private DriverProperties buildDriverPropertiesFromRoute(ApprovedRoute targetRoute) {

        return Optional.ofNullable(targetRoute.getDriver())
            .map(driver -> {
                Optional<Account> routeDriver = accountRepository.findByEmail(driver.getEmail());
                Map<String, UUID> emailMap = routeDriver
                    .map(account -> Map.of(driver.getEmail(), account.getUserId()))
                    .orElse(Map.of());
                return getDriverProperties(targetRoute, emailMap);
            })
            .orElse(new DriverProperties(null, null));
    }

    private Set<String> difference(Set<String> first, Set<String> second) {
        Set<String> result = new HashSet<>(first);
        result.removeAll(second);
        return result;
    }

    private Set<String> intersection(Set<String> first, Set<String> second) {
        return first.stream()
            .filter(second::contains)
            .collect(Collectors.toSet());
    }

    private Map<String, Step> createOrderIdToDeliveryStepMap(ApprovedRoute route, Set<Order> orders) {
        Map<String, Step> idToStep = createStepMapFromRoute(route);
        Map<String, String> idToNumber = orders.stream()
            .collect(Collectors.toMap(Order::getId, Order::getName));

        Map<String, Step> result = new HashMap<>();
        idToStep.forEach((id, step) -> {
            String orderNumber = idToNumber.get(id);
            if (orderNumber != null) {
                result.put(orderNumber, step);
            }
        });
        return result;
    }

    private List<DeliveryOrder> prepareNewOrders(
        Set<String> orderNumbers,
        DeliveryTask task,
        Map<String, DeliveryOrder> availableOrders,
        Map<String, Step> stepMap) {
        return orderNumbers.stream()
            .map(availableOrders::get)
            .filter(Objects::nonNull)
            .peek(order -> {
                log.info("Order: {}", order);
                Step step = stepMap.get(order.getOrderNumber());
                order.assignToTask(task, step);
                log.info("Order {} prepared for addition to task {}", order.getOrderNumber(), task.getId());
            })
            .collect(Collectors.toList());
    }

    private List<DeliveryOrder> prepareOrderUpdates(
        Set<String> orderNumbers,
        Map<String, DeliveryOrder> existingOrders,
        Map<String, Step> stepMap) {

        return orderNumbers.stream()
            .map(existingOrders::get)
            .filter(Objects::nonNull)
            .peek(order -> {
                log.info("Order: {}", order);
                Step step = stepMap.get(order.getOrderNumber());
                order.updateTaskAssignment(order.getDeliveryDate(), step);
                log.info("Order {} prepared for update", order.getOrderNumber());
            })
            .collect(Collectors.toList());
    }

    private List<DeliveryOrder> prepareOrderRemovalsOrRescheduled(
        Set<String> orderNumbers,
        Map<String, DeliveryOrder> existingOrders) {

        List<DeliveryOrder> result = new ArrayList<>();

        for (String orderNumber : orderNumbers) {
            DeliveryOrder order = existingOrders.get(orderNumber);
            if (order == null) {
                continue;
            }

            log.info("prepareOrderRemovalsOrRescheduled, Order number {}, order status {}",
                order.getOrderNumber(), order.getStatus());

            if (order.getStatus() == DeliveryOrderStatus.CANCELED || order.getStatus() == DeliveryOrderStatus.DELIVERED) {
                log.warn("prepareOrderRemovalsOrRescheduled, order {} is already canceled or delivered, removing from task",
                    order.getOrderNumber());
                order.removeFromTask(order);
            } else {
                order.revertToCreated(order);
            }

            log.info("Order {} prepared for removal or rescheduled", order.getOrderNumber());

            result.add(order);
        }

        return result;
    }


    private int persistOrderChanges(List<DeliveryOrder> toAdd, List<DeliveryOrder> toUpdate,
        List<DeliveryOrder> ordersToRemoveOrRescheduled) {

        List<DeliveryOrder> allChanges = Stream.of(toAdd, toUpdate, ordersToRemoveOrRescheduled)
            .flatMap(Collection::stream)
            .collect(Collectors.toList());

        List<DeliveryOrder> processed = deliveryOrderRepository.updateAll(allChanges);
        log.info("Processed {} orders: {}",
            processed.size(),
            processed.stream().map(DeliveryOrder::getOrderNumber).collect(Collectors.toSet()));
        return processed.size();
    }

    /**
     * Extract orders that are marked as rescheduled from the given route
     *
     * @param targetRoute the target route containing orders
     * @return a set of orders that are marked as rescheduled
     */
    private Set<Order> extractRescheduledOrders(ApprovedRoute targetRoute) {
        if (targetRoute == null || targetRoute.getOrders() == null) {
            return Collections.emptySet();
        }

        Map<String, Step> orderIdToStep = createStepMapFromRoute(targetRoute);

        return targetRoute.getOrders().values().stream()
            .filter(Objects::nonNull)
            .filter(order -> {
                Step step = orderIdToStep.get(order.getId());
                if (step == null) {
                    return false; // No step means no tracking data, so not rescheduled
                }

                TrackingData trackingData = step.getTrackingData();
                // Order is rescheduled if tracking data status equals "reschedule"
                return trackingData != null && Objects.equals(trackingData.getStatus(), "reschedule");
            })
            .collect(Collectors.toSet());
    }

    /**
     * Extract orders that are not marked for rescheduling from the given route
     *
     * @param targetRoute the target route containing orders
     * @return a set of valid orders excluding those marked for rescheduling
     * @throws DeliveryBusinessException if the route or orders are null
     */
    private Set<Order> extractOrders(ApprovedRoute targetRoute) {
        if (targetRoute == null || targetRoute.getOrders() == null) {
            throw new DeliveryBusinessException(
                ErrorCodeEnums.DELIVERY_ROUTE_ORDER_NOT_FOUND.getCode(),
                ErrorCodeEnums.DELIVERY_ROUTE_ORDER_NOT_FOUND.getMessage());
        }

        Map<String, Step> orderIdToStep = createStepMapFromRoute(targetRoute);

        return targetRoute.getOrders().values().stream()
            .filter(Objects::nonNull)
            .filter(order -> {
                Step step = orderIdToStep.get(order.getId());

                // If there's no step, include the order
                if (step == null) {
                    return true;
                }

                TrackingData trackingData = step.getTrackingData();
                // Check if order should be excluded (has tracking data with status other than "reschedule")
                if (trackingData != null && Objects.equals(trackingData.getStatus(), "reschedule")) {
                    log.info("Order {} is rescheduled, remove from the route.", order.getName());
                    return false;
                }

                return true;
            })
            .collect(Collectors.toSet());
    }

    private DeliveryTask retrieveDeliveryTask(UUID taskId) {
        DeliveryTask task = deliveryTaskRepository.findById(taskId);
        if (task == null) {
            throw new DeliveryBusinessException(
                ErrorCodeEnums.DELIVERY_TASK_NOT_FOUND.getCode(),
                ErrorCodeEnums.DELIVERY_TASK_NOT_FOUND.getMessage());
        }
        return task;
    }

    private String retrieveRouteIdForTask(UUID taskId) {
        List<RmRoute> rmRoutes = rmRouteRepository.findByDeliveryTaskId(taskId);
        if (rmRoutes.isEmpty()) {
            throw new DeliveryBusinessException(
                ErrorCodeEnums.DELIVERY_TASK_ROUTE_NOT_FOUND.getCode(),
                ErrorCodeEnums.DELIVERY_TASK_ROUTE_NOT_FOUND.getMessage());
        }

        String routeId = rmRoutes.getFirst().getRouteId();
        log.info("Found route ID: {} for task ID: {}", routeId, taskId);
        return routeId;
    }


    private Set<String> getExistingRouteIds(List<DeliveryTask> existingTasks) {
        if (CollectionUtils.isEmpty(existingTasks)) {
            return Collections.emptySet();
        }

        Set<UUID> taskIds = extractTaskIds(existingTasks);
        List<RmRoute> rmRoutes = rmRouteRepository.findByDeliveryTaskIdIn(taskIds);
        Set<String> routeIds = extractValidRouteIds(rmRoutes);

        logRouteRetrievalResult(existingTasks.size(), routeIds.size());
        detectAndLogMissingRoutes(taskIds, rmRoutes);

        return routeIds;
    }

    private Set<UUID> extractTaskIds(List<DeliveryTask> tasks) {
        return tasks.stream()
            .map(DeliveryTask::getId)
            .collect(Collectors.toSet());
    }

    private Set<String> extractValidRouteIds(List<RmRoute> rmRoutes) {
        return rmRoutes.stream()
            .map(RmRoute::getRouteId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());
    }

    private void logRouteRetrievalResult(int taskCount, int routeCount) {
        log.info("Retrieved {} route IDs for {} tasks", routeCount, taskCount);
    }

    private void detectAndLogMissingRoutes(Set<UUID> allTaskIds, List<RmRoute> foundRoutes) {
        if (foundRoutes.size() >= allTaskIds.size()) {
            return;
        }

        Set<UUID> foundTaskIds = foundRoutes.stream()
            .map(RmRoute::getDeliveryTaskId)
            .collect(Collectors.toSet());

        Set<UUID> missingRouteTaskIds = allTaskIds.stream()
            .filter(taskId -> !foundTaskIds.contains(taskId))
            .collect(Collectors.toSet());

        log.warn("Data inconsistency detected: {} tasks missing route records: {}",
            missingRouteTaskIds.size(), missingRouteTaskIds);
    }

    private ApprovedRoute retrieveApprovedRoute(String routeId) {
        CurrentRoutes currentRoute = routeManagerAdaptor.getCurrentRoute(routeId);
        if (currentRoute == null || currentRoute.getRoutes() == null || currentRoute.getRoutes().isEmpty()) {
            log.error("No current route found for route ID: {}", routeId);
            throw new DeliveryBusinessException(
                ErrorCodeEnums.DELIVERY_ROUTE_NOT_FOUND.getCode(),
                "Route with ID " + routeId + " not found in Route Manager");
        }

        ApprovedRoute targetRoute = convertToApprovedRoute(currentRoute, routeId);
        log.info("Found and converted current route for route ID: {}", routeId);
        return targetRoute;
    }

    private ApprovedRoute convertToApprovedRoute(CurrentRoutes currentRoute, String routeId) {
        ApprovedRoute approvedRoute = new ApprovedRoute();

        if (currentRoute == null || currentRoute.getRoutes() == null || currentRoute.getRoutes().isEmpty()
            || currentRoute.getRoutes().get(routeId) == null) {
            log.warn("Route {} not found in current routes", routeId);
            return approvedRoute;
        }

        Route route = currentRoute.getRoutes().get(routeId);
        approvedRoute.setRoute(route);

        if (currentRoute.getOrders() != null) {
            approvedRoute.setOrders(currentRoute.getOrders());
        }

        if (currentRoute.getDrivers() != null && !currentRoute.getDrivers().isEmpty()) {
            approvedRoute.setDriver(currentRoute.getDrivers().values().iterator().next());
        }

        if (currentRoute.getVehicles() != null && !currentRoute.getVehicles().isEmpty()) {
            approvedRoute.setVehicle(currentRoute.getVehicles().values().iterator().next());
        }

        return approvedRoute;
    }

    private Map<String, DeliveryOrder> mapOrdersByNumber(List<DeliveryOrder> orders) {
        return orders.stream()
            .collect(Collectors.toMap(
                DeliveryOrder::getOrderNumber,
                Function.identity(),
                (existing, replacement) -> existing));
    }

    private Map<String, Step> createStepMapFromRoute(ApprovedRoute route) {
        return route.getRoute() != null && route.getRoute().getSteps() != null
            ? createOrderIdToDeliveryStepMap(route.getRoute().getSteps())
            : new HashMap<>();
    }

    @Transactional
    public void inProgress(UUID deliveryTaskId) {
        DeliveryTask deliveryTask = retrieveDeliveryTask(deliveryTaskId);
        if (deliveryTask.getStatus() == DeliveryTaskStatus.IN_PROGRESS) {
            log.info("Delivery task {} is already in progress", deliveryTaskId);
            return;
        }
        deliveryTask.updateStatus(DeliveryTaskStatus.IN_PROGRESS);
        DeliveryTask updated = deliveryTaskRepository.update(deliveryTask);
        DeliveryTaskDto deliveryTaskDto = DeliveryTaskDtoApplicationMapper.INSTANCE.domainToDto(updated);
        dispatcher.dispatch(
            BusinessEventFactory.build(DeliveryTaskInProgressPayloadDto.builder()
                .deliveryTaskId(deliveryTaskDto.getId())
                .data(deliveryTaskDto)
                .build()));
    }

    /**
     * Complete a delivery task
     *
     * @param taskId the ID of the task to complete
     * @return the completed task as DTO
     */
    @Transactional
    public DeliveryTaskDto completeTask(UUID taskId) {
        log.info("Completing delivery task: {}", taskId);

        // Retrieve and validate task with its orders
        DeliveryTask task = retrieveAndValidateTask(taskId);

        // Complete task and publish event
        return completeTaskAndPublishEvent(task);
    }

    /**
     * Complete multiple delivery tasks in batch
     *
     * @param taskIds List of task IDs to complete
     * @return list of completed tasks as DTOs
     */
    @Transactional
    public List<UUID> batchCompleteTasks(List<UUID> taskIds) {
        if (CollectionUtils.isEmpty(taskIds)) {
            return Collections.emptyList();
        }

        log.info("Starting batch completion for {} delivery tasks", taskIds.size());

        List<UUID> failedCompleteTaskIds = new ArrayList<>();

        for (UUID taskId : taskIds) {
            try {
                completeTaskAndPublishEvent(retrieveAndValidateTask(taskId));
            } catch (DeliveryBusinessException e) {
                handleFailure(taskId, e.getMessage(), e, false);
                failedCompleteTaskIds.add(taskId);
            } catch (Exception e) {
                handleFailure(taskId, e.getMessage(), e, true);
                failedCompleteTaskIds.add(taskId);
            }
        }

        log.info("Batch task completion finished: total = {}, failed = {}",
            taskIds.size(), failedCompleteTaskIds.size());

        return failedCompleteTaskIds;
    }

    private void handleFailure(UUID taskId, String message, Exception e, boolean isSystemError) {
        if (isSystemError) {
            log.error("System error completing task with ID {}: {}", taskId, message, e);
        } else {
            log.warn("Business rule prevented task completion. Task ID = {}, Reason = {}", taskId, message, e);
        }
    }

    /**
     * Retrieves task by ID and validates its orders
     *
     * @param taskId the task ID
     * @return the validated task
     * @throws DeliveryBusinessException if task not found or has invalid orders
     */
    private DeliveryTask retrieveAndValidateTask(UUID taskId) {
        DeliveryTask task = retrieveDeliveryTask(taskId);
        validateOrderStatuses(taskId);
        return task;
    }

    /**
     * Validates that all orders for a task are in delivered or canceled status
     *
     * @param taskId the task ID
     * @throws DeliveryBusinessException if any order has invalid status
     */
    private void validateOrderStatuses(UUID taskId) {
        List<DeliveryOrder> orders = deliveryOrderRepository.findAllByDeliveryTaskId(taskId);

        boolean hasValidOrders = orders.stream()
            .allMatch(order -> validOrderStatus(order.getStatus()) || order.getRescheduleType() != null);

        if (!hasValidOrders) {
            throw new DeliveryBusinessException(
                ErrorCodeEnums.DELIVERY_TASK_ORDERS_NOT_COMPLETE.getCode(),
                ErrorCodeEnums.DELIVERY_TASK_ORDERS_NOT_COMPLETE.getMessage());
        }
    }

    /**
     * Checks if an order status allows task completion
     *
     * @param status the order status
     * @return true if status allows completion
     */
    private boolean validOrderStatus(DeliveryOrderStatus status) {
        return status == DeliveryOrderStatus.DELIVERED || status == DeliveryOrderStatus.CANCELED;
    }

    private DeliveryTaskDto completeTaskAndPublishEvent(DeliveryTask task) {
        task.updateStatus(DeliveryTaskStatus.COMPLETED);
        DeliveryTask updated = deliveryTaskRepository.update(task);
        DeliveryTaskDto taskDto = DeliveryTaskDtoApplicationMapper.INSTANCE.domainToDto(updated);

        publishTaskCompletedEvent(taskDto);

        return taskDto;
    }

    private void publishTaskCompletedEvent(DeliveryTaskDto taskDto) {
        dispatcher.dispatch(
            BusinessEventFactory.build(DeliveryTaskCompletedPayloadDto.builder()
                .deliveryTaskId(taskDto.getId())
                .data(taskDto)
                .build()));
    }

    public DeliveryTaskDto reassignTask(UUID deliveryTaskId, ReassignDeliveryTaskCommand command) {
        log.info("Reassigning delivery task with ID: {}", deliveryTaskId);
        DeliveryTask task = retrieveAndValidateTask(deliveryTaskId, command);
        final String previousDriverName = task.getDriverUserName();
        Account newDriverAccount = getNewDriverAccountAndValidateStatus(command.getCurrentUserId());
        DeliveryTask updatedTask = reassignTaskToNewDriver(task, newDriverAccount);

        return publishReassignmentEvent(updatedTask, command.getPreviousUserId(), previousDriverName);
    }

    private DeliveryTask retrieveAndValidateTask(UUID deliveryTaskId, ReassignDeliveryTaskCommand command) {
        DeliveryTask task = retrieveDeliveryTask(deliveryTaskId);

        validateTaskStatus(task);
        validatePreviousDriver(task, command.getPreviousUserId());

        return task;
    }

    private void validateTaskStatus(DeliveryTask task) {
        if (task.getStatus() == DeliveryTaskStatus.COMPLETED) {
            throw new DeliveryBusinessException(
                ErrorCodeEnums.DELIVERY_TASK_CAN_NOT_REASSIGN.getCode(),
                ErrorCodeEnums.DELIVERY_TASK_CAN_NOT_REASSIGN.getMessage());
        }
    }

    private void validatePreviousDriver(DeliveryTask task, UUID previousUserId) {
        if (!previousUserId.equals(task.getDriverUserId())) {
            throw new DeliveryBusinessException(
                ErrorCodeEnums.DELIVERY_TASK_REASSIGNED.getCode(),
                ErrorCodeEnums.DELIVERY_TASK_REASSIGNED.getMessage());
        }
    }

    private Account getNewDriverAccountAndValidateStatus(UUID currentUserId) {
        Account account = accountRepository.findByUserId(currentUserId)
            .orElseThrow(() -> new DeliveryBusinessException(
                ErrorCodeEnums.DRIVER_ACCOUNT_NOT_FOUND.getCode(),
                ErrorCodeEnums.DRIVER_ACCOUNT_NOT_FOUND.getMessage()));
        if (account.getStatus() != AccountStatus.ACTIVE) {
            throw new DeliveryBusinessException(
                ErrorCodeEnums.DRIVER_ACCOUNT_NOT_ACTIVE.getCode(),
                ErrorCodeEnums.DRIVER_ACCOUNT_NOT_ACTIVE.getMessage());
        }
        return account;
    }

    private DeliveryTask reassignTaskToNewDriver(DeliveryTask task, Account newDriverAccount) {
        DriverProperties driverProperties = new DriverProperties(
            newDriverAccount.getUserId(),
            newDriverAccount.getUserName()
        );

        task.assignDriverToTask(driverProperties);
        return deliveryTaskRepository.update(task);
    }

    private DeliveryTaskDto publishReassignmentEvent(
        DeliveryTask updatedTask,
        UUID previousUserId,
        String previousUserName
    ) {
        DeliveryTaskDto taskDto = DeliveryTaskDtoApplicationMapper.INSTANCE.domainToDto(updatedTask);

        DeliveryTaskReassignedEventDto eventData = DeliveryTaskReassignedEventDto.builder()
            .previousUserId(previousUserId)
            .previousUserName(previousUserName)
            .currentUserId(updatedTask.getDriverUserId())
            .currentUserName(updatedTask.getDriverUserName())
            .build();

        DeliveryTaskReassignedPayloadDto payload = DeliveryTaskReassignedPayloadDto.builder()
            .deliveryTaskId(taskDto.getId())
            .data(eventData)
            .build();

        dispatcher.dispatch(BusinessEventFactory.build(payload));

        return taskDto;
    }

    public DeliveryTaskDto update(UUID deliveryTaskId, UpdateDeliveryTaskCommand command) {
        DeliveryTask task = retrieveDeliveryTask(deliveryTaskId);

        task.update(command);
        DeliveryTask updated = deliveryTaskRepository.update(task);
        DeliveryTaskDto taskDto = DeliveryTaskDtoApplicationMapper.INSTANCE.domainToDto(updated);

        dispatcher.dispatch(
            BusinessEventFactory.build(DeliveryTaskUpdatedPayloadDto.builder()
                .deliveryTaskId(deliveryTaskId)
                .data(DeliveryTaskUpdatedEventDto.builder()
                    .preCheck(command.getPreCheck())
                    .postCheck(command.getPostCheck())
                    .build())
                .build()));

        return taskDto;

    }
}
