package com.mercaso.wms.application.dto.view;

import com.mercaso.wms.application.dto.BaseDto;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SearchCrossDockTaskView extends BaseDto {

    private UUID id;

    private String number;

    private String pickerUserName;

    private Integer totalPickedQty;

    private Integer totalCrossDockedQty;

    private Instant createdAt;

    private Instant updatedAt;


}
