package com.mercaso.wms.infrastructure.repository.batchitems.jpa;

import com.mercaso.wms.infrastructure.repository.batchitems.jpa.dataobject.BatchItemsDo;
import java.util.List;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface BatchItemsJpaDao extends JpaRepository<BatchItemsDo, UUID> {

    @Query(value = """
        select * from batch_items  
        where deleted_at is null 
        and batch_id = :batchId 
        and source = :source 
        and big_order = :bigOrder 
        and (:departments is null or department in :departments) """, nativeQuery = true)
    List<BatchItemsDo> findBy(@Param("batchId") UUID batchId,
        @Param("source") String source,
        @Param("departments") List<String> departments,
        @Param("bigOrder") boolean bigOrder);

    @Query(value = "select * from batch_items where deleted_at is null and batch_id = :batchId and source = :source and department not in :departments",
        nativeQuery = true)
    List<BatchItemsDo> findByBatchIdAndSourceAndDepartmentNotIn(@Param("batchId") UUID batchId,
        @Param("source") String source,
        @Param("departments") List<String> departments);

    List<BatchItemsDo> findByBatchIdAndSource(UUID batchId, String source);

    @Query(value = "select distinct order_number from batch_items where deleted_at is null and batch_id = :batchId",
        nativeQuery = true)
    List<String> findOrderNumbersByBatchId(UUID batchId);

    List<BatchItemsDo> findByBatchIdAndOrderNumberIn(UUID batchId, List<String> orderNumbers);

    @Query(value = "select bid from BatchItemsDo bid left join BatchDo bd on bid.batchId = bd.id where bd.tag = :deliveryDate and bid.source = :source ")
    Page<BatchItemsDo> findBySourceAndDeliveryDate(@Param("source") String source,
        @Param("deliveryDate") String deliveryDate,
        Pageable pageable);

    List<BatchItemsDo> findByBatchIdAndSourceAndLocationName(UUID batchId, String source, String locationName);

}
