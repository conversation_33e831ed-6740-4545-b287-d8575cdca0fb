package com.mercaso.wms.infrastructure.event.applicationevent.listener;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.wms.application.dto.event.PickingTaskCompletedApplicationEvent;
import com.mercaso.wms.application.dto.event.PickingTaskPartiallyCompletedApplicationEvent;
import com.mercaso.wms.application.dto.event.ReceivingTaskReceivedApplicationEvent;
import com.mercaso.wms.application.service.ReceivingTaskApplicationService;
import com.mercaso.wms.application.service.ShippingOrderApplicationService;
import com.mercaso.wms.application.service.pickingtask.AutoAssignBatchLevelPickingTaskService;
import com.mercaso.wms.application.service.pickingtask.CreateBatchLevelPickingTaskService;
import com.mercaso.wms.application.service.pickingtask.CreateCoolerPickingTaskService;
import com.mercaso.wms.application.service.pickingtask.CreateNAPickingTaskService;
import com.mercaso.wms.application.service.pickingtask.CreatePhotoStudioPickingTaskService;
import com.mercaso.wms.application.service.pickingtask.CreatePickingTaskService;
import com.mercaso.wms.infrastructure.retryabletransaction.RetryableTransaction;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.event.TransactionalEventListener;


@Slf4j
@Component
@RequiredArgsConstructor
public class PickingTaskApplicationEventListener {

    private final CreatePickingTaskService createOrderLevelPickingTaskService;

    private final CreateBatchLevelPickingTaskService createBatchLevelPickingTaskService;

    private final CreateNAPickingTaskService createNAPickingTaskService;

    private final CreateCoolerPickingTaskService createCoolerPickingTaskService;

    private final CreatePhotoStudioPickingTaskService createPhotoStudioPickingTaskService;

    private final AutoAssignBatchLevelPickingTaskService autoAssignBatchLevelPickingTaskService;

    private final ShippingOrderApplicationService shippingOrderApplicationService;

    private final ReceivingTaskApplicationService receivingTaskApplicationService;

    private final FeatureFlagsManager featureFlagsManager;

    public void handleBatchCreatedEvent(UUID batchId) {
        log.info("handle batch.created.event.{}", batchId);
        shippingOrderApplicationService.markInProgress(batchId);
        createPhotoStudioPickingTaskService.createPickingTask(batchId);
        createNAPickingTaskService.createPickingTask(batchId);
        createCoolerPickingTaskService.createPickingTask(batchId);
        createOrderLevelPickingTaskService.createPickingTask(batchId);
        createBatchLevelPickingTaskService.createPickingTask(batchId);
        autoAssignBatchLevelPickingTaskService.assignPickingTask(batchId);
        receivingTaskApplicationService.createReceivingTask(batchId);
        log.info("handle.batch.created.event.{}.done", batchId);
    }

    @TransactionalEventListener
    public void handlePickingTaskCompletedEvent(PickingTaskCompletedApplicationEvent pickingTaskCompletedApplicationEvent) {
        log.info("handle.picking.task.completed.event.{}",
            pickingTaskCompletedApplicationEvent.getPayload().getPickingTaskId());
        try {
            shippingOrderApplicationService.updateOrderStatusByPickingTaskId(pickingTaskCompletedApplicationEvent.getPayload()
                .getPickingTaskId());
        } catch (Exception e) {
            log.warn("handle picking task completed event: {}",
                pickingTaskCompletedApplicationEvent.getPayload().getPickingTaskId(),
                e);
        }
    }

    @TransactionalEventListener
    public void handlePickingTaskPartiallyCompletedEvent(PickingTaskPartiallyCompletedApplicationEvent pickingTaskPartiallyCompletedApplicationEvent) {
        log.info("handle.picking.task.partially completed.event.{}",
            pickingTaskPartiallyCompletedApplicationEvent.getPayload().getPickingTaskId());
        try {
            shippingOrderApplicationService.updateOrderStatusByPickingTaskId(pickingTaskPartiallyCompletedApplicationEvent.getPayload()
                .getPickingTaskId());
        } catch (Exception e) {
            log.warn("handle picking task partially completed event: {}",
                pickingTaskPartiallyCompletedApplicationEvent.getPayload().getPickingTaskId(),
                e);
        }
    }

    @Async
    @RetryableTransaction(propagation = Propagation.REQUIRES_NEW)
    @TransactionalEventListener
    public void handleReceivingTaskReceivedEvent(ReceivingTaskReceivedApplicationEvent receivingTaskReceivedApplicationEvent) {
        log.info("handle.receiving.task.received.event.{}",
            receivingTaskReceivedApplicationEvent.getPayload().getReceivingTaskId());
        shippingOrderApplicationService.updateOrderStatusByReceivingTaskId(receivingTaskReceivedApplicationEvent.getPayload()
            .getReceivingTaskId());
    }
}
