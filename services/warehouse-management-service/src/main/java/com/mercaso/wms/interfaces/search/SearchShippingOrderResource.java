package com.mercaso.wms.interfaces.search;

import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.dto.shippingorder.ShippingOrderDto;
import com.mercaso.wms.application.query.ShippingOrderQuery;
import com.mercaso.wms.application.query.ShippingOrderQuery.SortType;
import com.mercaso.wms.application.searchservice.ShippingOrderSearchService;
import com.mercaso.wms.interfaces.util.SortByParseUtil;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Validated
@RestController
@RequestMapping("/search/shipping-orders")
@RequiredArgsConstructor
public class SearchShippingOrderResource {

    private final ShippingOrderSearchService shippingOrderSearchService;

    @PreAuthorize("hasAuthority('wms:read:shipping-orders')")
    @GetMapping
    public Result<ShippingOrderDto> searchShippingOrders(
        @RequestParam(value = "page", defaultValue = "1") @Min(value = 1, message = "Page number must be greater than 0")
        int page,
        @RequestParam(value = "pageSize", defaultValue = "20") @Min(value = 1, message = "Page size must be greater than 0")
        @Max(value = 1000, message = "Page size must be less than or equal to 1000")
        int pageSize,
        @RequestParam(value = "orderNumber", required = false) String orderNumber,
        @RequestParam(value = "deliveryDate", required = false) LocalDate deliveryDate,
        @RequestParam(value = "statuses", required = false) List<String> statuses,
        @RequestParam(value = "sort", required = false) SortType sort) {
        log.info("Searching shipping orders with orderNumber: {}, deliveryDate: {}, statuses: {}, sort: {}",
            orderNumber,
            deliveryDate,
            statuses,
            sort);
        Page<ShippingOrderDto> shippingOrderDtoPage = shippingOrderSearchService.searchShippingOrders(
            ShippingOrderQuery.builder()
                .orderNumber(orderNumber)
                .deliveryDate(deliveryDate == null ? null : deliveryDate.toString())
                .statuses(statuses)
                .sort(sort)
                .build(),
            PageRequest.of(page - 1,
                pageSize,
                Objects.requireNonNull(null == sort ? SortByParseUtil.getSortField(SortType.CREATED_AT_DESC.name())
                    : SortByParseUtil.getSortField(sort.name()))));
        return new Result<>(shippingOrderDtoPage.getContent(), shippingOrderDtoPage.getTotalElements());
    }

}
