package com.mercaso.data.metrics.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.mercaso.data.dto.CustomPageable;
import com.mercaso.data.metrics.dto.SearchStoreAddressDto;
import com.mercaso.data.metrics.dto.StoreAddressFilter;
import com.mercaso.data.metrics.dto.StoreProfilingDto;
import com.mercaso.data.metrics.dto.StoreProfilingStoreMetricsDto;
import com.mercaso.data.metrics.entity.StoreProfilingStoreInfoEntity;
import com.mercaso.data.metrics.entity.StoreProfilingStoreMetricsEntity;
import com.mercaso.data.metrics.entity.StoreProfilingTagEntity;
import com.mercaso.data.metrics.mapper.StoreProfilingStoreInfoDtoMapper;
import com.mercaso.data.metrics.mapper.StoreProfilingStoreMetricsDtoMapper;
import com.mercaso.data.metrics.mock.MetricsStoreProfilingStoreInfoEntityMock;
import com.mercaso.data.metrics.repository.StoreProfilingStoreInfoRepository;
import com.mercaso.data.metrics.repository.StoreProfilingStoreMetricsRepository;
import com.mercaso.data.metrics.repository.StoreProfilingTagRepository;
import com.mercaso.data.metrics.service.impl.StoreProfilingServiceImpl;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import com.mercaso.data.metrics.dto.StoreProfilingStoreInfoDto;
import org.springframework.data.jpa.domain.Specification;
import java.util.Arrays;
import static org.junit.jupiter.api.Assertions.assertThrows;

@ExtendWith(MockitoExtension.class)
class StoreProfilingServiceTest {

    private final StoreProfilingStoreMetricsRepository storeProfilingStoreMetricsRepository = Mockito.mock(
        StoreProfilingStoreMetricsRepository.class);

    private final StoreProfilingStoreInfoRepository storeProfilingStoreInfoRepository = Mockito.mock(
        StoreProfilingStoreInfoRepository.class);

    private final StoreProfilingTagRepository storeProfilingTagRepository = Mockito.mock(
        StoreProfilingTagRepository.class
    );

    private final StoreProfilingStoreMetricsDtoMapper storeProfilingStoreMetricsDtoMapper = Mockito.mock(
        StoreProfilingStoreMetricsDtoMapper.class);

    private final StoreProfilingStoreInfoDtoMapper storeProfilingStoreInfoDtoMapper = Mockito.mock(
        StoreProfilingStoreInfoDtoMapper.class);

    private final StoreProfilingService storeProfilingService = new StoreProfilingServiceImpl(storeProfilingStoreMetricsRepository,
        storeProfilingStoreInfoRepository, storeProfilingTagRepository, storeProfilingStoreMetricsDtoMapper, storeProfilingStoreInfoDtoMapper);


    @Test
    void searchStoreAddressesV2_EmptyKeyword_ReturnsAllAddressesForStoreProfiling() {

        StoreProfilingStoreInfoEntity entity = MetricsStoreProfilingStoreInfoEntityMock.metricsStoreProfilingStoreInfoEntityMock();
        // Given
        StoreAddressFilter filter = new StoreAddressFilter();
        filter.setKeyword("");
        CustomPageable customPageable = new CustomPageable();
        customPageable.setPageNumber(0);
        customPageable.setPageSize(10);
        filter.setPageable(customPageable);
        Page<StoreProfilingStoreInfoEntity> mockPage = new PageImpl<>(List.of(entity));
        when(storeProfilingStoreInfoRepository.queryAll(any(Pageable.class))).thenReturn(mockPage);

        // When
        Page<SearchStoreAddressDto> result = storeProfilingService.searchStoreAddressesForStoreProfiling(filter);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getTotalElements()).isEqualTo(1);
    }

    @Test
    void searchStoreAddressesV2_WithKeyword_ReturnsMatchingAddressesForStoreProfiling() {
        StoreProfilingStoreInfoEntity entity = MetricsStoreProfilingStoreInfoEntityMock.metricsStoreProfilingStoreInfoEntityMock();
        // Given
        StoreAddressFilter filter = new StoreAddressFilter();
        filter.setKeyword("test");
        CustomPageable customPageable = new CustomPageable();
        customPageable.setPageNumber(0);
        customPageable.setPageSize(10);
        filter.setPageable(customPageable);
        Page<StoreProfilingStoreInfoEntity> mockPage = new PageImpl<>(List.of(entity));
        when(storeProfilingStoreInfoRepository.findBySearchQuery(anyString(), any(Pageable.class))).thenReturn(mockPage);

        // When
        Page<SearchStoreAddressDto> result = storeProfilingService.searchStoreAddressesForStoreProfiling(filter);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getTotalElements()).isEqualTo(1);
    }

    @Test
    void findMetricsByStoreId_ReturnsListOfStoreProfilingStoreMetricsDtoForStoreProfiling() {
        StoreProfilingStoreMetricsEntity entity = new StoreProfilingStoreMetricsEntity();
        entity.setStoreId("123");
        when(storeProfilingStoreMetricsRepository.findByStoreId(anyString())).thenReturn(List.of(entity));

        List<StoreProfilingStoreMetricsDto> result = storeProfilingService.findMetricsByStoreId("123");
        assertThat(result).isNotNull();
    }


    @Test
    void getStoreProfilingByStoreId_ReturnsStoreProfilingDtoForStoreProfiling() {
        StoreProfilingStoreInfoEntity entity = MetricsStoreProfilingStoreInfoEntityMock.metricsStoreProfilingStoreInfoEntityMock();
        when(storeProfilingStoreInfoRepository.findById(anyString())).thenReturn(Optional.of(entity));

        List<StoreProfilingStoreMetricsEntity> metrics = new ArrayList<>();
        StoreProfilingStoreMetricsEntity metric = new StoreProfilingStoreMetricsEntity();
        metric.setStoreId("123");
        metrics.add(metric);
        when(storeProfilingStoreMetricsRepository.findByStoreId(anyString())).thenReturn(metrics);

        StoreProfilingDto result = storeProfilingService.getStoreProfilingByStoreId("123");
        assertThat(result).isNotNull();
    }

    @Test
    void searchStoreByTags_whenMatchAny_returnsFilteredStores() {
        // Given
        StoreAddressFilter filter = new StoreAddressFilter();
        filter.setTags("category-electronics,brand-samsung");
        filter.setMatch("any");
        CustomPageable customPageable = new CustomPageable(0, 10);
        filter.setPageable(customPageable);

        List<String> storeIdsFound = Arrays.asList("store1", "store2");
        Page<String> mockedStoreIdsPage = new PageImpl<>(storeIdsFound, PageRequest.of(0, 10), storeIdsFound.size());

        when(storeProfilingStoreMetricsRepository.findDistinctStoreIds(
            any(Specification.class),
            any(Pageable.class)))
            .thenReturn(mockedStoreIdsPage);

        StoreProfilingStoreInfoEntity storeInfo1 = new StoreProfilingStoreInfoEntity();
        storeInfo1.setStoreId("store1");
        StoreProfilingStoreInfoEntity storeInfo2 = new StoreProfilingStoreInfoEntity();
        storeInfo2.setStoreId("store2");
        List<StoreProfilingStoreInfoEntity> storeInfoEntities = Arrays.asList(storeInfo1, storeInfo2);

        when(storeProfilingStoreInfoRepository.findAllById(storeIdsFound)).thenReturn(storeInfoEntities);

        StoreProfilingStoreInfoDto dto1 = new StoreProfilingStoreInfoDto();
        dto1.setStoreId("store1");
        StoreProfilingStoreInfoDto dto2 = new StoreProfilingStoreInfoDto();
        dto2.setStoreId("store2");
        when(storeProfilingStoreInfoDtoMapper.toDto(storeInfo1)).thenReturn(dto1);
        when(storeProfilingStoreInfoDtoMapper.toDto(storeInfo2)).thenReturn(dto2);

        // When
        Page<StoreProfilingStoreInfoDto> resultPage = storeProfilingService.searchStoreByTags(filter);

        // Then
        assertThat(resultPage).isNotNull();
        assertThat(resultPage.getTotalElements()).isEqualTo(storeIdsFound.size());
        assertThat(resultPage.getContent()).hasSize(storeIdsFound.size());
        assertThat(resultPage.getContent().get(0).getStoreId()).isEqualTo("store1");
        assertThat(resultPage.getContent().get(1).getStoreId()).isEqualTo("store2");
    }

    @Test
    void searchStoreByTags_whenMatchAll_returnsFilteredStores() {
        // Given
        StoreAddressFilter filter = new StoreAddressFilter();
        filter.setTags("feature-waterproof,color-black");
        filter.setMatch("all");
        CustomPageable customPageable = new CustomPageable(0, 5);
        filter.setPageable(customPageable);

        List<String> storeIdsFound = Arrays.asList("store3");
        Page<String> mockedStoreIdsPage = new PageImpl<>(storeIdsFound, PageRequest.of(0, 5), storeIdsFound.size());

        when(storeProfilingStoreMetricsRepository.findDistinctStoreIds(
            any(Specification.class),
            any(Pageable.class)))
            .thenReturn(mockedStoreIdsPage);

        StoreProfilingStoreInfoEntity storeInfo3 = new StoreProfilingStoreInfoEntity();
        storeInfo3.setStoreId("store3");
        List<StoreProfilingStoreInfoEntity> storeInfoEntities = Arrays.asList(storeInfo3);

        when(storeProfilingStoreInfoRepository.findAllById(storeIdsFound)).thenReturn(storeInfoEntities);

        StoreProfilingStoreInfoDto dto3 = new StoreProfilingStoreInfoDto();
        dto3.setStoreId("store3");
        when(storeProfilingStoreInfoDtoMapper.toDto(storeInfo3)).thenReturn(dto3);

        // When
        Page<StoreProfilingStoreInfoDto> resultPage = storeProfilingService.searchStoreByTags(filter);

        // Then
        assertThat(resultPage).isNotNull();
        assertThat(resultPage.getTotalElements()).isEqualTo(1);
        assertThat(resultPage.getContent()).hasSize(1);
        assertThat(resultPage.getContent().get(0).getStoreId()).isEqualTo("store3");
    }

    @Test
    void searchStoreByTags_whenMatchTypeNull_returnsAllStores() {
        // Given
        StoreAddressFilter filter = new StoreAddressFilter();
        filter.setMatch(null);
        CustomPageable customPageable = new CustomPageable(0, 10);
        filter.setPageable(customPageable);

        List<String> allStoreIds = Arrays.asList("storeA", "storeB", "storeC");
        Page<String> mockedStoreIdsPage = new PageImpl<>(allStoreIds, PageRequest.of(0, 10), allStoreIds.size());

        when(storeProfilingStoreMetricsRepository.findDistinctStoreIds(
            any(Specification.class), // Specification will be cb.conjunction()
            any(Pageable.class)))
            .thenReturn(mockedStoreIdsPage);

        StoreProfilingStoreInfoEntity storeInfoA = new StoreProfilingStoreInfoEntity(); storeInfoA.setStoreId("storeA");
        StoreProfilingStoreInfoEntity storeInfoB = new StoreProfilingStoreInfoEntity(); storeInfoB.setStoreId("storeB");
        StoreProfilingStoreInfoEntity storeInfoC = new StoreProfilingStoreInfoEntity(); storeInfoC.setStoreId("storeC");
        List<StoreProfilingStoreInfoEntity> storeInfoEntities = Arrays.asList(storeInfoA, storeInfoB, storeInfoC);

        when(storeProfilingStoreInfoRepository.findAllById(allStoreIds)).thenReturn(storeInfoEntities);

        // Mock mappers
        StoreProfilingStoreInfoDto dtoA = new StoreProfilingStoreInfoDto(); dtoA.setStoreId("storeA");
        StoreProfilingStoreInfoDto dtoB = new StoreProfilingStoreInfoDto(); dtoB.setStoreId("storeB");
        StoreProfilingStoreInfoDto dtoC = new StoreProfilingStoreInfoDto(); dtoC.setStoreId("storeC");
        when(storeProfilingStoreInfoDtoMapper.toDto(storeInfoA)).thenReturn(dtoA);
        when(storeProfilingStoreInfoDtoMapper.toDto(storeInfoB)).thenReturn(dtoB);
        when(storeProfilingStoreInfoDtoMapper.toDto(storeInfoC)).thenReturn(dtoC);


        // When
        Page<StoreProfilingStoreInfoDto> resultPage = storeProfilingService.searchStoreByTags(filter);

        // Then
        assertThat(resultPage).isNotNull();
        assertThat(resultPage.getTotalElements()).isEqualTo(allStoreIds.size());
        assertThat(resultPage.getContent().stream().map(StoreProfilingStoreInfoDto::getStoreId))
            .containsExactlyInAnyOrder("storeA", "storeB", "storeC");
    }

    @Test
    void searchStoreByTags_whenTagsAreNullOrEmpty_returnsAllStores() {
        // Given
        StoreAddressFilter filter = new StoreAddressFilter();
        filter.setTags(null);
        filter.setMatch("any");
        CustomPageable customPageable = new CustomPageable(0, 10);
        filter.setPageable(customPageable);

        List<String> allStoreIds = Arrays.asList("storeX", "storeY");
        Page<String> mockedStoreIdsPage = new PageImpl<>(allStoreIds, PageRequest.of(0, 10), allStoreIds.size());

        when(storeProfilingStoreMetricsRepository.findDistinctStoreIds(
            any(Specification.class),
            any(Pageable.class)))
            .thenReturn(mockedStoreIdsPage);
        
        StoreProfilingStoreInfoEntity storeInfoX = new StoreProfilingStoreInfoEntity(); storeInfoX.setStoreId("storeX");
        StoreProfilingStoreInfoEntity storeInfoY = new StoreProfilingStoreInfoEntity(); storeInfoY.setStoreId("storeY");
        List<StoreProfilingStoreInfoEntity> storeInfoEntities = Arrays.asList(storeInfoX, storeInfoY);

        when(storeProfilingStoreInfoRepository.findAllById(allStoreIds)).thenReturn(storeInfoEntities);
        
        // Mock mappers
        StoreProfilingStoreInfoDto dtoX = new StoreProfilingStoreInfoDto(); dtoX.setStoreId("storeX");
        StoreProfilingStoreInfoDto dtoY = new StoreProfilingStoreInfoDto(); dtoY.setStoreId("storeY");
        when(storeProfilingStoreInfoDtoMapper.toDto(storeInfoX)).thenReturn(dtoX);
        when(storeProfilingStoreInfoDtoMapper.toDto(storeInfoY)).thenReturn(dtoY);

        // When
        Page<StoreProfilingStoreInfoDto> resultPage = storeProfilingService.searchStoreByTags(filter);

        // Then
        assertThat(resultPage).isNotNull();
        assertThat(resultPage.getTotalElements()).isEqualTo(allStoreIds.size());

        // Test with empty tags as well
        filter.setTags("");
        resultPage = storeProfilingService.searchStoreByTags(filter);
        assertThat(resultPage).isNotNull();
        assertThat(resultPage.getTotalElements()).isEqualTo(allStoreIds.size());
    }


    @Test
    void searchStoreByTags_whenTagFormatIsInvalid_throwsIllegalArgumentException() {
        // Given
        StoreAddressFilter filter = new StoreAddressFilter();
        filter.setTags("categoryelectronics");
        filter.setMatch("any");
        CustomPageable customPageable = new CustomPageable(0, 10);
        filter.setPageable(customPageable);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            storeProfilingService.searchStoreByTags(filter);
        });
        assertThat(exception.getMessage()).contains("Tag format error");
    }

    @Test
    void searchStoreByTags_whenMatchTypeIsInvalid_throwsIllegalArgumentException() {
        // Given
        StoreAddressFilter filter = new StoreAddressFilter();
        filter.setTags("brand-apple");
        filter.setMatch("sometimes"); // Invalid match type
        CustomPageable customPageable = new CustomPageable(0, 10);
        filter.setPageable(customPageable);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            storeProfilingService.searchStoreByTags(filter);
        });
        assertThat(exception.getMessage()).contains("Match value must be 'all' or 'any'");
    }

    @Test
    void getStoreTags_whenNoTags_returnsEmptyList() {
        when(storeProfilingTagRepository.findAll()).thenReturn(List.of());
        List<String> tags = storeProfilingService.getStoreTags();
        assertThat(tags).isEmpty();
    }

    @Test
    void getStoreTags_whenTags_returnsListOfTags() {
        when(storeProfilingTagRepository.findAll()).thenReturn(List.of(new StoreProfilingTagEntity(1L,"tag1"), new StoreProfilingTagEntity(2L,"tag2")));
        List<String> tags = storeProfilingService.getStoreTags();
        assertThat(tags).isNotEmpty();
        assertThat(tags).containsExactlyInAnyOrder("tag1", "tag2"); 
    }
}
