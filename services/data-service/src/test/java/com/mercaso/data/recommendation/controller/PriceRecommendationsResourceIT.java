package com.mercaso.data.recommendation.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.recommendation.dto.DepartmentDto;
import com.mercaso.data.recommendation.dto.PageableResponse;
import com.mercaso.data.recommendation.dto.PriceRecommendationDto;
import com.mercaso.data.recommendation.entity.PriceRecommendation;
import com.mercaso.data.recommendation.repository.PriceRecommendationRepository;
import com.mercaso.data.recommendation.utils.resource_utils.PriceRecommendationResourceApiUtils;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class PriceRecommendationsResourceIT extends AbstractIT {

  @Autowired
  private PriceRecommendationRepository priceRecommendationRepository;

  @Autowired
  private PriceRecommendationResourceApiUtils priceRecommendationResourceApiUtils;

  @Test
  void getDepartments() throws Exception {
    String storeId = UUID.randomUUID().toString();
    String departmentId1 = UUID.randomUUID().toString();
    String departmentId2 = UUID.randomUUID().toString();
    PriceRecommendation priceRecommendation1 = mockData(storeId, departmentId1);
    PriceRecommendation priceRecommendation2 = mockData(storeId, departmentId2);
    PriceRecommendation priceRecommendation3 = mockData(storeId, departmentId2);

    priceRecommendationRepository.saveAll(List.of(priceRecommendation1, priceRecommendation2,
        priceRecommendation3));

    List<DepartmentDto> departmentDtos = priceRecommendationResourceApiUtils.searchDepartments(
        storeId);

    assertNotNull(departmentDtos);
    assertEquals(2, departmentDtos.size());
  }

  @Test
  void searchPriceRecommendations() throws Exception {
    String storeId = UUID.randomUUID().toString();
    String departmentId = UUID.randomUUID().toString();
    PriceRecommendation priceRecommendation1 = mockData(storeId, departmentId);
    PriceRecommendation priceRecommendation2 = mockData(storeId, departmentId);
    PriceRecommendation priceRecommendation3 = mockData(storeId, departmentId);

    priceRecommendationRepository.saveAll(List.of(priceRecommendation1, priceRecommendation2,
        priceRecommendation3));

    PageableResponse<PriceRecommendationDto> priceRecommendationDtoPageableResponse = priceRecommendationResourceApiUtils
        .searchPriceRecommendations(storeId, departmentId, null, false, 0, 10);

    assertNotNull(priceRecommendationDtoPageableResponse);
    List<PriceRecommendationDto> data = priceRecommendationDtoPageableResponse.getData();
    assertNotNull(data);
    assertEquals(3, data.size());

  }

  @Test
  void searchWithNotExistName() throws Exception {
    String storeId = UUID.randomUUID().toString();
    String departmentId = UUID.randomUUID().toString();
    PriceRecommendation priceRecommendation = mockData(storeId, departmentId);

    priceRecommendationRepository.save(priceRecommendation);

    PageableResponse<PriceRecommendationDto> priceRecommendationDtoPageableResponse = priceRecommendationResourceApiUtils
        .searchPriceRecommendations(storeId, departmentId, "notExistName", false, 0, 10);

    assertNotNull(priceRecommendationDtoPageableResponse);
    List<PriceRecommendationDto> data = priceRecommendationDtoPageableResponse.getData();
    assertNotNull(data);
    assertEquals(0, data.size());

  }

  @Test
  void searchLatestOrderPriceRecommendation() throws Exception {
    String storeId = UUID.randomUUID().toString();
    String departmentId = UUID.randomUUID().toString();
    PriceRecommendation priceRecommendation1 = mockData(storeId, departmentId);
    PriceRecommendation priceRecommendation2 = mockData(storeId, departmentId);
    PriceRecommendation priceRecommendation3 = mockData(storeId, departmentId);

    priceRecommendation1.setLatestOrder(true);
    priceRecommendation2.setLatestOrder(false);
    priceRecommendation3.setLatestOrder(true);

    priceRecommendationRepository.saveAll(List.of(priceRecommendation1, priceRecommendation2,
        priceRecommendation3));

    PageableResponse<PriceRecommendationDto> priceRecommendationDtoPageableResponse = priceRecommendationResourceApiUtils
        .searchPriceRecommendations(storeId, departmentId, null, true, 0, 10);

    assertNotNull(priceRecommendationDtoPageableResponse);
    List<PriceRecommendationDto> data = priceRecommendationDtoPageableResponse.getData();
    assertNotNull(data);
    assertEquals(2, data.size());
  }

  private PriceRecommendation mockData(String storeId, String departmentId) {
    return PriceRecommendation.builder()
        .storeId(storeId)
        .productId(RandomStringUtils.randomNumeric(13))
        .sku("DW" + RandomStringUtils.randomNumeric(6))
        .name(RandomStringUtils.randomAlphabetic(10))
        .highPrice("44.88")
        .lowPrice("40.99")
        .avgPrice("42.99")
        .lastPurchasePrice("38.99")
        .departmentId(departmentId)
        .department(departmentId)
        .upc(RandomStringUtils.randomNumeric(14))
        .build();
  }

}