server:
  port: 8080
  forward-headers-strategy: framework
spring:
  profiles:
    active: local
  application:
    name: data
  datasource:
    url: jdbc:postgresql://${host.db_server}/data_service
    driver-class-name: org.postgresql.Driver
    hikari:
      connection-init-sql: "set role data_user"
      connection-timeout: 15000
      minimum-idle: 1
      maximum-pool-size: 20
      allow-pool-suspension: true
  cloud:
    vault:
      enabled: true
      scheme: http
      port: 8200
      host: vault.vault.svc.cluster.local
      authentication: KUBERNETES
      kubernetes:
        role: data-service
        kubernetes-path: kubernetes
        service-account-token-file: /var/run/secrets/kubernetes.io/serviceaccount/token
      # Application will fail if it cannot connect to vault, remember to disable vault for envs that don't need it
      fail-fast: true

      # Need to disable generic engine so that spring cloud knows to only pull secrets from KV engine
      generic:
        enabled: false
      kv:
        enabled: true
        backend: secret
        profile-separator: '/'
        application-name: data-service
      database:
        enabled: true
        role: data-service
        backend: database
        username-property: spring.datasource.username
        password-property: spring.datasource.password
  jpa:
    show-sql: false
    hibernate:
      ddl-auto: none
    properties:
      hibernate.dialect: org.hibernate.dialect.PostgreSQLDialect
  flyway:
    enabled: true
    locations: classpath:/db/migration
    baseline-on-migrate: true
    init-sqls: SET ROLE data_user
  servlet:
    multipart:
      max-file-size: 70MB
      max-request-size: 70MB
otel:
  traces:
    exporter: otlp
    sampler: always_on
  metrics:
    exporter: otlp
  exporter:
    otlp:
      endpoint: ${OTEL_EXPORTER_OTLP_ENDPOINT}
management:
  endpoint:
    health:
      show-details: always
      probes:
        enabled: true
      group:
        readiness:
          include: readinessState
    shutdown:
      enabled: true
  endpoints:
    enabled-by-default: true
    web:
      exposure:
        include: '*'
  health:
    db:
      enabled: true
    vault:
      enabled: false
    livenessState:
      enabled: true
    readinessState:
      enabled: true
  server:
    port: 8081
springdoc:
  swagger-ui:
    enabled: false
third-party:
  finale:
    graphql-api-url: https://app.finaleinventory.com/mercaso/api/graphql
    facility-api-url: https://app.finaleinventory.com/mercaso/api/facility
    finale-primary-api-url: https://app.finaleinventory.com/mercaso/api/customization/PRIMARY
    token: ${third-party.finale.token}

security:
  public-paths:
  - /v1/oauth/callback/square

logging:
  level:
    org.springframework.web.filter.CommonsRequestLoggingFilter: DEBUG

square:
  cipher:
    key: ${square.encryption.key}

mercaso:
  wms-url: http://warehouse-management-service.default.svc.cluster.local
  ims-url: http://item-management-service.default.svc.cluster.local
  document:
    operations:
      enabled: true
      storage:
        bucket-name: ${aws-bucket-name}
        master-catalog:
          root-folder: master-catalog/
        metrics:
          root-folder: metrics/
        image-management:
          root-folder: image-management/