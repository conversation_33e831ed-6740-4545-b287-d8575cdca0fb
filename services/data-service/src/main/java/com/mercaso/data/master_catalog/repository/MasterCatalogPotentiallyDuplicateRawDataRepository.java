package com.mercaso.data.master_catalog.repository;

import com.mercaso.data.master_catalog.entity.MasterCatalogPotentiallyDuplicateRawData;
import com.mercaso.data.master_catalog.enums.PotentiallyDuplicateRawDataStatus;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface MasterCatalogPotentiallyDuplicateRawDataRepository extends
    JpaRepository<MasterCatalogPotentiallyDuplicateRawData, UUID> {

    Page<MasterCatalogPotentiallyDuplicateRawData> findByStatusIsIn(List<PotentiallyDuplicateRawDataStatus> status,
        Pageable pageable);

    List<MasterCatalogPotentiallyDuplicateRawData> findAllByTaskId(@NotNull UUID taskId);

    List<MasterCatalogPotentiallyDuplicateRawData> findAllByTaskIdAndStatus(UUID taskId, PotentiallyDuplicateRawDataStatus status);
}