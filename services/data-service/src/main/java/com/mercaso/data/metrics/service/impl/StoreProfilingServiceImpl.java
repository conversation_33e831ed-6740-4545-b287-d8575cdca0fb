package com.mercaso.data.metrics.service.impl;

import com.mercaso.data.metrics.dto.SearchStoreAddressDto;
import com.mercaso.data.metrics.dto.StoreAddressFilter;
import com.mercaso.data.metrics.dto.StoreProfilingDto;
import com.mercaso.data.metrics.dto.StoreProfilingStoreInfoDto;
import com.mercaso.data.metrics.dto.StoreProfilingStoreMetricsDto;
import com.mercaso.data.metrics.entity.StoreProfilingStoreInfoEntity;
import com.mercaso.data.metrics.entity.StoreProfilingStoreMetricsEntity;
import com.mercaso.data.metrics.entity.StoreProfilingTagEntity;
import com.mercaso.data.metrics.mapper.StoreProfilingStoreInfoDtoMapper;
import com.mercaso.data.metrics.mapper.StoreProfilingStoreMetricsDtoMapper;
import com.mercaso.data.metrics.repository.StoreProfilingStoreInfoRepository;
import com.mercaso.data.metrics.repository.StoreProfilingStoreMetricsRepository;
import com.mercaso.data.metrics.repository.StoreProfilingTagRepository;
import com.mercaso.data.metrics.repository.specification.StoreProfilingStoreInfoSpecifications;
import com.mercaso.data.metrics.repository.specification.StoreProfilingStoreMetricsSpecification;
import com.mercaso.data.metrics.service.StoreProfilingService;
import com.mercaso.data.metrics.utils.LuceneTokenizer;
import com.mercaso.data.metrics.utils.StoreAddressDtoHelper;
import java.util.List;
import java.util.Optional;
import java.util.Comparator;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import java.util.ArrayList;
import org.apache.commons.lang3.tuple.Pair;

@Service
@RequiredArgsConstructor
@Slf4j
public class StoreProfilingServiceImpl implements StoreProfilingService {

    private final StoreProfilingStoreMetricsRepository storeProfilingStoreMetricsRepository;

    private final StoreProfilingStoreInfoRepository storeProfilingStoreInfoRepository;

    private final StoreProfilingTagRepository storeProfilingTagRepository;

    private final StoreProfilingStoreMetricsDtoMapper storeProfilingStoreMetricsDtoMapper;

    private final StoreProfilingStoreInfoDtoMapper storeProfilingStoreInfoDtoMapper;

    @Override
    public List<StoreProfilingStoreMetricsDto> findMetricsByStoreId(String storeId) {

        if (!StringUtils.hasText(storeId)) {
            return List.of();
        }

        List<StoreProfilingStoreMetricsEntity> storeProfilingStoreMetricsEntities = storeProfilingStoreMetricsRepository.findByStoreId(
            storeId);

        return storeProfilingStoreMetricsEntities.stream().map(storeProfilingStoreMetricsDtoMapper::toDto).toList();
    }

    @Override
    public StoreProfilingStoreInfoDto getStoreProfilingStoreInfoByStoreId(String storeId) {

        Optional<StoreProfilingStoreInfoEntity> storeInfoEntityOptional = storeProfilingStoreInfoRepository.findById(storeId);

        if (storeInfoEntityOptional.isEmpty()) {
            return null;
        }

        StoreProfilingStoreInfoEntity storeProfilingStoreInfoEntity = storeInfoEntityOptional.get();

        return storeProfilingStoreInfoDtoMapper.toDto(storeProfilingStoreInfoEntity);
    }


    @Override
    public StoreProfilingDto getStoreProfilingByStoreId(String storeId) {

        StoreProfilingStoreInfoDto storeInfoDto = getStoreProfilingStoreInfoByStoreId(storeId);
        List<StoreProfilingStoreMetricsDto> storeProfilingList = findMetricsByStoreId(storeId);

        // Sort by metricCategory and metricName in ascending order
        List<StoreProfilingStoreMetricsDto> sortedStoreProfilingList = storeProfilingList.stream()
            .sorted(Comparator.comparing(StoreProfilingStoreMetricsDto::getMetricCategory)
                .thenComparing(StoreProfilingStoreMetricsDto::getMetricName))
            .collect(Collectors.toList());

        return StoreProfilingDto.builder()
            .storeProfilingList(sortedStoreProfilingList)
            .storeInfo(storeInfoDto)
            .build();
    }

    @Override
    public Page<SearchStoreAddressDto> searchStoreAddressesForStoreProfiling(StoreAddressFilter filter) {

        Pageable pageable = PageRequest.of(filter.getPageable().getPageNumber(), filter.getPageable().getPageSize());

//         If no keyword is provided, return all addresses for the store
        if (org.apache.commons.lang3.StringUtils.isEmpty(filter.getKeyword())) {
            log.info("[searchStoreAddressesForStoreProfiling] No keyword provided, returning all addresses");
            return storeProfilingStoreInfoRepository.queryAll(pageable)
                .map(StoreAddressDtoHelper::buildStoreAddressDto);
        }

        Page<StoreProfilingStoreInfoEntity> addressInfoEntitiesPage = Page.empty();

        try {
            // Convert the keyword to a ts_query
            String formattedQuery = LuceneTokenizer.convertKeywordToTsQuery(filter.getKeyword());
            // Search for the keyword in the address_info table
            addressInfoEntitiesPage = storeProfilingStoreInfoRepository.findBySearchQuery(formattedQuery, pageable);
        } catch (Exception ex) {
            log.warn("[searchStoreAddressesForStoreProfiling] Failed to search for addresses by keyword: {}", ex.getMessage());
        }

        // If no results are found, use %like% search
        if (addressInfoEntitiesPage.isEmpty()) {
            log.info("[searchStoreAddressesForStoreProfiling] No results found, using %like% search");
            addressInfoEntitiesPage = searchByNamePartsForStoreProfiling(LuceneTokenizer.tokenize(filter.getKeyword()), pageable);
        }

        Page<SearchStoreAddressDto> storeAddressDtosPage = addressInfoEntitiesPage.map(StoreAddressDtoHelper::buildStoreAddressDto);
        log.info("[searchStoreAddressesForStoreProfiling] Found {} addresses", storeAddressDtosPage.getTotalElements());
        return storeAddressDtosPage;
    }


    public Page<StoreProfilingStoreInfoEntity> searchByNamePartsForStoreProfiling(List<String> parts, Pageable pageable) {
        Specification<StoreProfilingStoreInfoEntity> spec = Specification
            .where(StoreProfilingStoreInfoSpecifications.addressNameContainsParts(parts));
        return storeProfilingStoreInfoRepository.findAll(spec, pageable);
    }


    @Override
    public Page<StoreProfilingStoreInfoDto> searchStoreByTags(StoreAddressFilter filter) {

        // process the tags
        List<Pair<String, String>> tagsList = new ArrayList<>();
        if (StringUtils.hasText(filter.getTags())) {
            String[] tags = filter.getTags().split(",");
            for (String tag : tags) {
                if (!tag.contains("-")) {
                    throw new IllegalArgumentException("Tag format error, must contain '-' separator: " + tag);
                }
                String[] parts = tag.split("-", 2);
                tagsList.add(Pair.of(parts[0], parts[1]));
            }
        }

        // process the match
        String match = filter.getMatch();
        if (StringUtils.hasText(match) && !match.equals("all") && !match.equals("any")) {
                throw new IllegalArgumentException("Match value must be 'all' or 'any', current value: " + match);
            }

        // Get pageable from filter
        Pageable pageable = PageRequest.of(filter.getPageable().getPageNumber(), filter.getPageable().getPageSize());

        Page<String> entities = storeProfilingStoreMetricsRepository.findDistinctStoreIds(
                StoreProfilingStoreMetricsSpecification.matchTags(tagsList, match),
                pageable
        );

        // Get store info by store ids
        List<String> storeIds = entities.stream().toList();
        List<StoreProfilingStoreInfoEntity> storeInfoEntities = storeProfilingStoreInfoRepository.findAllById(storeIds);
        // Return the page of store info DTOs
        return new PageImpl<>(storeInfoEntities.stream().map(storeProfilingStoreInfoDtoMapper::toDto).toList(), pageable, entities.getTotalElements());
    }

    @Override
    public List<String> getStoreTags() {

        List<StoreProfilingTagEntity> storeProfilingTags =  storeProfilingTagRepository.findAll();
        
        log.info("[getStoreTags] Found {} tags", storeProfilingTags.size());

        if (storeProfilingTags.isEmpty()) {
            return List.of();
        }


        return storeProfilingTags.stream().map(StoreProfilingTagEntity::getTag).distinct().toList();

    }

}
