package com.mercaso.data.recommendation.service;

import com.mercaso.data.recommendation.dto.DepartmentDto;
import com.mercaso.data.recommendation.dto.PageableResponse;
import com.mercaso.data.recommendation.dto.PriceRecommendationDto;
import com.mercaso.data.recommendation.dto.SearchPriceDto;
import java.util.List;

public interface PriceRecommendationService {

  PageableResponse<PriceRecommendationDto> search(SearchPriceDto search, Integer pageNumber,
      Integer pageSize);

  List<DepartmentDto> findDepartmentsByStoreId(String storeId);
}
