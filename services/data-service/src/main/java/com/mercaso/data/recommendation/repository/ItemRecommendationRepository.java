package com.mercaso.data.recommendation.repository;

import com.mercaso.data.recommendation.entity.ItemRecommendation;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

public interface ItemRecommendationRepository extends JpaRepository<ItemRecommendation, UUID> {

  Page<ItemRecommendation> findByStoreIdAndVersion(String storeId, String version, Pageable pageable);

}
