package com.mercaso.data.recommendation.dto;

import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PriceRecommendationDto {

  private String productId;
  private String sku;
  private String name;
  private BigDecimal highPrice;
  private BigDecimal lowPrice;
  private BigDecimal avgPrice;
  private BigDecimal lastPurchasePrice;
  private String departmentId;
  private String department;
  private String upc;
}
