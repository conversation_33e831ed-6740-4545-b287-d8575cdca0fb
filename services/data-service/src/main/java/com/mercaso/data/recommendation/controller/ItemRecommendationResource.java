package com.mercaso.data.recommendation.controller;

import com.mercaso.data.recommendation.ItemRecommendationService;
import com.mercaso.data.recommendation.dto.ItemRecommendationRecordDto;
import com.mercaso.data.recommendation.dto.PageableResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/recommendation/v1/item-recommendations")
@RequiredArgsConstructor
public class ItemRecommendationResource {

  private final ItemRecommendationService itemRecommendationService;

  @GetMapping("/search")
  @PreAuthorize("hasAnyAuthority('recommendation:read:recommendations')")
  public PageableResponse<ItemRecommendationRecordDto> search(
      @RequestParam(defaultValue = "0") Integer pageNumber,
      @RequestParam(defaultValue = "20") Integer pageSize,
      @RequestParam String storeId,
      @RequestParam(required = false, defaultValue = "V1") String version) {

    log.info("Search item recommendations for store: {}, version: {}, page: {}, size: {}", 
        storeId, version, pageNumber, pageSize);

    return itemRecommendationService.search(storeId, version, pageNumber, pageSize);
  }
}
