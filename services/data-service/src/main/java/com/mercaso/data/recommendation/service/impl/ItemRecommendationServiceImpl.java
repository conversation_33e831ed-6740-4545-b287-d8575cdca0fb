package com.mercaso.data.recommendation.service.impl;

import com.mercaso.data.recommendation.ItemRecommendationService;
import com.mercaso.data.recommendation.dto.ItemRecommendationRecordDto;
import com.mercaso.data.recommendation.dto.PageableResponse;
import com.mercaso.data.recommendation.entity.ItemRecommendation;
import com.mercaso.data.recommendation.mapper.ItemRecommendationMapper;
import com.mercaso.data.recommendation.repository.ItemRecommendationRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class ItemRecommendationServiceImpl implements ItemRecommendationService {

  private final ItemRecommendationRepository itemRecommendationRepository;
  private final ItemRecommendationMapper itemRecommendationMapper;

  @Override
  public PageableResponse<ItemRecommendationRecordDto> search(String storeId, String version, Integer pageNumber,
      Integer pageSize) {
    Pageable pageable = PageRequest.of(pageNumber, pageSize, Sort.by(Sort.Direction.DESC,
        "reasonValue"));

    Page<ItemRecommendation> recommendations = itemRecommendationRepository.findByStoreIdAndVersion(
        storeId, version, pageable);

    List<ItemRecommendationRecordDto> dtos = recommendations.getContent().stream()
        .map(itemRecommendationMapper::toDto)
        .toList();

    return PageableResponse.<ItemRecommendationRecordDto>builder()
        .data(dtos)
        .pageNumber(recommendations.getNumber())
        .pageSize(recommendations.getSize())
        .totalPages(recommendations.getTotalPages())
        .totalElements((int) recommendations.getTotalElements())
        .build();
  }
}