package com.mercaso.data.recommendation.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import com.mercaso.data.master_catalog.entity.BaseEntity;

@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
@Entity
@Table(name = "item_recommendation")
public class ItemRecommendation extends BaseEntity {

    @Size(max = 255)
    @Column(name = "store_id")
    private String storeId;

    @Size(max = 255)
    @Column(name = "sku_number")
    private String skuNumber;

    @Size(max = 255)
    @Column(name = "sku_name")
    private String skuName;

    @Size(max = 255)
    @Column(name = "product_id")
    private String productId;

    @Size(max = 255)
    @Column(name = "department")
    private String department;

    @Size(max = 255)
    @Column(name = "reason")
    private String reason;

    @Size(max = 255)
    @Column(name = "reason_value")
    private String reasonValue;

    @Size(max = 255)
    @Column(name = "version")
    private String version;

}