package com.mercaso.data.recommendation.mapper;

import com.mercaso.data.recommendation.dto.ItemRecommendationRecordDto;
import com.mercaso.data.recommendation.dto.RecommendationReasonDto;
import com.mercaso.data.recommendation.entity.ItemRecommendation;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface ItemRecommendationMapper {

  ItemRecommendationMapper INSTANCE = Mappers.getMapper(ItemRecommendationMapper.class);

  /**
   * Convert ItemRecommendation entity to ItemRecommendationRecordDto.
   *
   * @param itemRecommendation the entity to convert
   * @return the converted DTO
   */
  @Mapping(source = "skuNumber", target = "sku")
  @Mapping(source = "reason", target = "reason.type")
  @Mapping(source = "reasonValue", target = "reason.value", qualifiedByName = "stringToInteger")
  @Mapping(source = "version", target = "version")
  ItemRecommendationRecordDto toDto(ItemRecommendation itemRecommendation);

  /**
   * Converts a string reasonValue to Integer with rounding.
   * Uses mathematical rounding to get the nearest integer value.
   *
   * @param value the string value to convert
   * @return the converted Integer value rounded to the nearest integer, or null if conversion fails
   */
  @Named("stringToInteger")
  default Integer stringToInteger(String value) {
    if (value == null || value.isEmpty()) {
      return null;
    }
    try {
      double doubleValue = Double.parseDouble(value);
      return (int) Math.round(doubleValue);
    } catch (NumberFormatException e) {
      return null;
    }
  }
}