package com.mercaso.data.recommendation.entity;

import com.mercaso.data.master_catalog.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
@Entity
@Table(name = "price_recommendation")
public class PriceRecommendation extends BaseEntity {

  @Size(max = 255)
  @Column(name = "store_id")
  private String storeId;

  @Size(max = 255)
  @Column(name = "product_id")
  private String productId;

  @Size(max = 255)
  @Column(name = "sku")
  private String sku;

  @Size(max = 255)
  @Column(name = "name")
  private String name;

  @Size(max = 255)
  @Column(name = "upc")
  private String upc;

  @Size(max = 64)
  @Column(name = "department_id")
  private String departmentId;

  @Size(max = 255)
  @Column(name = "department")
  private String department;

  @Size(max = 32)
  @Column(name = "last_purchase_price")
  private String lastPurchasePrice;

  @Size(max = 32)
  @Column(name = "high_price")
  private String highPrice;

  @Size(max = 32)
  @Column(name = "low_price")
  private String lowPrice;

  @Size(max = 32)
  @Column(name = "avg_price")
  private String avgPrice;

  @Column(name = "latest_order")
  private Boolean latestOrder;

  @Column(name = "new_item_flag")
  private Integer newItemFlag;
}