package com.mercaso.data.metrics.service;

import com.mercaso.data.metrics.dto.SearchStoreAddressDto;
import com.mercaso.data.metrics.dto.StoreAddressFilter;
import com.mercaso.data.metrics.dto.StoreProfilingDto;
import com.mercaso.data.metrics.dto.StoreProfilingStoreInfoDto;
import com.mercaso.data.metrics.dto.StoreProfilingStoreMetricsDto;
import java.util.List;
import org.springframework.data.domain.Page;

public interface StoreProfilingService {

    List<StoreProfilingStoreMetricsDto> findMetricsByStoreId(String storeId);

    StoreProfilingStoreInfoDto getStoreProfilingStoreInfoByStoreId(String storeId);

    Page<SearchStoreAddressDto> searchStoreAddressesForStoreProfiling(StoreAddressFilter filter);

    StoreProfilingDto getStoreProfilingByStoreId(String storeId);

    Page<StoreProfilingStoreInfoDto> searchStoreByTags(StoreAddressFilter filter);

    List<String> getStoreTags();
}

