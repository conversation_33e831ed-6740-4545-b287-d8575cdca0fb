package com.mercaso.data.recommendation.service.impl;

import com.mercaso.data.recommendation.dto.DepartmentDto;
import com.mercaso.data.recommendation.dto.PageableResponse;
import com.mercaso.data.recommendation.dto.PriceRecommendationDto;
import com.mercaso.data.recommendation.dto.SearchPriceDto;
import com.mercaso.data.recommendation.mapper.PriceRecommendationMapper;
import com.mercaso.data.recommendation.repository.PriceRecommendationRepository;
import com.mercaso.data.recommendation.service.PriceRecommendationService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class PriceRecommendationServiceImpl implements PriceRecommendationService {

  private final PriceRecommendationRepository priceRecommendationRepository;
  private final PriceRecommendationMapper priceRecommendationMapper;

  @Override
  public PageableResponse<PriceRecommendationDto> search(SearchPriceDto search, Integer pageNumber,
      Integer pageSize) {

    Sort sort = Sort.by(Sort.Direction.DESC, "id");

    if(Boolean.TRUE.equals(search.getLatestOrder())) {
      sort = Sort.by(Sort.Direction.DESC, "newItemFlag", "id");
    }

    Pageable pageable = PageRequest.of(pageNumber, pageSize, sort);

    var recommendations = priceRecommendationRepository.search(search, pageable);

    return PageableResponse.<PriceRecommendationDto>builder()
        .data(recommendations.getContent().stream().map(priceRecommendationMapper::toDto).toList())
        .pageNumber(recommendations.getNumber())
        .pageSize(recommendations.getSize())
        .totalPages(recommendations.getTotalPages())
        .totalElements((int) recommendations.getTotalElements())
        .build();
  }

  @Override
  public List<DepartmentDto> findDepartmentsByStoreId(String storeId) {
    var recommendations = priceRecommendationRepository.findByStoreId(storeId);
    return recommendations.stream()
        .map(priceRecommendation ->
            new DepartmentDto(priceRecommendation.getDepartmentId(),
                priceRecommendation.getDepartment()))
        .distinct()
        .toList();
  }
}
