package com.mercaso.data.metrics.controller;

import com.mercaso.data.dto.CustomPage;
import com.mercaso.data.dto.CustomPageable;
import com.mercaso.data.metrics.dto.SearchStoreAddressDto;
import com.mercaso.data.metrics.dto.StoreAddressFilter;
import com.mercaso.data.metrics.dto.StoreProfilingDto;
import com.mercaso.data.metrics.dto.StoreProfilingStoreInfoDto;
import com.mercaso.data.metrics.service.StoreProfilingService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/metrics/store-profiling")
@RequiredArgsConstructor
public class StoreProfilingController {

    private final StoreProfilingService storeProfilingService;


    @PreAuthorize("hasAnyAuthority('ds:read:metrics')")
    @GetMapping("/v1/addresses")
    public ResponseEntity<CustomPage<SearchStoreAddressDto>> searchStoreProfilingAddresses(@RequestParam String keyword,
        @RequestParam(defaultValue = "1") int pageNumber, @RequestParam(defaultValue = "20") int pageSize) {

        StoreAddressFilter filter = new StoreAddressFilter();
        filter.setKeyword(keyword);
        filter.setPageable(new CustomPageable(pageNumber - 1, pageSize));

        Page<SearchStoreAddressDto> page = storeProfilingService.searchStoreAddressesForStoreProfiling(filter);
        CustomPage<SearchStoreAddressDto> customPage = new CustomPage<SearchStoreAddressDto>().build(page);

        return ResponseEntity.ok(customPage);
    }

    @PreAuthorize("hasAnyAuthority('ds:read:metrics')")
    @GetMapping("/{storeId}")
    public ResponseEntity<StoreProfilingDto> getStoreProfilingByStoreId(@PathVariable(value = "storeId") String storeId) {

        return ResponseEntity.ok(storeProfilingService.getStoreProfilingByStoreId(storeId));
    }

    @PreAuthorize("hasAnyAuthority('ds:read:metrics')")
    @GetMapping("/search/stores")
    public ResponseEntity<CustomPage<StoreProfilingStoreInfoDto>> searchStoreByTags(String tags, String match,
        @RequestParam(name = "page", defaultValue = "1") int pageNumber, @RequestParam(defaultValue = "20") int pageSize) {

        StoreAddressFilter filter = new StoreAddressFilter();
        filter.setTags(tags);
        filter.setMatch(match);
        filter.setPageable(new CustomPageable(pageNumber - 1, pageSize));

        Page<StoreProfilingStoreInfoDto> page = storeProfilingService.searchStoreByTags(filter);
        CustomPage<StoreProfilingStoreInfoDto> customPage = new CustomPage<StoreProfilingStoreInfoDto>().build(page);
        return ResponseEntity.ok(customPage);
    }

    @PreAuthorize("hasAnyAuthority('ds:read:metrics')")
    @GetMapping("/tags")
    public ResponseEntity<List<String>> getStoreTags() {
        List<String> tags = storeProfilingService.getStoreTags();
        return ResponseEntity.ok(tags);
    }
}
