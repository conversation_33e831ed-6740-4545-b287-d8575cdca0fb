package com.mercaso.data.master_catalog.service;

import com.mercaso.data.master_catalog.dto.MasterCatalogPotentiallyDuplicateRawDataDto;
import com.mercaso.data.master_catalog.dto.MasterCatalogPotentiallyDuplicateRawDataUpdateRequest;
import com.mercaso.data.master_catalog.dto.PotentiallyDuplicateRawDataRequest;
import com.mercaso.data.master_catalog.dto.PotentiallyDuplicateSubmitRequest;
import com.mercaso.data.master_catalog.enums.PotentiallyDuplicateRawDataStatus;
import jakarta.validation.Valid;
import java.util.List;
import java.util.UUID;
import org.springframework.data.domain.Page;

public interface MasterCatalogPotentiallyDuplicateRawDataService {

    Page<MasterCatalogPotentiallyDuplicateRawDataDto> list(PotentiallyDuplicateRawDataRequest request);

    void submit(@Valid PotentiallyDuplicateSubmitRequest potentiallyDuplicateSubmitRequest);

    MasterCatalogPotentiallyDuplicateRawDataDto update(UUID id, MasterCatalogPotentiallyDuplicateRawDataUpdateRequest request);

    List<MasterCatalogPotentiallyDuplicateRawDataDto> processPotentiallyDuplicateItem(List<List<UUID>> rawDataIds, UUID taskId,
        PotentiallyDuplicateRawDataStatus status);
}