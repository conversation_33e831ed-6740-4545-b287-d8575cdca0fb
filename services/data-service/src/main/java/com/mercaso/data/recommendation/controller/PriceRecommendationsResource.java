package com.mercaso.data.recommendation.controller;

import com.mercaso.data.recommendation.dto.DepartmentDto;
import com.mercaso.data.recommendation.dto.PageableResponse;
import com.mercaso.data.recommendation.dto.PriceRecommendationDto;
import com.mercaso.data.recommendation.dto.SearchPriceDto;
import com.mercaso.data.recommendation.service.PriceRecommendationService;
import jakarta.annotation.PostConstruct;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ClassPathResource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/recommendation/v1/price-recommendations")
public class PriceRecommendationsResource {

  private final PriceRecommendationService priceRecommendationService;
  private final Environment environment;

  private final List<PriceRecommendationDto> csvRecommendations = new ArrayList<>();

  @PostConstruct
  public void init() {
    loadCsvData();
  }

  @GetMapping("/departments")
  @PreAuthorize("hasAnyAuthority('recommendation:read:recommendations')")
  public List<DepartmentDto> getDepartments(@RequestParam("storeId") String storeId) {
    log.info("Get departments for store: {}", storeId);

    if (shouldUseCsvData(storeId)) {
      log.info("Using CSV data for departments (dev profile + test store ID)");
      return csvRecommendations.stream()
          .map(dto -> new DepartmentDto(dto.getDepartmentId(), dto.getDepartment()))
          .distinct()
          .collect(Collectors.toList());
    }

    return priceRecommendationService.findDepartmentsByStoreId(storeId);
  }

  @GetMapping("/search")
  @PreAuthorize("hasAnyAuthority('recommendation:read:recommendations')")
  public PageableResponse<PriceRecommendationDto> search(
      @RequestParam(defaultValue = "0") Integer pageNumber,
      @RequestParam(defaultValue = "20") Integer pageSize,
      @RequestParam String storeId,
      @RequestParam(required = false) String departmentId,
      @RequestParam(required = false) String searchText,
      @RequestParam(required = false, defaultValue = "false") Boolean latestOrder) {

    log.info("Search price recommendations for store: {}, departmentId: {}, searchText: {}",
        storeId, departmentId, searchText);

    if (shouldUseCsvData(storeId)) {
      log.info("Using CSV data for search (dev profile + test store ID)");
      return searchCsvData(storeId, departmentId, searchText, pageNumber, pageSize);
    }

    return priceRecommendationService.search(SearchPriceDto.builder()
        .storeId(storeId)
        .departmentId(departmentId)
        .searchText(searchText)
        .latestOrder(latestOrder)
        .build(), pageNumber, pageSize);
  }

  private PageableResponse<PriceRecommendationDto> searchCsvData(
      String storeId, String departmentId, String searchText, Integer pageNumber,
      Integer pageSize) {

    List<PriceRecommendationDto> filteredData = csvRecommendations.stream()
        .filter(dto -> {
          if (departmentId != null && !departmentId.isBlank()) {
            return departmentId.equals(dto.getDepartmentId());
          }
          return true;
        })
        .filter(dto -> {
          if (searchText != null && !searchText.isBlank()) {
            String text = searchText.toLowerCase();
            return dto.getName().toLowerCase().contains(text);
          }
          return true;
        })
        .collect(Collectors.toList());

    int start = pageNumber * pageSize;
    int end = Math.min(start + pageSize, filteredData.size());

    List<PriceRecommendationDto> pagedData = filteredData.subList(
        Math.min(start, filteredData.size()),
        Math.min(end, filteredData.size())
    );

    return PageableResponse.<PriceRecommendationDto>builder()
        .data(pagedData)
        .pageNumber(pageNumber)
        .pageSize(pageSize)
        .totalPages((int) Math.ceil((double) filteredData.size() / pageSize))
        .totalElements(filteredData.size())
        .build();
  }

  private void loadCsvData() {
    try {
      BufferedReader reader = new BufferedReader(
          new InputStreamReader(
              new ClassPathResource("mock_price_recommendation.csv").getInputStream()));

      // Skip header
      String line = reader.readLine();

      while ((line = reader.readLine()) != null) {
        String[] data = line.split(",(?=([^\"]*\"[^\"]*\")*[^\"]*$)");
        if (data.length < 13) {
          log.warn("Invalid CSV line: {}", line);
          continue;
        }

        try {
          PriceRecommendationDto dto = PriceRecommendationDto.builder()
              .productId(cleanField(data[1]))
              .sku(cleanField(data[2]))
              .name(cleanField(data[3]))
              .upc(cleanField(data[4]))
              .departmentId(cleanField(data[5]))
              .department(cleanField(data[6]))
              .lastPurchasePrice(new BigDecimal(cleanField(data[7])))
              .highPrice(new BigDecimal(cleanField(data[8])))
              .lowPrice(new BigDecimal(cleanField(data[9])))
              .avgPrice(new BigDecimal(cleanField(data[10])))
              .build();
          csvRecommendations.add(dto);
        } catch (Exception e) {
          log.error("Error parsing line: {}", line, e);
        }
      }

      log.info("Loaded {} price recommendations from CSV", csvRecommendations.size());
    } catch (IOException e) {
      log.error("Failed to load CSV data", e);
    }
  }

  private String cleanField(String field) {
    if (field == null) {
      return null;
    }
    return field.trim().replaceAll("^\"|\"$", "");
  }

  private boolean isDevProfile() {
    return Arrays.asList(environment.getActiveProfiles()).contains("dev");
  }

  private boolean shouldUseCsvData(String storeId) {
    return isDevProfile() && storeId.equals("78d259cf-1399-4e9c-9960-31c7667cd975");
  }
}
