package com.mercaso.data.master_catalog.service.impl;

import com.mercaso.data.master_catalog.constants.ProductGenerationConstants;
import com.mercaso.data.master_catalog.dto.MasterCatalogPotentiallyDuplicateRawDataDto;
import com.mercaso.data.master_catalog.dto.MasterCatalogPotentiallyDuplicateRawDataUpdateRequest;
import com.mercaso.data.master_catalog.dto.PotentiallyDuplicateRawDataRequest;
import com.mercaso.data.master_catalog.dto.PotentiallyDuplicateSubmitRequest;
import com.mercaso.data.master_catalog.entity.MasterCatalogPotentiallyDuplicateRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogProductGenerationTask;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import com.mercaso.data.master_catalog.enums.PotentiallyDuplicateRawDataStatus;
import com.mercaso.data.master_catalog.enums.ProductGenerationTaskStatus;
import com.mercaso.data.master_catalog.event.applicationevent.publisher.ApplicationEventPublisherProvider;
import com.mercaso.data.master_catalog.mapper.MasterCatalogPotentiallyDuplicateRawDataMapper;
import com.mercaso.data.master_catalog.repository.MasterCatalogPotentiallyDuplicateRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogProductGenerationTaskRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.service.MasterCatalogImageService;
import com.mercaso.data.master_catalog.service.MasterCatalogPotentiallyDuplicateRawDataService;
import com.mercaso.data.master_catalog.service.MasterCatalogRawDataService;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.ObjectNotFoundException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class MasterCatalogPotentiallyDuplicateRawDataServiceImpl implements MasterCatalogPotentiallyDuplicateRawDataService {

    private final MasterCatalogPotentiallyDuplicateRawDataRepository masterCatalogPotentiallyDuplicateRawDataRepository;
    private final MasterCatalogProductGenerationTaskRepository masterCatalogProductGenerationTaskRepository;
    private final MasterCatalogPotentiallyDuplicateRawDataMapper masterCatalogPotentiallyDuplicateRawDataMapper;
    private final MasterCatalogRawDataService masterCatalogRawDataService;
    private final ApplicationEventPublisherProvider applicationEventPublisherProvider;
    private final MasterCatalogRawDataRepository masterCatalogRawDataRepository;
    private final MasterCatalogImageService masterCatalogImageService;

    @Override
    public Page<MasterCatalogPotentiallyDuplicateRawDataDto> list(PotentiallyDuplicateRawDataRequest request) {
        Page<MasterCatalogPotentiallyDuplicateRawDataDto> masterCatalogPotentiallyDuplicateRawDataDtos = masterCatalogProductGenerationTaskRepository.findAllByStatusIn(
                ProductGenerationConstants.IN_PROGRESS_STATUSES)
            .stream()
            .findFirst()
            .map(task -> findPendingDuplicates(request))
            .orElse(Page.empty());

        List<UUID> rawDataIds = new ArrayList<>(masterCatalogPotentiallyDuplicateRawDataDtos.getContent()
            .stream()
            .map(MasterCatalogPotentiallyDuplicateRawDataDto::getPotentiallyDuplicateRawDataId)
            .toList());

        rawDataIds.addAll(masterCatalogPotentiallyDuplicateRawDataDtos.getContent()
            .stream()
            .map(MasterCatalogPotentiallyDuplicateRawDataDto::getRawDataId)
            .toList());

        Map<UUID, List<String>> imagesPathByRawDataIds = masterCatalogImageService.getImagesPathByRawDataIds(
            rawDataIds.stream().distinct().toList());

        masterCatalogPotentiallyDuplicateRawDataDtos.forEach(masterCatalogPotentiallyDuplicateRawDataDto -> {
            if (masterCatalogPotentiallyDuplicateRawDataDto.getRawDataId() != null) {
                masterCatalogPotentiallyDuplicateRawDataDto.setImages(imagesPathByRawDataIds.get(
                    masterCatalogPotentiallyDuplicateRawDataDto.getRawDataId()));
            }

            if (masterCatalogPotentiallyDuplicateRawDataDto.getPotentiallyDuplicateRawDataId() != null) {
                masterCatalogPotentiallyDuplicateRawDataDto.setPotentiallyDuplicateImages(imagesPathByRawDataIds.get(
                    masterCatalogPotentiallyDuplicateRawDataDto.getPotentiallyDuplicateRawDataId()));
            }
        });

        return masterCatalogPotentiallyDuplicateRawDataDtos;
    }

    @Transactional
    @Override
    public void submit(PotentiallyDuplicateSubmitRequest potentiallyDuplicateSubmitRequest) {
        List<UUID> potentiallyDuplicateRawDataIds = potentiallyDuplicateSubmitRequest.getPotentiallyDuplicateRawDataIds();
        log.info("Submitting potentially duplicate raw data with ids: {}", potentiallyDuplicateRawDataIds);

        // Fetch data to be submitted
        List<MasterCatalogPotentiallyDuplicateRawData> waitingSubmit = masterCatalogPotentiallyDuplicateRawDataRepository
            .findAllById(potentiallyDuplicateRawDataIds);
        log.info("Submitting potentially duplicate, found {} potentially duplicate raw data", waitingSubmit.size());

        if (CollectionUtils.isEmpty(waitingSubmit)) {
            log.info("Submitting potentially duplicate, no data to submit");
            return;
        }

        // Filter data with IN_STAGE status
        List<MasterCatalogPotentiallyDuplicateRawData> validToSubmit = filterValidToSubmitData(waitingSubmit);
        log.info("Submitting potentially duplicate, found {} valid data to submit", validToSubmit.size());

        if (CollectionUtils.isEmpty(validToSubmit)) {
            log.info("Submitting potentially duplicate, no valid data to submit because of the status is not in stage.");
            return;
        }

        // Get task ID and process
        validToSubmit.stream()
            .map(MasterCatalogPotentiallyDuplicateRawData::getTaskId)
            .findFirst()
            .flatMap(this::getAndValidateTask)
            .ifPresent(task -> processSubmitByTaskStatus(task, validToSubmit));
    }

    private List<MasterCatalogPotentiallyDuplicateRawData> filterValidToSubmitData(
        List<MasterCatalogPotentiallyDuplicateRawData> waitingSubmit) {

        return waitingSubmit.stream()
            .filter(data -> data.getStatus() == PotentiallyDuplicateRawDataStatus.IN_STAGE)
            .toList();
    }

    /**
     * Get and validate task
     */
    private Optional<MasterCatalogProductGenerationTask> getAndValidateTask(UUID taskId) {
        return masterCatalogProductGenerationTaskRepository.findById(taskId)
            .map(task -> {
                log.info("Submitting potentially duplicate, task {} status is {}", task.getId(), task.getStatus());
                if (!ProductGenerationConstants.AVAILABLE_SUBMIT_TASK_STATUSES.contains(task.getStatus())) {
                    throw new IllegalArgumentException(
                        "Submitting potentially duplicate, task status is not in available submit task status, please confirm the data.");
                }
                return task;
            })
            .or(() -> {
                log.info("Submitting potentially duplicate, task {} not found", taskId);
                throw new IllegalArgumentException("Submitting potentially duplicate, task not found, please confirm the data.");
            });
    }

    /**
     * Process submission based on task status
     */
    private void processSubmitByTaskStatus(MasterCatalogProductGenerationTask task,
        List<MasterCatalogPotentiallyDuplicateRawData> validToSubmit) {

        Map<ProductGenerationTaskStatus, StatusHandler> statusHandlers = buildProductGenerationTaskStatusHandlerMap();

        // Get the corresponding processor
        StatusHandler handler = statusHandlers.get(task.getStatus());

        validToSubmit.forEach(data -> data.setStatus(handler.targetStatus()));
        masterCatalogPotentiallyDuplicateRawDataRepository.saveAll(validToSubmit);

        markMasterCatalogRawDataAsCompleted(validToSubmit);

        handler.eventPublisher().accept(task, validToSubmit);
    }

    private Map<ProductGenerationTaskStatus, StatusHandler> buildProductGenerationTaskStatusHandlerMap() {

        return Map.of(
            ProductGenerationTaskStatus.REMOVE_DUPLICATION_IN_PROGRESS,
            new StatusHandler(
                PotentiallyDuplicateRawDataStatus.FIRST_ROUND_REVIEWED,
                applicationEventPublisherProvider::removeDuplicationSubmitEvent
            ),
            ProductGenerationTaskStatus.GENERATE_PRODUCTS_IN_PROGRESS,
            new StatusHandler(
                PotentiallyDuplicateRawDataStatus.SECOND_ROUND_REVIEWED,
                applicationEventPublisherProvider::generateProductsSubmitEvent
            )
        );
    }

    // Define the mapping of task statuses and their corresponding handling
    private record StatusHandler(
        PotentiallyDuplicateRawDataStatus targetStatus,
        BiConsumer<MasterCatalogProductGenerationTask, List<MasterCatalogPotentiallyDuplicateRawData>> eventPublisher
    ) {

    }

    private void markMasterCatalogRawDataAsCompleted(List<MasterCatalogPotentiallyDuplicateRawData> validToSubmit) {
        Set<UUID> potentiallyDuplicateRawDataIds = validToSubmit.stream()
            .filter(data -> Boolean.TRUE.equals(data.getDuplicated()))
            .map(MasterCatalogPotentiallyDuplicateRawData::getPotentiallyDuplicateRawDataId)
            .collect(Collectors.toSet());
        masterCatalogRawDataService.markAsCompleted(potentiallyDuplicateRawDataIds);
    }

    @Override
    public MasterCatalogPotentiallyDuplicateRawDataDto update(UUID id,
        MasterCatalogPotentiallyDuplicateRawDataUpdateRequest request) {

        // Find data and verify existence
        MasterCatalogPotentiallyDuplicateRawData potentiallyDuplicateRawData = masterCatalogPotentiallyDuplicateRawDataRepository
            .findById(id)
            .orElseThrow(() -> new ObjectNotFoundException(id, MasterCatalogPotentiallyDuplicateRawData.class.getName()));

        // Validate data status
        validateDataStatus(potentiallyDuplicateRawData);

        // Validate task status
        validateTaskStatus(potentiallyDuplicateRawData.getTaskId());

        // Update data
        if (request.getDuplicated() != null) {
            potentiallyDuplicateRawData.setDuplicated(request.getDuplicated());
            potentiallyDuplicateRawData.setStatus(PotentiallyDuplicateRawDataStatus.IN_STAGE);
            // Save and return result
            MasterCatalogPotentiallyDuplicateRawData saved = masterCatalogPotentiallyDuplicateRawDataRepository.save(
                potentiallyDuplicateRawData);

            return masterCatalogPotentiallyDuplicateRawDataMapper.toDto(saved);
        }

        return masterCatalogPotentiallyDuplicateRawDataMapper.toDto(potentiallyDuplicateRawData);
    }

    // Validate data status
    private void validateDataStatus(MasterCatalogPotentiallyDuplicateRawData data) {
        if (data.getStatus() == PotentiallyDuplicateRawDataStatus.FIRST_ROUND_REVIEWED) {
            throw new IllegalArgumentException("The data is already reviewed, cannot update.");
        }
    }

    // Validate task status
    private void validateTaskStatus(UUID taskId) {
        masterCatalogProductGenerationTaskRepository.findById(taskId)
            .filter(task -> ProductGenerationConstants.AVAILABLE_SUBMIT_TASK_STATUSES.contains(task.getStatus()))
            .orElseThrow(() -> new IllegalArgumentException("The task is not in available submit task status, cannot update."));
    }

    private Page<MasterCatalogPotentiallyDuplicateRawDataDto> findPendingDuplicates(PotentiallyDuplicateRawDataRequest request) {

        PageRequest pageRequest = PageRequest.of(request.getPage() - 1, request.getPageSize(),
            Sort.by(Direction.DESC, "createdAt"));

        List<PotentiallyDuplicateRawDataStatus> status =
            StringUtils.isEmpty(request.getStatus()) ? ProductGenerationConstants.PENDING_STATUSES
                : List.of(PotentiallyDuplicateRawDataStatus.valueOf(request.getStatus()));

        return masterCatalogPotentiallyDuplicateRawDataRepository.findByStatusIsIn(status, pageRequest)
            .map(masterCatalogPotentiallyDuplicateRawDataMapper::toDto);
    }

    public List<MasterCatalogPotentiallyDuplicateRawDataDto> processPotentiallyDuplicateItem(List<List<UUID>> rawDataIds,
        UUID taskId,
        PotentiallyDuplicateRawDataStatus status) {

        //Separating to two list, duplicateUUIDList contains all the potentially duplicated pair, unduplicateUUIDList contains those non duplicated UUID that do not need human review.
        List<List<UUID>> duplicateUUIDList = rawDataIds.stream()
            .filter(list -> list.size() == 2)
            .toList();

        List<UUID> unduplicateUUIDList = rawDataIds.stream()
            .filter(list -> list.size() == 1)
            .flatMap(List::stream)
            .toList();

        // Retrieve raw data accordingly and convert raw data to potentiallyDuplicateRawData
        List<MasterCatalogPotentiallyDuplicateRawData> pendingSaveDuplicateRawDataList = new ArrayList<>(
            generateDuplicateRawData(
                unduplicateUUIDList, taskId, status));

        duplicateUUIDList.stream()
            .map(duplicateGroup -> buildPotentiallyDuplicateRawData(
                duplicateGroup, taskId))
            .filter(Objects::nonNull)
            .forEach(pendingSaveDuplicateRawDataList::add);

        masterCatalogPotentiallyDuplicateRawDataRepository.saveAll(pendingSaveDuplicateRawDataList);

        return pendingSaveDuplicateRawDataList.stream()
            .map(masterCatalogPotentiallyDuplicateRawDataMapper::toDto)
            .toList();
    }

    private List<MasterCatalogPotentiallyDuplicateRawData> generateDuplicateRawData(
        List<UUID> unduplicatedUUIDList, UUID taskId, PotentiallyDuplicateRawDataStatus status) {
        //Retrieve raw data
        List<MasterCatalogRawData> rawDataList = masterCatalogRawDataRepository.findAllById(
            unduplicatedUUIDList);

        //Mapping raw data to MasterCatalogPotentiallyDuplicateRawData
        List<MasterCatalogPotentiallyDuplicateRawData> masterCatalogPotentiallyDuplicateRawDataList = rawDataList.stream()
            .map(
                rawData -> masterCatalogPotentiallyDuplicateRawDataMapper.mapRawDataToPotentiallyUnDuplicateRawData(
                    UUID.randomUUID(),
                    rawData, taskId, status)).toList();
        //Setting time
        masterCatalogPotentiallyDuplicateRawDataList.stream().filter(Objects::nonNull).forEach(
            masterCatalogPotentiallyDuplicateRawData -> {
                masterCatalogPotentiallyDuplicateRawData.setCreatedAt(
                    Instant.now());
                masterCatalogPotentiallyDuplicateRawData.setUpdatedAt(Instant.now());
            });
        return masterCatalogPotentiallyDuplicateRawDataList;
    }

    private MasterCatalogPotentiallyDuplicateRawData buildPotentiallyDuplicateRawData(
        List<UUID> duplicatedUUIDList, UUID taskId) {

        Optional<MasterCatalogRawData> productOptional = masterCatalogRawDataRepository.findById(
            duplicatedUUIDList.getFirst());
        Optional<MasterCatalogRawData> potentialDuplicateOptional = masterCatalogRawDataRepository.findById(
            duplicatedUUIDList.getLast());

        //if cannot find by id
        if (productOptional.isEmpty()
            || potentialDuplicateOptional.isEmpty()) {
            log.error("Generate Potentially Duplicate Raw Data Error, taskId: {}, for UUIDs: {}",
                taskId, duplicatedUUIDList);
            return null;
        }

        MasterCatalogRawData product = productOptional.get();
        MasterCatalogRawData potentialDuplicateRawData = potentialDuplicateOptional.get();

        //Mapping raw data to MasterCatalogPotentiallyDuplicateRawData
        MasterCatalogPotentiallyDuplicateRawData masterCatalogPotentiallyDuplicateRawData = masterCatalogPotentiallyDuplicateRawDataMapper.mapRawDataToPotentialDuplicateRawData(
            UUID.randomUUID(), taskId, product, potentialDuplicateRawData,
            PotentiallyDuplicateRawDataStatus.PENDING_REVIEW);
        //Setting time
        masterCatalogPotentiallyDuplicateRawData.setCreatedAt(Instant.now());
        masterCatalogPotentiallyDuplicateRawData.setUpdatedAt(Instant.now());
        return masterCatalogPotentiallyDuplicateRawData;
    }
}
